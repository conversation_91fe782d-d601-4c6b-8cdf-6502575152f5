apiVersion: apps/v1
kind: Deployment
metadata:
  name: basic-system
  namespace: {{namespace}}
spec:
  replicas: 1  
  minReadySeconds: 1 # 容器启动创建多少后服务可用
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 2 # 升级过程中最多可以比原先设置多出的POD数量
      maxUnavailable: 0 # 升级过程中最多有多少个POD处于无法提供服务的状态
  selector:
    matchLabels:
      app: basic-system
  template:
    metadata:
      labels:
        app: basic-system
    spec:
      containers:
        - name: basic-system
          image: swr.cn-south-1.myhuaweicloud.com/hishop/basic-system:{{docker_image_tag}}
          imagePullPolicy: Always
          envFrom:
            - configMapRef:
                name: basic-system-cm # configmap配置
          ports:
            - containerPort: 80
          resources:
            limits:
              cpu: 500m
              memory: 1Gi
            requests:
              cpu: 250m
              memory: 256Mi
          livenessProbe: # 存活检测
            httpGet:
              path: /wine/health
              port: 80 
            initialDelaySeconds: 20
            timeoutSeconds: 10
            successThreshold: 1
            failureThreshold: 5  
          readinessProbe: # 就绪检测
            httpGet:
              path: /wine/health
              port: 80 
            initialDelaySeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 5
      imagePullSecrets:
        - name: dockerregistry
        - name: default-secret

---
apiVersion: v1
kind: Service
metadata:
  name: basic-system
  labels:
    app: basic-system
  namespace: {{namespace}}
spec:
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
  selector:
    app: basic-system

---
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  annotations:
    nginx.ingress.kubernetes.io/cors-allow-headers: 'Referer,Accept,Accept-Encoding,Accept-Language,Origin,User-Agent,DNT,Cache-Control,Content-Type,X-Requested-With,req-host,Authorization,moduleCode,Content-Disposition,feignFlag,traceId'
    nginx.ingress.kubernetes.io/cors-allow-methods: 'PUT, GET, POST, OPTIONS'
    nginx.ingress.kubernetes.io/cors-expose-headers: 'moduleCode,Content-Disposition,feignFlag,traceId'
    nginx.ingress.kubernetes.io/cors-allow-origin: '*'
    nginx.ingress.kubernetes.io/enable-cors: 'true'
    nginx.ingress.kubernetes.io/cors-max-age: '9999'
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-connect-timeout: '600'
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
  name: basic-system
  namespace: {{namespace}}
spec:
  tls:
    - hosts:
        - {{domainurl}}
      secretName: basic-system
  rules:
    - host: {{domainurl}}
      http:
        paths:
          - path: /
            backend:
              serviceName: basic-system
              servicePort: 80
