package com.hishop.wine.model.po.material;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "MaterialCreatePO", description = "图片库新增入参对象")
public class ImageCreatePO {
    @ApiModelProperty(value = "资源分组id", required = true)
    @NotNull(message = "资源分组id不能为空")
    private Long materialCategoryId;

    @ApiModelProperty(value = "资源标题(带扩展名)", required = true)
    @NotBlank(message = "资源标题不能为空")
    private String title;

    @ApiModelProperty(value = "图片路径（相对路径）",required = true)
    private String path;
    @ApiModelProperty(value = "图片大小（b）")
    private Long fileSize;
}
