package com.hishop.wine.model.vo.logistics;

import cn.hutool.core.util.StrUtil;
import com.hishop.common.annotation.Desensitized;
import com.hishop.common.enums.SensitiveTypeEnum;
import com.hishop.setting.AbstractSetting;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**   
 * 物流配置返回对象
 * @author: chenpeng
 * @date: 2023-07-19
 */

@Data
@ApiModel(value = "LogisticsVO", description = "物流配置")
public class LogisticsSettingVO extends AbstractSetting implements Serializable {

    private static final long serialVersionUID = 1L;
	
    @ApiModelProperty("物流平台appKey")
    @Desensitized(type = SensitiveTypeEnum.ALL)
    private String appKey;

    @ApiModelProperty("物流平台appSecret")
    @Desensitized(type = SensitiveTypeEnum.ALL)
    private String appSecret;

    @Override
    protected void initDefault() {
        this.setAppKey(StrUtil.EMPTY);
        this.setAppSecret(StrUtil.EMPTY);
    }

}
