package com.hishop.wine.model.po.material;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 素材库分组 更新入参对象
 *
 * @author: HuBiao
 * @date: 2023-06-20
 */
@Data
@ApiModel(value = "MaterialCategoryUpdatePO", description = "素材库分组更新入参对象")
public class MaterialCategoryUpdatePO {

    @ApiModelProperty(value = "主键id", required = true)
    @NotNull(message = "主键id不能为空")
    private Long id;

    @ApiModelProperty(value = "父级id", required = true)
    @NotNull(message = "父级id不能为空")
    private Long parentId;

    @ApiModelProperty(value = "分组名称", required = true)
    @NotBlank(message = "分组名称不能为空")
    private String name;
}
