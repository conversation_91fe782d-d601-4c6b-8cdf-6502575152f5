package com.hishop.wine.constants;

/**
 * 交易回调topic
 *
 * <AUTHOR>
 * @date : 2023/8/4
 */
public interface TransactionMqConstants {

    /**
     * 积分商城支付回调
     */
    String POINTS_ORDER_PAY_SUCCESS_TOPIC = "POINTS_ORDER_PAY_SUCCESS";

    /**
     * 积分订单退款回调
     */
    String POINTS_ORDER_REFUND_SUCCESS_TOPIC = "POINTS_ORDER_REFUND_SUCCESS";

    /**
     * 扫码营销支付回调
     */
    String SCAN_ORDER_PAY_SUCCESS_TOPIC = "SCAN_ORDER_PAY_SUCCESS";

    /**
     * 扫码营销退款回调
     */
    String SCAN_ORDER_REFUND_SUCCESS_TOPIC = "SCAN_ORDER_REFUND_SUCCESS";

    /**
     * 企业打款回调
     */
    String ENTRY_PAY_SUCCESS_TOPIC = "ENTRY_PAY_SUCCESS";

    /**
     * 封坛酒订单支付回调
     */
    String FENG_TAN_WINE_PAY_SUCCESS_TOPIC = "FENG_TAN_WINE_PAY_SUCCESS";

    /**
     * 封坛酒保管费支付回调
     */
    String WINE_STORAGE_FEE_BILL_PAY_SUCCESS_TOPIC = "WINE_STORAGE_FEE_BILL_PAY_SUCCESS";

    /**
     * 酒票保管费支付回调
     */
    String TICKET_STORAGE_FEE_BILL_PAY_SUCCESS_TOPIC = "TICKET_STORAGE_FEE_BILL_PAY_SUCCESS";

    /**
     * 取酒订单支付回调
     */
    String TAKE_WINE_ORDER_PAY_SUCCESS_TOPIC = "TAKE_WINE_ORDER_PAY_SUCCESS";

    /**
     * 定制酒订单支付回调
     */
    String CUSTOM_WINE_PAY_SUCCESS_TOPIC = "CUSTOM_WINE_PAY_SUCCESS";

    /**
     * 取酒订单退款回调
     */
    String TAKE_WINE_ORDER_PAY_REFUND_TOPIC = "TAKE_WINE_ORDER_REFUND_SUCCESS";

    /**
     * 取酒订单通联支付回调
     */
    String TAKE_WINE_ORDER_PAY_TL_SUCCESS_TOPIC = "TAKE_WINE_ORDER_PAY_TL_SUCCESS";

    /**
     * 取酒订单通联退款回调
     */
    String TAKE_WINE_ORDER_PAY_REFUND_TL_TOPIC = "TAKE_WINE_ORDER_PAY_REFUND_TL_TOPIC";
}
