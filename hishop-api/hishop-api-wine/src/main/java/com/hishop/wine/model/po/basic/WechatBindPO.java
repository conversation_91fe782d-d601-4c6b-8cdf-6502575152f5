package com.hishop.wine.model.po.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 微信网页扫码参数
 *
 * <AUTHOR>
 * @date : 2023/6/16
 */
@Data
@ApiModel(value = "WechatBindPO", description = "微信绑定参数")
public class WechatBindPO {

    @ApiModelProperty(value = "微信code", required = true)
    @NotBlank(message = "请传入微信code")
    private String code;

    @ApiModelProperty(value = "回调参数", required = true)
    @NotBlank(message = "请传入回调参数")
    private String state;

}
