package com.hishop.wine.model.po.member;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hishop.common.pojo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.LinkedHashMap;

/**
 * 会员查询入参
 *
 * <AUTHOR>
 * @date : 2023/7/25
 */
@Data
@ApiModel(value = "MemberQueryPO", description = "会员查询入参")
public class MemberQueryPO extends PageParam {

    @ApiModelProperty(value = "注册开始时间。yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "注册结束时间。yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty("会员信息")
    private String memberSearchKey;

    @ApiModelProperty("会员头衔id")
    private Long rankId;

    @ApiModelProperty(value = "排序字段。key：字段名，value：true-升序，false-降序 (注册时间-registerTime)")
    private LinkedHashMap<String, Boolean> sortField;

}
