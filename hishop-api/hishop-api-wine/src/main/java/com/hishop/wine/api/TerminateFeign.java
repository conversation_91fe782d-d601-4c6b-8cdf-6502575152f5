package com.hishop.wine.api;

import com.hishop.common.pojo.IdBatchPO;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.vo.terminate.TerminateFeignDetailVo;
import com.hishop.wine.model.vo.terminate.TerminateFeignVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * @description: 门店feign
 * @author: chenzw
 * @date: 2024/7/10 10:04
 */
@FeignClient(name = "basic-system", contextId = "hishop-wine-terminate", url = "${feign.url.basic-system:basic-system}", path = "/wine/terminate")
public interface TerminateFeign {

    /**
     * 门店列表
     *
     * @param idBatchPo 查询参数
     * @return 门店列表
     */
    @PostMapping("/queryTerminateList")
    ResponseBean<List<TerminateFeignVo>> queryTerminateList(@RequestBody @Valid IdBatchPO<Long> idBatchPo);

    /**
     * 门店列表
     *
     * @param idBatchPo 查询参数
     * @return 门店列表
     */
    @PostMapping("/queryTerminateByCodeList")
    ResponseBean<List<TerminateFeignVo>> queryTerminateByCodeList(@RequestBody @Valid IdBatchPO<String> idBatchPo);

    /**
     * 门店详情
     *
     * @param id 查询参数
     * @return 门店详情
     */
    @GetMapping("/feign/detail")
    ResponseBean<TerminateFeignDetailVo> feignDetail(@RequestParam(name = "id") Long id);

    /**
     * 查询user是否是业务
     * @param userId 用户id
     * @param moduleCode 模块编码
     * @return 是否是业务员
     */
    @GetMapping("/feign/queryUserIsSalesman")
    ResponseBean<Boolean> queryUserIsSalesman(@RequestParam(name = "userId") Long userId, @RequestParam(name = "moduleCode") String moduleCode);

    /**
     * 根据电话号码查询门店
     * @param phone 手机号码
     * @return 门店信息
     */
    @GetMapping("/feign/queryTerminateByPhone")
    ResponseBean<TerminateFeignDetailVo> queryTerminateByPhone(@RequestParam(name = "phone") String phone);

    /**
     * 根据业务员id查询门店id集合
     * @param businessUserId
     * @return 门店id集合
     */
    @GetMapping("/feign/listByBusinessUserId")
    ResponseBean<List<Long>> listByBusinessUserId(@RequestParam(name = "businessUserId") Long businessUserId);
}
