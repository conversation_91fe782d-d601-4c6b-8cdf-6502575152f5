package com.hishop.wine.model.po.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户入参
 *
 * <AUTHOR>
 * @date : 2023/9/7
 */
@Data
@ApiModel(value = "UserPO", description = "用户入参")
public class UserPO {

    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "用户身份类型。积分根据身份类型隔离")
    private Integer identityType;
}
