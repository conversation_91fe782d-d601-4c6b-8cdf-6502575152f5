package com.hishop.wine.model.po.minUser;

import cn.hutool.core.lang.Assert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hishop.common.pojo.SortPO;
import com.hishop.common.pojo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 小程序用户黑名单查询入参
 * <AUTHOR>
 * @since 2024-01-27 08:06:07
 */
@Data
@ApiModel(value = "MiniUserBlacklistQueryPo", description = "小程序用户黑名单查询入参")
public class MiniUserBlacklistQueryPo extends PageParam {
    
    @ApiModelProperty(value = "客户名称")
    private String nickName;
    
    @ApiModelProperty(value = "客户手机")
    private String mobile;

    @ApiModelProperty(value = "下单开始时间。yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "下单结束时间。yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty("排序字段")
    private List<SortPO> sortList;

    @ApiModelProperty(value = "排序sql", hidden = true)
    private String sortSql;

    public void validateParam() {
        Assert.isTrue(this.getPageNo() != null && this.getPageSize() != null, "查询请求分页参数不能为空");
    }
}
