package com.hishop.wine.model.vo.terminate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: 门店信息返回对象
 * @author: chenzw
 * @date: 2024/7/10 10:00
 */
@Data
@ApiModel(value = "TerminateFeignVo", description = "门店信息返回对象")
public class TerminateFeignVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "门店id")
    private Long id;

    @ApiModelProperty(value = "门店名称")
    private String name;

    @ApiModelProperty(value = "门店编码")
    private String code;
}
