package com.hishop.wine.api;

import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.po.member.MemberSetRankBatchPO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 会员feign接口
 *
 * @author: LiGuoQiang
 * @date: 2023-06-25
 */

@FeignClient(name = "basic-system",
        contextId = "hishop-wine-member",
        url = "${feign.url.basic-system:basic-system}",
        path = "/wine/member")
public interface MemberFeign {

    /**
     * 批量设置头衔
     *
     * @param memberSetRankBatchPO 头衔设置参数
     * @return 返回结果
     */
    @PostMapping("/pc/setRankBatch")
    ResponseBean setRankBatch(@RequestBody @Valid MemberSetRankBatchPO memberSetRankBatchPO);

}