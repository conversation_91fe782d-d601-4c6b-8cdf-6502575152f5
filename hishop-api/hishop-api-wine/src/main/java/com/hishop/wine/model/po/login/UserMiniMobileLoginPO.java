package com.hishop.wine.model.po.login;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 小程序手机号登录参数
 *
 * <AUTHOR>
 * @date : 2023/6/16
 */
@Data
@ApiModel("小程序手机号登录参数")
public class UserMiniMobileLoginPO extends UserBaseLoginPO {

    @ApiModelProperty(value = "微信code", required = true)
    @NotBlank(message = "请传入微信code")
    private String code;

    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3|4|5|6|7|8|9][0-9]\\d{8}$", message = "手机号格式不正确")
    private String mobile;

    @ApiModelProperty(value = "验证码", required = true)
    @NotBlank(message = "请传入验证码")
    private String verifyCode;

    @ApiModelProperty("邀请人")
    private Long inviterUserId;

    @ApiModelProperty("注册渠道")
    private String registerChannel;

    @ApiModelProperty("注册业务码")
    private String registerBizCode;

}
