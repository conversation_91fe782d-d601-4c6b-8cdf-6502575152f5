package com.hishop.wine.model.dto.bill;

import com.baomidou.mybatisplus.annotation.TableField;
import com.hishop.wine.enums.order.PayMethod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  对账分组
 */
@Data
@Builder
@AllArgsConstructor
@ApiModel(value = "BillGroupDto", description = "对账分组")
public class BillGroupDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "日期")
    private String dateStr;

    @ApiModelProperty(value = "支付方式 online_pay:在线支付 offline_pay:线下支付")
    private String payMethod;

    @ApiModelProperty(value = "金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "几笔")
    private Long num;

}
