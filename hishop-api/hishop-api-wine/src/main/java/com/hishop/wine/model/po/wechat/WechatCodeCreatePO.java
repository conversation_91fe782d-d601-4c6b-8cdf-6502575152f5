package com.hishop.wine.model.po.wechat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/6/26
 */
@Data
@ApiModel(value = "通用小程序二维码创建入参")
public class WechatCodeCreatePO {

    @ApiModelProperty(value = "二维码页面。小程序码必填")
    private String page;
    @ApiModelProperty("太阳码大小")
    private Integer width = 600;
    @ApiModelProperty("是否背景透明")
    private Boolean izHyaline;
    @ApiModelProperty(value = "业务路径。可选的是否根据业务进行文件夹划分，比如不同的子系统，不同子系统下的某个业务")
    private String bizPath;
    @ApiModelProperty(value = "二维码类型。1：小程序码；2：公众号二维码")
    private Integer codeType;
    @ApiModelProperty(value = "二维码来源。BASIC_SYSTEM：基础库；SCAN_MARKETING：扫码营销；FANS_CLUB：粉丝俱乐部")
    private String codeFrom;
    @ApiModelProperty(value = "二维码描述")
    private String codeDesc;
    @ApiModelProperty("小程序appId(该参数非必填)")
    private String appId;
    @ApiModelProperty("要打开的小程序版本。正式版为 \"release\"，体验版为 \"trial\"，开发版为 \"develop\"。默认是正式版。")
    private String envVersion;
}
