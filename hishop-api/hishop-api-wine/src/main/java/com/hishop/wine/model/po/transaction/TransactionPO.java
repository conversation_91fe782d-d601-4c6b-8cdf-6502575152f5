package com.hishop.wine.model.po.transaction;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/03/05/ $
 * @description:
 */
@Data
@ApiModel(value = "TransactionPO", description = "交易流水入参")
public class TransactionPO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "交易流水")
    private String transactionNo;

    @ApiModelProperty(value = "模块编码")
    private String moduleCode;

    @ApiModelProperty(value = "交易方式 WX_PAY-微信支付 WX_SEC_PAY-微信服务商支付")
    private String transactionMethod;

    @ApiModelProperty(value = "交易类型 PAY-付款 REFUND-退款")
    private String transactionType;

    @ApiModelProperty(value = "商户id")
    private String mchId;

    @ApiModelProperty(value = "app_id")
    private String appId;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "支付用户 openId")
    private String openId;

    @ApiModelProperty(value = "业务类型")
    private String bizType;

    @ApiModelProperty(value = "业务编码(用来存放订单号)")
    private String bizCode;

    @ApiModelProperty(value = "交易金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "第三方支付流水号")
    private String thirdTransactionNo;

    @ApiModelProperty(value = "状态 1-待确认 2-交易成功 3-交易失败")
    private Integer status;

    @ApiModelProperty(value = "第三方状态")
    private String thirdStatus;

    @ApiModelProperty(value = "失败原因")
    private String failReason;

    @ApiModelProperty(value = "原交易id(如果是交易类型为退款才存在)")
    private Long orgTransactionId;

    @ApiModelProperty(value = "退款状态 1-未退款 2-部分退款 3-全部退款 (只有是交易为付款时该值才有效)")
    private Integer refundStatus;

    @ApiModelProperty(value = "退款金额 (只有是交易为付款时该值才有效)")
    private BigDecimal refundAmount;

    @ApiModelProperty(value = "完成时间")
    private Date finishTime;

    @ApiModelProperty(value = "小程序appId")
    private String miniAppId;
}
