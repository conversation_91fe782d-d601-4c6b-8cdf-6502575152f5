package com.hishop.wine.model.vo.miniApp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 功能页面 返回对象
 *
 * @author: HuBiao
 * @date: 2023-07-07
 */
@Data
@ApiModel(value = "LinkPagesVO", description = "功能页面vo")
public class LinkPagesVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "页面地址")
    private String path;

}
