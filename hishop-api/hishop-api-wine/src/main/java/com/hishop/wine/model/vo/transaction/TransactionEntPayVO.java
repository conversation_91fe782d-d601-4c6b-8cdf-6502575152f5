package com.hishop.wine.model.vo.transaction;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 企业打款返回值
 *
 * <AUTHOR>
 * @date : 2023/9/19
 */
@Data
@ApiModel(value = "TransactionEntPayVO", description = "企业打款返回值")
public class TransactionEntPayVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("内部交易id")
    private Long transactionId;

    @ApiModelProperty("内部交易流水号")
    private String transactionNo;

    public static TransactionEntPayVO of(Long transactionId, String transactionNo) {
        TransactionEntPayVO vo = new TransactionEntPayVO();
        vo.setTransactionId(transactionId);
        vo.setTransactionNo(transactionNo);
        return vo;
    }

}
