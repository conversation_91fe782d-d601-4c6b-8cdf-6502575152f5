package com.hishop.wine.enums;

import cn.hutool.core.util.StrUtil;
import com.hishop.wine.constants.TransactionMqConstants;
import lombok.Getter;

/**
 * 交易枚举
 *
 * <AUTHOR>
 * @date : 2023/6/28
 */
public class TransactionEnum {

    /**
     * 支付方式枚举
     */
    @Getter
    public enum MethodEnum {
        /**
         * 微信支付
         */
        WX_PAY("微信支付"),

        /**
         * 微信服务商支付
         */
        WX_SEC_PAY("微信服务商支付")
        ;

        private final String desc;

        MethodEnum(String desc) {
            this.desc = desc;
        }

        public static String getDesc(String name) {
            for (MethodEnum methodEnum : MethodEnum.values()) {
                if (methodEnum.name().equals(name)) {
                    return methodEnum.getDesc();
                }
            }
            return StrUtil.EMPTY;
        }

        public static MethodEnum getEnum(String name) {
            for (MethodEnum methodEnum : MethodEnum.values()) {
                if (methodEnum.name().equals(name)) {
                    return methodEnum;
                }
            }
            return null;
        }
    }

    /**
     * 交易类型枚举
     */
    @Getter
    public enum Type {

        /**
         * 交易
         */
        PAY,

        /**
         * 退款
         */
        REFUND,

        /**
         * 打款
         */
        ENTRY_PAY;

    }

    /**
     * 交易类型枚举
     */
    @Getter
    public enum TradeTypeEnum {
        /**
         * APP支付
         */
        APP,
        /**
         * 小程序支付
         */
        JSAPI,
        /**
         * Native支付
         */
        NATIVE,
        /**
         * H5支付
         */
        H5;

    }

    /**
     * 业务类型枚举
     * 该枚举暂时列在一起, 需要开发人员自己注意, 不要乱指定
     */
    @Getter
    public enum BizTypeEnum {

        /**
         * 积分订单支付
         */
        POINTS_ORDER_PAY("POINTS_ORDER_PAY", ModuleEnums.fans_club.name(), Type.PAY, TransactionMqConstants.POINTS_ORDER_PAY_SUCCESS_TOPIC),

        /**
         * 积分订单退款
         */
        POINTS_ORDER_REFUND("POINTS_ORDER_REFUND", ModuleEnums.fans_club.name(), Type.REFUND, TransactionMqConstants.POINTS_ORDER_REFUND_SUCCESS_TOPIC),

        /**
         * 扫码营销订单支付
         */
        SCAN_ORDER_PAY("SCAN_ORDER_PAY", ModuleEnums.scan_marketing.name(), Type.PAY, TransactionMqConstants.SCAN_ORDER_PAY_SUCCESS_TOPIC),

        /**
         * 扫码营销订单退款
         */
        SCAN_ORDER_REFUND("SCAN_ORDER_REFUND", ModuleEnums.scan_marketing.name(), Type.REFUND, TransactionMqConstants.SCAN_ORDER_REFUND_SUCCESS_TOPIC),

        /**
         * 扫码营销发红包
         */
        SCAN_GAVE_RED_PACKAGE("SCAN_GAVE_RED_PACKAGE", ModuleEnums.scan_marketing.name(), Type.ENTRY_PAY, TransactionMqConstants.ENTRY_PAY_SUCCESS_TOPIC),

        /**
         * 封坛酒订单支付
         */
        FENG_TAN_WINE_ORDER_PAY("FENG_TAN_WINE_ORDER_PAY", ModuleEnums.fengtan_wine.name(), Type.PAY, TransactionMqConstants.FENG_TAN_WINE_PAY_SUCCESS_TOPIC),

        /**
         * 封坛酒保管费支付
         */
        WINE_STORAGE_FEE_BILL_PAY("WINE_STORAGE_FEE_BILL_PAY", ModuleEnums.fengtan_wine.name(), Type.PAY, TransactionMqConstants.WINE_STORAGE_FEE_BILL_PAY_SUCCESS_TOPIC),

        /**
         * 酒票保管费支付
         */
        TICKET_STORAGE_FEE_BILL_PAY("TICKET_STORAGE_FEE_BILL_PAY", ModuleEnums.fengtan_wine.name(), Type.PAY, TransactionMqConstants.TICKET_STORAGE_FEE_BILL_PAY_SUCCESS_TOPIC),

        /**
         * 取酒订单支付
         */
        TAKE_WINE_ORDER_PAY("TAKE_WINE_ORDER_PAY", ModuleEnums.fengtan_wine.name(), Type.PAY, TransactionMqConstants.TAKE_WINE_ORDER_PAY_SUCCESS_TOPIC),

        /**
         * 定制酒订单支付
         */
        CUSTOM_WINE_ORDER_PAY("CUSTOM_WINE_ORDER_PAY", ModuleEnums.custom_wine.name(), Type.PAY, TransactionMqConstants.CUSTOM_WINE_PAY_SUCCESS_TOPIC),

        /**
         * 取酒订单退款
         */
        TAKE_WINE_ORDER_REFUND("TAKE_WINE_ORDER_REFUND", ModuleEnums.fengtan_wine.name(), Type.REFUND, TransactionMqConstants.TAKE_WINE_ORDER_PAY_REFUND_TOPIC),
        /**
         * 取酒订单通联支付
         */
        TAKE_WINE_ORDER_PAY_TL("TAKE_WINE_ORDER_PAY_TL", ModuleEnums.fengtan_wine.name(), Type.PAY, TransactionMqConstants.TAKE_WINE_ORDER_PAY_TL_SUCCESS_TOPIC),
        /**
         * 取酒订单通联退款
         */
        TAKE_WINE_ORDER_REFUND_TL("TAKE_WINE_ORDER_REFUND_TL", ModuleEnums.fengtan_wine.name(), Type.REFUND, TransactionMqConstants.TAKE_WINE_ORDER_PAY_REFUND_TL_TOPIC),
        ;

        /**
         * 业务类型
         */
        private final String type;

        /**
         * 业务模块
         */
        private final String moduleCode;

        /**
         * 交易类型
         */
        private final Type transactionType;

        /**
         * 交易成功回调 topic
         */
        private final String callbackTopic;

        BizTypeEnum(String type, String moduleCode, Type transactionType, String callbackTopic) {
            this.type = type;
            this.moduleCode = moduleCode;
            this.transactionType = transactionType;
            this.callbackTopic = callbackTopic;
        }

        public static BizTypeEnum getByType(String type) {
            for (BizTypeEnum value : BizTypeEnum.values()) {
                if (value.getType().equals(type)) {
                    return value;
                }
            }
            return null;
        }
    }

    /**
     * 交易状态
     */
    @Getter
    public enum Status {

        /**
         * 待确认
         */
        WAITING(1),

        /**
         * 交易成功
         */
        SUCCESS(2),

        /**
         * 交易失败
         */
        FAIL(3);

        /**
         * 状态
         */
        private final Integer status;

        Status(Integer status) {
            this.status = status;
        }
    }

    /**
     * 退款状态枚举 1-未退款 2-部分退款 3-全部退款
     */
    @Getter
    public enum RefundStatus {

        /**
         * 未退款
         */
        NOT_REFUND(1),

        /**
         * 部分退款
         */
        PART_REFUND(2),

        /**
         * 全部退款
         */
        ALL_REFUND(3);

        /**
         * 状态
         */
        private final Integer status;

        RefundStatus(Integer status) {
            this.status = status;
        }
    }

}
