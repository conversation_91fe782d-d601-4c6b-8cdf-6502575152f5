package com.hishop.wine.enums.points;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 基础系统涉及积分业务的业务类型
 * <AUTHOR>
 * @date 2023/8/17
 */
public enum BasicSystemBizType implements BizType {


    EXCEPTION(11, "注册送积分", MemberPointsEnum.ModifiedType.INCREASE);

    private final Integer code;
    private final String desc;
    private final MemberPointsEnum.ModifiedType parentType;

    BasicSystemBizType(Integer code, String desc, MemberPointsEnum.ModifiedType parentType) {
        this.code = code;
        this.desc = desc;
        this.parentType = parentType;
    }

    @Override
    public String moduleCode() {
        return "basic_system";
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public MemberPointsEnum.ModifiedType getParentType() {
        return parentType;
    }

    @Override
    public String getDesc(Integer code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .map(BasicSystemBizType::getDesc)
                .orElse("");
    }

    public static boolean isLegal(Integer code) {
        return Arrays.stream(values())
                .anyMatch(e -> e.getCode().equals(code));
    }

    public static List<BasicSystemBizType> getBizType(MemberPointsEnum.ModifiedType parentType) {
        return Optional.ofNullable(parentType)
                .map(type -> Arrays.stream(BasicSystemBizType.values())
                        .filter(e -> e.getParentType().equals(type))
                        .filter(e -> !BasicSystemBizType.EXCEPTION.equals(e))
                        .collect(Collectors.toList()))
                .orElse(Arrays.asList(BasicSystemBizType.values()));
    }

}
