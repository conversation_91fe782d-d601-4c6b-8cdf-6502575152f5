package com.hishop.wine.model.po.points;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hishop.common.pojo.SortPO;
import com.hishop.common.pojo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

/**   
 * 会员积分表 查询入参对象
 * @author: LiGuoQiang
 * @date: 2023-06-25
 */

@Data
@ApiModel(value = "MemberPointsQueryPO", description = "会员积分表查询入参对象")
public class MemberPointsQueryPO extends PageParam {

    @ApiModelProperty(value = "搜索关键字。目前支持用户昵称和手机号码")
    private String searchKey;
    @ApiModelProperty(value = "用户ID")
	private Long userId;
    @ApiModelProperty(value = "身份类型 1-管理员 2-消费者 3-经销商 4-终端")
    private Integer identityType;
    @ApiModelProperty(value = "模块编码。BASIC_SYSTEM：基础库；SCAN_MARKETING：扫码营销；FANS_CLUB：粉丝俱乐部")
    private String moduleCode;
    @ApiModelProperty(value = "用户手机号码")
	private String userPhone;
    @ApiModelProperty(value = "用户昵称")
    private String nickName;
    @ApiModelProperty(value = "变更类型。-1：减积分/积分消耗；0：积分清零；1：加积分/积分发放。方便区分")
    private Integer modifiedType;
    @ApiModelProperty(value = "具体的业务类型，枚举与来源系统保持一致。积分清零固定为0")
    private Integer bizType;
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    @ApiModelProperty("排序字段")
    private List<SortPO> sortList;



}
