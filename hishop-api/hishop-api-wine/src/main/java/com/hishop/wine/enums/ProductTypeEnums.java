package com.hishop.wine.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * 产品类型枚举
 *
 * <AUTHOR>
 * @date : 2023/6/19
 */
@Getter
public enum ProductTypeEnums {

    /**
     * 商品
     */
    GOODS(1, "商品"),

    /**
     * 礼品
     */
    GIFT(2, "礼品");

    /**
     * 产品类型
     */
    private final Integer type;

    /**
     * 类型名称
     */
    private final String name;

    ProductTypeEnums(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    /**
     * 根据类型获取名称
     *
     * @param type 类型
     * @return 名称
     */
    public static String getNameByType(Integer type) {
        for (ProductTypeEnums value : ProductTypeEnums.values()) {
            if (value.getType().equals(type)) {
                return value.getName();
            }
        }
        return StrUtil.EMPTY;
    }

    /**
     * 根据名称获取类型
     *
     * @param name 名称
     * @return 类型
     */
    public static Integer getTypeByName(String name) {
        for (ProductTypeEnums value : ProductTypeEnums.values()) {
            if (value.getName().equals(name)) {
                return value.getType();
            }
        }
        return null;
    }
}
