package com.hishop.wine.model.po.logisticsCode;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @description: 物流码状态更新请求参数
 * @author: chenzw
 * @date: 2024/7/10 15:34
 */
@Data
@ApiModel("物流码状态更新请求参数")
public class LogisticsCodeStatusPo {

    @ApiModelProperty(value = "物流码批次id列表")
    @NotEmpty(message = "物流码批次id列表不能为空")
    private List<Long> fileImportIdList;

    @ApiModelProperty(value = "状态（0：未使用，1：已使用）")
    private Integer status;
}
