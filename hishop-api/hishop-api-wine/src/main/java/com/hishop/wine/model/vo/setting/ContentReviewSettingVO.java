package com.hishop.wine.model.vo.setting;

import cn.hutool.core.util.StrUtil;
import com.hishop.setting.AbstractSetting;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 内容审核配置
 *
 * <AUTHOR>
 * @date : 2023/8/2
 */
@Data
@ApiModel(value = "ContentReviewSettingVO", description = "内容审核配置")
public class ContentReviewSettingVO extends AbstractSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "是否启用内容审核 true:启用 false:不启用")
    private Boolean enable;

    @ApiModelProperty(value = "appKey")
    private String appKey;

    @ApiModelProperty(value = "appSecret")
    private String appSecret;

    @ApiModelProperty(value = "region")
    private String region;

    @Override
    protected void initDefault() {
        this.enable = false;
        this.setAppKey(StrUtil.EMPTY);
        this.setAppSecret(StrUtil.EMPTY);
        this.setRegion(StrUtil.EMPTY);
    }
}
