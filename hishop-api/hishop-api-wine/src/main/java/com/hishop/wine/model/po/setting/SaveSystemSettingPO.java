package com.hishop.wine.model.po.setting;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hishop.wine.common.annotation.XssClean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date : 2023/7/12
 */
@Data
@ApiModel(value = "SaveSystemSettingPO", description = "保存系统配置参数")
public class SaveSystemSettingPO {

    @ApiModelProperty(value = "系统名称", required = true)
    @NotBlank(message = "系统名称不能为空")
    @XssClean(mode = XssClean.Mode.TEXT_ONLY, message = "系统名称包含不安全的脚本代码")
    private String systemName;

    @ApiModelProperty(value = "系统logo", required = true)
    @NotBlank(message = "请上传系统logo")
    private String logo;

    @ApiModelProperty("微信开放平台appId")
    private String appId;

    @ApiModelProperty("微信开放平台appSecret")
    private String appSecret;

    @ApiModelProperty("后台启用微信扫码登录")
    private Boolean adminWxScanLoginFlag;

    @ApiModelProperty(value = "用户协议", required = true)
    @NotBlank(message = "用户协议不能为空")
    @XssClean(mode = XssClean.Mode.RICH_TEXT, message = "用户协议包含不安全的脚本代码")
    private String userAgreement;

    @ApiModelProperty(value = "隐私政策", required = true)
    @NotBlank(message = "隐私政策不能为空")
    @XssClean(mode = XssClean.Mode.RICH_TEXT, message = "隐私政策包含不安全的脚本代码")
    private String privacyPolicy;

    public void check() {
        if (ObjectUtil.isNotNull(adminWxScanLoginFlag) && adminWxScanLoginFlag) {
            Assert.isTrue(StrUtil.isNotEmpty(appId) && StrUtil.isNotEmpty(appSecret),
                    "开启微信扫码登录后, 微信开放平台appId和appSecret不能为空");
        }
    }

}
