package com.hishop.wine.model.po.basic;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class AreaNamePO {

    @ApiModelProperty(value = "省", required = true)
    @NotNull(message = "省-不能为空")
    private String province;

    @ApiModelProperty(value = "市", required = true)
    @NotNull(message = "市-不能为空")
    private String city;

    @ApiModelProperty(value = "区", required = true)
    private String area;

}
