package com.hishop.wine.model.po.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 产品分类表 更新入参对象
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
@Data
@ApiModel(value = "ProductCategoryUpdatePO", description = "产品分类表更新入参对象")
public class ProductCategoryUpdatePO {

    @ApiModelProperty(value = "主键id", required = true)
    @NotNull(message = "分类id不能为空")
    private Long id;

    @ApiModelProperty(value = "分类名称", required = true)
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 20, message = "分类名称最大长度为20")
    private String categoryName;

    @ApiModelProperty(value = "排序", required = true)
    @NotNull(message = "请指定分类排序")
    private Integer orderNum;

    @ApiModelProperty(value = "状态 0：禁用  1：正常", required = true)
    @NotNull(message = "状态不能为空")
    private Boolean status;

}
