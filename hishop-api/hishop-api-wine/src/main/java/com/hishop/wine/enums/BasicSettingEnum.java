package com.hishop.wine.enums;

import com.hishop.setting.AbstractSetting;
import com.hishop.wine.model.vo.logistics.LogisticsSettingVO;
import com.hishop.wine.model.vo.setting.*;
import lombok.Getter;

/**
 * 公共配置枚举
 *
 * <AUTHOR>
 * @date : 2023/7/12
 */
public enum BasicSettingEnum {


    /**
     * 系统配置
     */
    SYSTEM_SETTING(SystemSettingVO.class, Boolean.FALSE),

    /**
     * 短信配置
     */
    SMS_SETTING(SmsSettingVO.class, Boolean.FALSE),

    /**
     * 腾讯地图配置
     */
    TENCENT_MAP_SETTING(TencentMapSettingVO.class, Boolean.FALSE),

    /**
     * 物流配置
     */
    LOGISTICS_SETTING(LogisticsSettingVO.class, Boolean.FALSE),

    /**
     * 积分设置
     */
    POINTS_SETTING(PointsSettingVO.class, Boolean.TRUE),

    /**
     * 内容审核密钥
     */
    CONTENT_REVIEW_SETTING(ContentReviewSettingVO.class, Boolean.FALSE),

    /**
     * 协议设置
     */
    PROTOCOL_SETTING(ProtocolSettingVO.class, Boolean.FALSE),

    /**
     * 外链配置
     */
    OUT_LINK_SETTING(OutLinkSettingVO.class, Boolean.FALSE),


    ;

    /**
     * 配置类
     */
    @Getter
    private final Class<? extends AbstractSetting> settingClass;

    /**
     * 是否接受同步
     */
    @Getter
    private final Boolean acceptSync;

    BasicSettingEnum(Class<? extends AbstractSetting> settingClass, Boolean acceptSync) {
        this.settingClass = settingClass;
        this.acceptSync = acceptSync;
    }
}
