package com.hishop.wine.model.vo.login;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 登录返回的用户信息
 *
 * <AUTHOR>
 * @date : 2023/7/6
 */
@Data
@ApiModel(value = "PcLoginUserVO", description = "PC端登录用户信息")
public class PcLoginUserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("账号")
    private String username;

    @ApiModelProperty("头像")
    private String icon;

    @ApiModelProperty("姓名")
    private String realName;

    @ApiModelProperty("身份类型 1-管理员 2-消费者 3-经销商 4-终端")
    private Integer identityType;

    @ApiModelProperty("是否是超级管理员")
    private Boolean izSupAdmin;

}
