package com.hishop.wine.assist;

import com.hishop.common.util.InnerResponseUtil;
import com.hishop.wine.api.TransactionFeign;
import com.hishop.wine.enums.TransactionEnum;
import com.hishop.wine.model.po.transaction.TransactionEntPayPO;
import com.hishop.wine.model.po.transaction.TransactionPayPO;
import com.hishop.wine.model.po.transaction.TransactionQueryPO;
import com.hishop.wine.model.po.transaction.TransactionRefundPO;
import com.hishop.wine.model.vo.transaction.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/8/4
 */
@Component
public class TransactionAssist {

    @Resource
    private TransactionFeign transactionFeign;

    /**
     * 发起支付
     *
     * @param bizType     业务类型
     * @param bizCode     业务编码
     * @param userId      用户id
     * @param amount      金额
     * @param description 商品描述
     * @return 支付参数返回值
     */
    public TransactionPayVO pay(TransactionEnum.BizTypeEnum bizType, String bizCode, Long userId, BigDecimal amount, String description) {
        TransactionPayPO transactionPayPO = new TransactionPayPO();
        transactionPayPO.setBizType(bizType);
        transactionPayPO.setBizCode(bizCode);
        transactionPayPO.setUserId(userId);
        transactionPayPO.setAmount(amount);
        transactionPayPO.setDescription(description);
        return InnerResponseUtil.resolve(transactionFeign.pay(transactionPayPO));
    }

    /**
     * 发起退款
     *
     * @param bizType          业务类型
     * @param bizCode          业务编码(退款单号)
     * @param orgTransactionId 原交易流水id
     * @param amount           退款金额
     * @param description      描述
     * @return 发起退款返回值
     */
    public TransactionRefundVO refund(TransactionEnum.BizTypeEnum bizType, String bizCode, Long orgTransactionId, BigDecimal amount, String description) {
        TransactionRefundPO transactionRefundPO = new TransactionRefundPO();
        transactionRefundPO.setBizType(bizType);
        transactionRefundPO.setBizCode(bizCode);
        transactionRefundPO.setOrgTransactionId(orgTransactionId);
        transactionRefundPO.setAmount(amount);
        transactionRefundPO.setDescription(description);
        return InnerResponseUtil.resolve(transactionFeign.refund(transactionRefundPO));
    }

    /**
     * 发起打款
     *
     * @param bizType 业务类型
     * @param bizCode 业务编码
     * @param userId  用户id
     * @param amount  金额
     * @return 打款参数返回值
     */
    public TransactionEntPayVO entPay(TransactionEnum.BizTypeEnum bizType, String bizCode, Long userId, BigDecimal amount, String description) {
        TransactionEntPayPO transactionEntPayPO = new TransactionEntPayPO();
        transactionEntPayPO.setBizType(bizType);
        transactionEntPayPO.setBizCode(bizCode);
        transactionEntPayPO.setUserId(userId);
        transactionEntPayPO.setAmount(amount);
        return InnerResponseUtil.resolve(transactionFeign.entPay(transactionEntPayPO));
    }

    /**
     * 查询交易明细
     *
     * @param bizType 业务类型
     * @param bizCode 业务编码
     * @param status  交易状态
     * @return 交易明细
     */
    public List<TransactionInfoVO> queryInfo(TransactionEnum.BizTypeEnum bizType, String bizCode, TransactionEnum.Status status) {
        TransactionQueryPO queryPO = new TransactionQueryPO();
        queryPO.setPageSize(Integer.MAX_VALUE);
        queryPO.setBizType(bizType);
        queryPO.setBizCode(bizCode);
        queryPO.setStatus(status);
        return InnerResponseUtil.resolve(transactionFeign.queryTransaction(queryPO)).getList();
    }

    /**
     * 查询交易汇总
     *
     * @param bizType 业务类型
     * @param bizCode 业务编码
     * @param status  交易状态
     * @return 交易汇总
     */
    public TransactionSummaryVO querySummary(TransactionEnum.BizTypeEnum bizType, String bizCode, TransactionEnum.Status status) {
        List<TransactionInfoVO> infoList = queryInfo(bizType, bizCode, status);
        return TransactionSummaryVO.of(infoList);
    }
}
