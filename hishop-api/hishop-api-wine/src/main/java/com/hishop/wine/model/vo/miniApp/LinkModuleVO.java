package com.hishop.wine.model.vo.miniApp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 链接模块vo
 *
 * <AUTHOR>
 * @date : 2023/7/21
 */
@Data
@ApiModel(value = "LinkModuleVO", description = "链接模块vo")
public class LinkModuleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("模块编码")
    private String code;

    @ApiModelProperty("模块名称")
    private String name;

    @ApiModelProperty("tab列表")
    private List<LinkTabVO> tabList;

}
