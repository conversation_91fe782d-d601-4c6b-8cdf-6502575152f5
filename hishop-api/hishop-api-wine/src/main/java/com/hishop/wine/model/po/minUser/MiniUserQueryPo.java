package com.hishop.wine.model.po.minUser;

import cn.hutool.core.lang.Assert;
import com.hishop.common.pojo.SortPO;
import com.hishop.common.pojo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 小程序用户表(MiniUser)表查询入参
 *
 * <AUTHOR>
 * @since 2024-01-25 11:06:07
 */
@Data
@ApiModel(value = "MiniUserPo", description = "小程序用户表查询入参")
public class MiniUserQueryPo extends PageParam {
    
    @ApiModelProperty(value = "客户名称")
    private String nickName;
    
    @ApiModelProperty(value = "客户手机")
    private String mobile;

    @ApiModelProperty(value = "标签ids")
    private List<Long> tagIds;

    @ApiModelProperty(value = "是否黑名单 0：否 1：是")
    private Boolean izBlacklist;

    @ApiModelProperty("排序字段")
    private List<SortPO> sortList;

    @ApiModelProperty(value = "排序sql", hidden = true)
    private String sortSql;

    public void validateParam() {
        Assert.isTrue(this.getPageNo() != null && this.getPageSize() != null, "查询请求分页参数不能为空");
    }
}
