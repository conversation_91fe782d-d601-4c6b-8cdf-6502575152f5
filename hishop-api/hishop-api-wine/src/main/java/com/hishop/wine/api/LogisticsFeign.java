package com.hishop.wine.api;

import com.alibaba.fastjson.JSONObject;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.po.logistics.LogisticsQueryPO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 物流 微服务接口
 *
 * @author: HuBiao
 * @date: 2023-06-26
 */
@FeignClient(name = "basic-system", contextId = "hishop-wine-logistics", url = "${feign.url.basic-system:basic-system}", path = "/wine/logistics")
public interface LogisticsFeign {

    /**
     * 物流查询
     *
     * @param queryPO 查询参数
     * @return 物流信息
     */
    @PostMapping("/query")
    ResponseBean<JSONObject> query(@Valid @RequestBody LogisticsQueryPO queryPO);
}