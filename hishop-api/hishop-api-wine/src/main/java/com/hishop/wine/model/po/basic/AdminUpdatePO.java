package com.hishop.wine.model.po.basic;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.hishop.common.exception.BusinessException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 编辑管理员入参
 *
 * <AUTHOR>
 * @date : 2023/6/21
 */
@Data
@ApiModel(value = "AdminUpdatePO", description = "编辑管理员入参对象")
public class AdminUpdatePO {

    @ApiModelProperty(value = "管理员id", required = true)
    @NotNull(message = "管理员id不能为空")
    private Long id;

    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "请输入姓名")
    @Size(max = 20, message = "姓名不能超过20位")
    private String realName;

    @ApiModelProperty(value = "头像")
    private String icon;

    @ApiModelProperty(value = "角色id", required = true)
    @NotNull(message = "请选择角色权限")
    private Long roleId;

    @ApiModelProperty(value = "登录密码", required = true)
    @Size(min = 6, message = "密码长度为6-18位")
    @Size(max = 18, message = "密码长度为6-18位")
    private String password;

    @ApiModelProperty(value = "确认密码", required = true)
    @Size(min = 6, message = "确认密码长度为6-18位")
    @Size(max = 18, message = "确认密码长度为6-18位")
    private String passwordAgain;


    @ApiModelProperty(value = "部门id", required = true)
    @NotNull(message = "请选择部门")
    private Long departmentId;

    @ApiModelProperty(value = "是否为业务员")
    private Boolean izBusinessUser;

    @ApiModelProperty(value = "销售区域列表")
    private List<Long> saleAreaList;

    private static final String PASSWORD_PATTERN =
            "^(?=.*[A-Z])(?=.*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]).{6,18}$";

    public static boolean isValidPassword(String password) {
        // 使用 Pattern 编译正则表达式
        Pattern pattern = Pattern.compile(PASSWORD_PATTERN);
        // 检查密码是否符合规则
        return pattern.matcher(password).matches();
    }

    /**
     * 检测新增员工信息参数
     */
    public void check() {
        // 如果是编辑员工, 则需要检测密码为空或者两次密码一致
        if (StrUtil.isNotEmpty(password) || StrUtil.isNotEmpty(passwordAgain)) {
            Assert.isTrue(StrUtil.equals(password, passwordAgain), "两次密码不一致");
        }

        if(!isValidPassword(password)){
            throw new BusinessException("密码必须为6-18位且包含大写字母和特殊字符");
        }

        if (izBusinessUser != null && izBusinessUser) {
            Assert.notEmpty(saleAreaList, "请选择销售区域");
        }

        if (CollectionUtils.isNotEmpty(saleAreaList)) {
            if (saleAreaList.size() > 20) {
                Assert.isTrue(false, "销售区域最多选择20个");
            }
        }
    }
}
