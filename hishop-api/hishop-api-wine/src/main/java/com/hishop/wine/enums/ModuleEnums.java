package com.hishop.wine.enums;

import com.google.common.collect.ImmutableList;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 模块枚举 与数据库中的hishop_module表保持一致
 *
 * <AUTHOR>
 * @date : 2023/7/4
 */
public enum ModuleEnums {

    /**
     * 基础系统
     */
    basic_system("控制台", "basicSystem"),
    /**
     * 扫码营销
     */
    scan_marketing("扫码营销","scanMarketing"),
    /**
     * 粉丝俱乐部
     */
    fans_club("粉丝俱乐部","fansClub"),
    /**
     * 封坛酒
     */
    fengtan_wine("封坛酒","fengtanWine"),
    /**
     * 定制酒
     */
    custom_wine("定制酒","customWine"),

    /**
     * 宴席酒
     */
    feast_wine("宴席酒","feastWine");

    private final String desc;

    private final String field;

    private static final Object LOCK = new Object();

    private static List<ModuleEnums> allList;

    ModuleEnums(String desc, String field) {
        this.desc = desc;
        this.field = field;
    }

    static {
        synchronized (LOCK) {
            List<ModuleEnums> list = new ArrayList<>();
            Collections.addAll(list, ModuleEnums.values());
            allList = ImmutableList.copyOf(list);
        }
    }

    public String getField() {
        return field;
    }
    public String getDesc() {
        return desc;
    }

    public static String getDesc(String code) {
        return Arrays.stream(values())
                .filter(e -> e.name().equalsIgnoreCase(code))
                .findFirst()
                .map(ModuleEnums::getDesc)
                .orElse("");
    }

    public static boolean isLegal(String code) {
        return Arrays.stream(values())
                .anyMatch(e -> e.name().equalsIgnoreCase(code));
    }

    public static List<ModuleEnums> list() {
        return allList;
    }

}
