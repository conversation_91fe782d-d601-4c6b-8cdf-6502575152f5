package com.hishop.wine.model.vo.setting;

import cn.hutool.core.util.StrUtil;
import com.hishop.setting.AbstractSetting;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 积分名称配置
 *
 * <AUTHOR>
 * @date : 2023/7/24
 */
@Data
@ApiModel(value = "PointsSettingVO", description = "积分配置")
public class PointsSettingVO extends AbstractSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "积分名称")
    private String pointsName;

    @ApiModelProperty(value = "新会员注册可获得积分数")
    private Integer newRegisterPoints;

    @Override
    protected void initDefault() {
        this.setPointsName(StrUtil.EMPTY);
        this.newRegisterPoints = 0;
    }
}
