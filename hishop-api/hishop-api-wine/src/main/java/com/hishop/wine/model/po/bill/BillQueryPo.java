package com.hishop.wine.model.po.bill;

import cn.hutool.core.lang.Assert;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hishop.common.pojo.SortPO;
import com.hishop.common.pojo.page.PageParam;
import com.hishop.wine.enums.bill.TransactionType;
import com.hishop.wine.enums.order.PayMethod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 账单查询
 * <AUTHOR>
 * @since 2024-01-29 1:49:23
 */
@Data
@ApiModel(value = "BillQueryPo", description = "账单查询")
public class BillQueryPo extends PageParam {

    @ApiModelProperty(value = "模块编码")
    private String moduleCode;

    @ApiModelProperty(value = "支付方式 online_pay:在线支付 offline_pay:线下支付")
    private String payMethod;

    @ApiModelProperty(value = "开始时间。yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间。yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "单号")
    private String no;

    @ApiModelProperty(value = "交易类型")
    private String transactionType;

    @ApiModelProperty(value = "金额最小值")
    private BigDecimal minFee;

    @ApiModelProperty(value = "金额最大值")
    private BigDecimal maxFee;

    @ApiModelProperty(value = "年份")
    @JsonFormat(pattern = "yyyy", timezone = "GMT+8")
    private Date year;

    @ApiModelProperty(value = "月份")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    private Date month;

    @ApiModelProperty("排序字段")
    private List<SortPO> sortList;

    @ApiModelProperty(value = "排序sql", hidden = true)
    private String sortSql;

    public void validateParam() {
        Assert.isTrue(this.getPageNo() != null && this.getPageSize() != null, "查询请求分页参数不能为空");
    }
}
