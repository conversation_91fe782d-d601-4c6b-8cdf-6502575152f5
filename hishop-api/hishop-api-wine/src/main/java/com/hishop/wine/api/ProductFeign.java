package com.hishop.wine.api;

import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.po.product.ProductIdQueryPO;
import com.hishop.wine.model.po.product.ProductQueryPO;
import com.hishop.wine.model.vo.product.ProductInnerVO;
import com.hishop.wine.model.vo.product.ProductPageVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * 产品表 微服务接口
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
@FeignClient(name = "basic-system",
        contextId = "hishop-wine-product",
        url = "${feign.url.basic-system:basic-system}",
        path = "/wine/product")
public interface ProductFeign {

    /**
     * 分页查询产品列表
     *
     * @param pagePO 分页参数
     * @return 产品列表
     */
    @PostMapping("/pc/pageList")
    ResponseBean<PageResult<ProductPageVO>> pageList(@RequestBody @Valid ProductQueryPO pagePO);

    /**
     * 查询产品详情 用户内部数据同步
     *
     * @param id 产品id
     * @return 产品详情
     */
    @GetMapping("/pc/inner/detail")
    ResponseBean<ProductInnerVO> getDetailForInner(@RequestParam(name = "id") Long id);

    /**
     * 根据ID批量查询产品信息 用户内部数据同步
     *
     * @param qryPO 查询参数
     * @return 产品详情
     */
    @PostMapping("/pc/inner/listById")
    ResponseBean<List<ProductInnerVO>> listById(@RequestBody ProductQueryPO qryPO);

    /**
     * 查询产品id的集合
     *
     * @param productIdQueryPO 查询参数
     * @return 产品id的集合
     */
    @PostMapping("/inner/queryIds")
    ResponseBean<List<Long>> queryIds(@RequestBody @Valid ProductIdQueryPO productIdQueryPO);


}