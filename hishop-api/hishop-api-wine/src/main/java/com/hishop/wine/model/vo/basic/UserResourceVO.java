package com.hishop.wine.model.vo.basic;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 用户资源返回值
 *
 * <AUTHOR>
 * @date : 2023/7/20
 */
@NoArgsConstructor
@Data
public class UserResourceVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String defaultPagePath;
    private List<ProductListVO> productList;
    private List<MenuListVO> menuList;

    @NoArgsConstructor
    @Data
    public static class ProductListVO {
        private String type;
        private String name;
    }

    @NoArgsConstructor
    @Data
    public static class MenuListVO {
        private String appName;
        private Integer menuLayout;
        private DefaultIndexVO defaultIndex;
        private List<MenuDataVO> menuData;

        @NoArgsConstructor
        @Data
        public static class DefaultIndexVO {
            private String path;
            private Long id;
        }

        @NoArgsConstructor
        @Data
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class MenuDataVO {
            private String name;
            private String path;
            private Long id;
            private String icon;
            @JsonProperty("isShow")
            private Boolean izShow;
            @JsonProperty("isTab")
            private Boolean izTab;
            @JsonProperty("isGroup")
            private Boolean izGroup;
            private List<MenuDataVO> menus;
            @JsonIgnore
            private Long parentId;
        }
    }
}
