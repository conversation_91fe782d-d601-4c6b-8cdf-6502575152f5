package com.hishop.wine.model.vo.login;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**   
 * 子模块授权返回对象
 * @author: chenpeng
 * @date: 2023-07-13
 */

@Data
@ApiModel(value = "MiniConfigVO", description = "子模块授权返回对象")
public class AuthModuleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "积分商城，1：已授权，其他：未授权")
    private String pointsMall;

    @ApiModelProperty(value = "扫码营销，1：已授权，其他：未授权")
    private String scanMarketing;

    @ApiModelProperty(value = "粉丝俱乐部，1：已授权，其他：未授权")
    private String fansClub;

    @ApiModelProperty(value = "封坛酒，1：已授权，其他：未授权")
    private String fengtanWine;

    @ApiModelProperty(value = "定制酒，1：已授权，其他：未授权")
    private String customWine;

    @ApiModelProperty(value = "宴席酒，1：已授权，其他：未授权")
    private String feastWine;
}
