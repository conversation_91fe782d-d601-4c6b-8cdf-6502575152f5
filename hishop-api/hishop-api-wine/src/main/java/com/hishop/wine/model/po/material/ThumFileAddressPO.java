package com.hishop.wine.model.po.material;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(value = "ThumFileAddressPO", description = "截图的详细信息")
public class ThumFileAddressPO {
    @JsonProperty("location")
    private String location;

    @JsonProperty("bucket")
    private String bucket;
    @JsonProperty("object")
    private String object;
    @JsonProperty("file_name")
    private String fileName;
}
