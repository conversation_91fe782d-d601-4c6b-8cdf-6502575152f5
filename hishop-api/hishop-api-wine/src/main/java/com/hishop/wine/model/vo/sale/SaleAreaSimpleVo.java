package com.hishop.wine.model.vo.sale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description: 经销商销售区域入参
 * @author: chenzw
 * @date: 2024/7/5 14:17
 */
@Data
@ApiModel(value = "DealerSaleAreaPo", description = "经销商销售区域入参")
public class SaleAreaSimpleVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "销售区域ID")
    private Long id;

    @ApiModelProperty(value = "销售区域名称")
    private String name;
}
