package com.hishop.wine.model.vo.points;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/6/25
 */
@Data
@ApiModel(value = "MemberPointsSummaryVO", description = "会员积分汇总表返回对象")
public class MemberPointsSummaryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID")
    private Long userId;
    @ApiModelProperty(value = "身份类型 1-管理员 2-消费者 3-经销商 4-终端")
    private Integer identityType;
    @ApiModelProperty(value = "身份类型 1-管理员 2-消费者 3-经销商 4-终端")
    private String identityTypeDesc;
    /**
     * 所有用户总的可用积分
     */
    @ApiModelProperty(value = "所有用户总的可用积分")
    private Integer availablePoints;
    /**
     * 所有用户总的累计发放积分
     */
    @ApiModelProperty(value = "所有用户总的累计发放积分")
    private Integer totalPoints;
    /**
     * 所有用户总的已消耗积分
     */
    @ApiModelProperty(value = "所有用户总的已消耗积分")
    private Integer consumedPoints;
    /**
     * 所有用户总的过期清零的积分
     */
    @ApiModelProperty(value = "所有用户总的过期清零的积分")
    private Integer expiredPoints;

}
