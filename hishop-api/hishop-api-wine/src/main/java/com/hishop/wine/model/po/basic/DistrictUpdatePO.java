package com.hishop.wine.model.po.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 编辑地区入参
 *
 * <AUTHOR>
 * @date : 2023/7/17
 */
@Data
@ApiModel(value = "DistrictUpdatePO", description = "编辑地区入参")
public class DistrictUpdatePO {

    @NotNull(message = "id不能为空")
    @ApiModelProperty(value = "地区id", required = true)
    private Integer id;

    @NotBlank(message = "地区名称不能为空")
    @ApiModelProperty(value = "地区名称", required = true)
    private String name;

}
