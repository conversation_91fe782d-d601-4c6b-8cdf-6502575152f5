package com.hishop.wine.model.po.pages;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**   
 * 页面配置表 新增入参对象
 * @author: HuBiao
 * @date: 2023-07-07
 */

@Data
@ApiModel(value = "PagesCreatePO", description = "页面配置表新增入参对象")
public class PagesCreatePO {
	
    @ApiModelProperty(value = "模块编码")
	private String moduleCode;

    @ApiModelProperty(value = "名称", required = true)
	private String name;

    @ApiModelProperty(value = "请求地址", required = true)
	private String path;

}
