package com.hishop.wine.model.po.log;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.hishop.common.pojo.page.PageParam;
import java.util.Date;

/**   
 * 操作日志表 查询入参对象
 * @author: HuBiao
 * @date: 2023-08-01
 */

@Data
@ApiModel(value = "SystemLogQueryPO", description = "操作日志表查询入参对象")
public class SystemLogQueryPO extends PageParam {

    @ApiModelProperty(value = "模块编码")
	private String moduleCode;

    @ApiModelProperty("业务描述")
    private String businessDesc;

    @ApiModelProperty(value = "操作名称")
	private String operationName;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty("操作账号")
    private String operationUsername;




}
