package com.hishop.wine.model.vo.rank;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hishop.common.util.serializer.ObjectToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date : 2023/7/25
 */
@Data
@ApiModel(value = "RankSelectVO", description = "头衔下拉")
public class RankSelectVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonSerialize(using = ObjectToStringSerializer.class)
    @ApiModelProperty("头衔id")
    private Long id;

    @ApiModelProperty(value = "头衔名称")
    private String rankName;

}
