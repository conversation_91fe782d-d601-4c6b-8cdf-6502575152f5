package com.hishop.wine.model.vo.setting;

import cn.hutool.core.util.StrUtil;
import com.hishop.common.annotation.Desensitized;
import com.hishop.common.enums.SensitiveTypeEnum;
import com.hishop.setting.AbstractSetting;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 系统配置
 *
 * <AUTHOR>
 * @date : 2023/7/12
 */
@Data
@ApiModel(value = "SaveSystemSettingVO", description = "系统配置参数")
public class SystemSettingVO extends AbstractSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "系统名称")
    private String systemName;

    @ApiModelProperty(value = "系统logo")
    private String logo;

    @ApiModelProperty("微信开放平台appId")
    private String appId;

    @ApiModelProperty("微信开放平台appSecret")
    @Desensitized(type = SensitiveTypeEnum.ALL)
    private String appSecret;

    @ApiModelProperty("后台启用微信扫码登录")
    private Boolean adminWxScanLoginFlag;

    @ApiModelProperty(value = "用户协议")
    private String userAgreement;

    @ApiModelProperty(value = "隐私政策")
    private String privacyPolicy;

    @Override
    protected void initDefault() {
        this.setSystemName(StrUtil.EMPTY);
        this.setLogo(StrUtil.EMPTY);
        this.setAppId(StrUtil.EMPTY);
        this.setAppSecret(StrUtil.EMPTY);
        this.setAdminWxScanLoginFlag(Boolean.FALSE);
        this.setUserAgreement(StrUtil.EMPTY);
        this.setPrivacyPolicy(StrUtil.EMPTY);
    }
}
