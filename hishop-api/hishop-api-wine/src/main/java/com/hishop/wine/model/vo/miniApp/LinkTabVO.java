package com.hishop.wine.model.vo.miniApp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 链接tab vo
 *
 * <AUTHOR>
 * @date : 2023/7/21
 */
@Data
@ApiModel(value = "LinkTabVO", description = "链接tabVo")
public class LinkTabVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("tab 唯一标识 FUNCTION_PAGES-功能页面 EXCHANGE_ACTIVITY-抽奖活动")
    private String code;

    @ApiModelProperty("tab 名称")
    private String name;

    @ApiModelProperty("功能页面列表")
    private List<LinkPagesVO> pagesList;

}
