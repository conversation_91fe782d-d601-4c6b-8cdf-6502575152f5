package com.hishop.wine.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/07/11/ $
 * @description:
 */
@Getter
@AllArgsConstructor
public enum CheckIdentityTypeEnum {
    /**
     * 正常
     */
    NORMAL(10000, "正常"),
    /**
     * 手机号未绑定门店信息
     */
    PHONE_NOT_REGISTERED(10001, "手机号未注册，是否前往注册"),
    /**
     *  当前openID与phone不匹配
     */
    OPEN_ID_BOUND(10002, "当前openID已经绑定"),
    /**
     *  openID首次绑定
     */
    ACTIVATION_FIRST(10003, "当前账号已经激活，欢迎进入"),
    /**
     * 业务员不存在
     */
    SALESMAN_NOT_EXIST(10004, "账号不存在，请确认手机号"),
    /**
     * 门店处于禁用状态
     */
    TERMINATE_DISABLE(10005, "门店处于禁用状态"),
    /**
     * 门店处于审核状态
     */
    TERMINATE_AUDIT(10006, "当前账号正在审核中，请稍后再试"),
    /**
     * 门店处于审核不通过状态
     */
    TERMINATE_REJECT(10007, "门店审核不通过"),
    ;

    @EnumValue
    private final Integer value;

    private final String desc;
}
