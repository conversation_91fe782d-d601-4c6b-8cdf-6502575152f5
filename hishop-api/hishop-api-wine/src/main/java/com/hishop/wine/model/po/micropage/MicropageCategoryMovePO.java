package com.hishop.wine.model.po.micropage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "MicropageCategoryMovePO", description = "微页面分组移动对象")
public class MicropageCategoryMovePO {
    @ApiModelProperty(value = "父id")
    private int parentId;

    @ApiModelProperty(value = "需要移动的分组集合")
    private List<Long> ids;
}
