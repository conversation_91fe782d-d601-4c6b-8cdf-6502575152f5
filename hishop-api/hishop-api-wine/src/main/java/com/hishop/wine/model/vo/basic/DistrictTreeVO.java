package com.hishop.wine.model.vo.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 地区树节点
 *
 * <AUTHOR>
 * @date : 2023/5/29
 */
@Data
@ApiModel(value = "DistrictTreeVO", description = "行政区域树返回参数")
public class DistrictTreeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键id")
    private Integer id;

    @ApiModelProperty("地区名称")
    private String name;

    @ApiModelProperty("上级地区id")
    private Integer parentId;

    @ApiModelProperty("下级地区")
    private List<DistrictTreeVO> childList;
}
