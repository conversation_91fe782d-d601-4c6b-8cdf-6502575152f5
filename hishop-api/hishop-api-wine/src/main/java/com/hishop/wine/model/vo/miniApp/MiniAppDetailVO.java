package com.hishop.wine.model.vo.miniApp;

import com.hishop.common.annotation.Desensitized;
import com.hishop.common.enums.SensitiveTypeEnum;
import com.hishop.wine.model.vo.decorate.DecorateVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 小程序详情
 *
 * <AUTHOR>
 * @date : 2023/7/18
 */
@Data
@ApiModel(value = "MiniAppDetailVO", description = "小程序详情")
public class MiniAppDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("appId")
    private String appId;

    @ApiModelProperty("appName")
    private String appName;

    @ApiModelProperty("appSecret")
    @Desensitized(type = SensitiveTypeEnum.ALL)
    private String appSecret;

    @ApiModelProperty("小程序原始id")
    private String originalId;

    @ApiModelProperty("当前版本(暂时获取不到)")
    private String version;

    @ApiModelProperty("绑定业务模块")
    private String moduleNames;

    @ApiModelProperty("主页和底部导航展示名称")
    private String moduleSettingName;

    @ApiModelProperty("装修信息")
    private DecorateVO decorateVO;

    @ApiModelProperty("底部导航信息")
    private DecorateVO navigation;

    @ApiModelProperty("小程序码")
    private String qrCode;

}
