package com.hishop.wine.model.po.material;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 素材库 上传资源回调入参对象
 *
 * @author: HuBiao
 * @date: 2023-06-20
 */
@Data
@ApiModel(value = "MaterialUploadSuccessPO", description = "素材库上传成功回调入参对象")
public class MaterialUploadSuccessPO {

    @ApiModelProperty(value = "资源路径", required = true)
    @NotBlank(message = "资源路径不能为空")
    private String path;

    @ApiModelProperty(value = "资源大小(Byte)", required = true)
    @NotNull(message = "资源大小不能为空")
    private Long size;

    @ApiModelProperty(value = "音视频长度(单位：秒)")
    private Integer duration;

}
