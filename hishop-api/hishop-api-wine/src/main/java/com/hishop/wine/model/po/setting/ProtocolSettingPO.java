package com.hishop.wine.model.po.setting;

import com.hishop.wine.common.annotation.XssClean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 协议配置
 *
 * <AUTHOR>
 * @date : 2023/8/23
 */
@Data
@ApiModel(value = "ProtocolSettingPO", description = "协议配置")
public class ProtocolSettingPO {

    @ApiModelProperty(value = "用户协议")
    @NotBlank(message = "用户协议不能为空")
    @XssClean(mode = XssClean.Mode.RICH_TEXT, message = "用户协议包含不安全的脚本代码")
    private String userAgreement;

    @ApiModelProperty(value = "隐私政策")
    @NotBlank(message = "隐私政策不能为空")
    @XssClean(mode = XssClean.Mode.RICH_TEXT, message = "隐私政策包含不安全的脚本代码")
    private String privacyPolicy;

}
