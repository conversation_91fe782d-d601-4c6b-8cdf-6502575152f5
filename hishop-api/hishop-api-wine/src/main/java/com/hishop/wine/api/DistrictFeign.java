package com.hishop.wine.api;

import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.po.basic.AreaNamePO;
import com.hishop.wine.model.vo.basic.DistrictTreeVO;
import com.hishop.wine.model.vo.basic.DistrictVO;
import com.hishop.wine.model.vo.basic.RegionVO;
import com.hishop.wine.model.vo.map.TecentAddressDetailDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 地区表 微服务接口
 *
 * @author: HuBiao
 * @date: 2023-06-26
 */
@FeignClient(name = "basic-system", contextId = "hishop-wine-district", url = "${feign.url.basic-system:basic-system}", path = "/wine/district")
public interface DistrictFeign {

    /**
     * 查询行政区域树
     *
     * @param maxLevel 层级
     * @return 行政区域树
     */
    @ApiOperation("获取行政区域树")
    @GetMapping("/tree")
    ResponseBean<List<DistrictTreeVO>> tree(@RequestParam(value = "maxLevel", defaultValue = "3") Integer maxLevel);

    /**
     * 根据上级id 查询行政区域列表
     *
     * @param parentId 上级id
     * @return 行政区域列表
     */
    @ApiOperation("获取区域列表")
    @GetMapping("/list")
    ResponseBean<List<DistrictVO>> list(@RequestParam(value = "parentId", defaultValue = "0") Integer parentId);

    /**
     * 查询行政区域详情
     *
     * @param id 区域id
     * @return 区域详情
     */
    @ApiOperation("获取区域详情")
    @GetMapping("/detail")
    ResponseBean<DistrictVO> detail(@RequestParam Integer id);

    /**
     * 获取大区集合
     *
     * @return 大区集合
     */
    @ApiOperation("获取大区集合")
    @GetMapping("/listRegions")
    ResponseBean<List<RegionVO>> listRegions();

    /**
     * 根据等级查询区域信息
     *
     * @param maxLevel 最大等级
     * @return 区域信息
     */
    @ApiOperation("查询等级内的区域信息")
    @GetMapping("/listByLevel")
    ResponseBean<List<DistrictVO>> listByLevel(@RequestParam(value = "maxLevel", defaultValue = "3") Integer maxLevel);

    @ApiOperation("根据经纬度获取本系统位置")
    @GetMapping("/getByLocation")
    ResponseBean<DistrictVO> getByLocation(@RequestParam(value = "lng") String lng, @RequestParam(value = "lat") String lat);

    @ApiOperation("根据经纬度获取位置")
    @GetMapping("/getMapByLocation")
    ResponseBean<TecentAddressDetailDTO> getMapByLocation(@RequestParam(value = "lng") String lng, @RequestParam(value = "lat") String lat);

    @ApiOperation("根据名称，查询区级信息")
    @PostMapping("/getAreaByName")
    ResponseBean<DistrictVO> getAreaByName(@RequestBody @Valid AreaNamePO areaNamePO);



}