package com.hishop.wine.model.po.minUser;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;

/**
 * 小程序用户删除标签
 * <AUTHOR>
 * @since 2024-01-31 14:06:07
 */
@Data
@ApiModel(value = "MinUserDelTagPo", description = "小程序用户删除标签")
public class MinUserDelTagPo {

    @ApiModelProperty(value = "userId/小程序用户id(定制酒PC接口)")
    @NotNull(message = "userId/小程序用户id不能为空")
    private Long id;

    @ApiModelProperty(value = "小程序标签id")
    @NotNull(message = "小程序标签id不能为空")
    private Long tagId;

}
