package com.hishop.wine.model.po.basic;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.hishop.common.exception.BusinessException;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 新增管理员入参
 *
 * <AUTHOR>
 * @date : 2023/6/21
 */
@Data
@ApiModel(value = "AdminCreatePO", description = "新增管理员入参对象")
public class AdminCreatePO {

    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "请输入手机号")
    @Pattern(regexp = "^1[3|4|5|6|7|8|9][0-9]\\d{8}$", message = "手机号格式不正确")
    private String mobile;

    @ApiModelProperty(value = "用户名", required = true)
    @NotBlank(message = "请输入用户名")
    @Size(min = 4, message = "用户名长度为4-12位")
    @Size(max = 12, message = "用户名长度为4-12位")
    private String username;

    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "请输入姓名")
    @Size(max = 20, message = "姓名不能超过20位")
    private String realName;

    @ApiModelProperty(value = "头像")
    private String icon;

    @ApiModelProperty(value = "角色id", required = true)
    @NotNull(message = "请选择角色权限")
    private Long roleId;

    @ApiModelProperty(value = "登录密码", required = true)
    @NotBlank(message = "请输入登录密码")
    @Size(min = 6, message = "密码长度为6-18位")
    @Size(max = 18, message = "密码长度为6-18位")
    private String password;

    @ApiModelProperty(value = "确认密码", required = true)
    @NotBlank(message = "请输入确认密码")
    @Size(min = 6, message = "确认密码长度为6-18位")
    @Size(max = 18, message = "确认密码长度为6-18位")
    private String passwordAgain;

    @ApiModelProperty(value = "部门id", required = true)
    @NotNull(message = "请选择部门")
    private Long departmentId;

    @ApiModelProperty(value = "是否为业务员")
    private Boolean izBusinessUser;

    @ApiModelProperty(value = "销售区域列表")
    private List<Long> saleAreaList;

    private static final String PASSWORD_PATTERN =
            "^(?=.*[A-Z])(?=.*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]).{6,18}$";

    public static boolean isValidPassword(String password) {
        // 使用 Pattern 编译正则表达式
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(PASSWORD_PATTERN);
        // 检查密码是否符合规则
        return pattern.matcher(password).matches();
    }

    /**
     * 检测新增员工信息参数
     */
    public void check() {
        Assert.isTrue(StrUtil.isNotEmpty(password), "请输入登录密码");
        Assert.isTrue(StrUtil.isNotEmpty(passwordAgain), "请输入确认密码");
        Assert.isTrue(StrUtil.equals(password, passwordAgain), "两次密码不一致");

        if(!isValidPassword(password)){
            throw new BusinessException("密码必须为6-18位且包含大写字母和特殊字符");
        }

        if (izBusinessUser != null && izBusinessUser) {
            Assert.notEmpty(saleAreaList, "请选择销售区域");
        }

        if (CollectionUtils.isNotEmpty(saleAreaList)) {
            if (saleAreaList.size() > 20) {
                Assert.isTrue(false, "销售区域最多选择20个");
            }
        }
    }
}
