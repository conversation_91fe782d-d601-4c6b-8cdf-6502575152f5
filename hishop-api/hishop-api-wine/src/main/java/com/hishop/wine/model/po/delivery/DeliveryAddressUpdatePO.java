package com.hishop.wine.model.po.delivery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 收货地址表 更新入参对象
 *
 * @author: HuBiao
 * @date: 2023-06-29
 */
@Data
@ApiModel(value = "DeliveryAddressUpdatePO", description = "收货地址表更新入参对象")
public class DeliveryAddressUpdatePO {

    @ApiModelProperty("收货地址id")
    @NotNull(message = "收货地址id不能为空")
    private Long id;

    @ApiModelProperty(value = "收货人", required = true)
    @NotBlank(message = "请填写收货人名称")
    @Size(max = 20, message = "收货人姓名不能超过20字")
    private String consignee;

    @ApiModelProperty(value = "收货人手机号", required = true)
    @NotBlank(message = "请填写收货人手机号")
    @Pattern(regexp = "^1[3|4|5|6|7|8|9][0-9]\\d{8}$", message = "手机号格式不正确")
    private String consigneePhone;

    @ApiModelProperty(value = "街道id", required = true)
    @NotNull(message = "请选择街道")
    private Integer streetId;

    @ApiModelProperty(value = "详细地址", required = true)
    @NotBlank(message = "请填写详细地址")
    private String address;

    @ApiModelProperty(value = "是否是默认地址 true-是 false 不是")
    private Boolean izDefault;


}
