package com.hishop.wine.model.po.payment;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 微信支付入参
 *
 * <AUTHOR>
 * @date : 2023/7/19
 */
@Data
@ApiModel(value = "PaymentSettingWxPO", description = "微信支付入参")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaymentSettingWxPO {

    @ApiModelProperty(value = "商户号", required = true)
    private String mchId;

    @ApiModelProperty(value = "apiV3支付密钥", required = true)
    private String apiV3Key;

    @ApiModelProperty(value = "证书id", required = true)
    private Long certificateId;

    @ApiModelProperty(value = "是否支持服务商模式", required = true)
    private Boolean isSupportSec;

    @ApiModelProperty(value = "服务商商户id")
    private String spMchId;

    @ApiModelProperty(value = "服务商小程序id")
    private String spAppId;

    /**
     * 使用Assert 检测参数不为空
     */
    public void checkParam() {
        Assert.isTrue(StrUtil.isNotEmpty(mchId), "商户号不能为空");
        Assert.isTrue(StrUtil.isNotEmpty(apiV3Key), "apiV3支付密钥不能为空");
        Assert.isTrue(ObjectUtil.isNotNull(certificateId), "请上传商户证书");
        if (isSupportSec != null && isSupportSec) {
            Assert.isTrue(StrUtil.isNotEmpty(spMchId), "服务商商户id不能为空");
            Assert.isTrue(StrUtil.isNotEmpty(spAppId), "服务商小程序id不能为空");
        }
    }
}
