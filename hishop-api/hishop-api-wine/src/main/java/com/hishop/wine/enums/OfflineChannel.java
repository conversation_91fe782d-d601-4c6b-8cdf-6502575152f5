package com.hishop.wine.enums;

import lombok.Getter;

import java.util.Arrays;

public enum OfflineChannel {

    /**
     * 微信转账
     */
    WECHAT_TRANSFER("WECHAT_TRANSFER", "微信转账"),
    /**
     * 支付宝转账
     */
    ALI_TRANSFER("ALI_TRANSFER", "支付宝转账"),
    /**
     * 银行卡转账
     */
    BANK_CARD_TRANSFER("BANK_CARD_TRANSFER", "银行卡转账");


    /**
     * 支付类型
     */
    @Getter
    private final String type;

    /**
     * 支付类型名称
     */
    @Getter
    private final String name;

    OfflineChannel(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public static OfflineChannel getOfflineChannelEnumByType(String type) {
        return Arrays.stream(OfflineChannel.values()).filter(item -> item.getType().equals(type)).findFirst().orElse(null);
    }
}
