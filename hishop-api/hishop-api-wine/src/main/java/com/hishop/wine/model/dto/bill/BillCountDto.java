package com.hishop.wine.model.dto.bill;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 *  对账统计
 */
@Data
@Builder
@AllArgsConstructor
@ApiModel(value = "BillCountDto", description = "对账统计")
public class BillCountDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总收入(元)")
    private BigDecimal totalFee;

    @ApiModelProperty(value = "总收入(笔)")
    private Long totalNum;

}
