package com.hishop.wine.model.po.material;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(value = "ThumbnailInfoPO", description = "截图信息")
public class ThumbnailInfoPO {
    @JsonProperty("task_id")
    private String taskId;

    @JsonProperty("status")
    private String status;

    @JsonProperty("output")
    private  ThumFileAddressPO output;
}
