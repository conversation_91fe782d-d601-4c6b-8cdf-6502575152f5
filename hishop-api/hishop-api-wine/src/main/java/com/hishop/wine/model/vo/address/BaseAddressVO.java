package com.hishop.wine.model.vo.address;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.hishop.wine.model.vo.delivery.DeliveryAddressSimpleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * 地址库表 返回对象
 * @author: HuBiao
 * @date: 2023-07-17
 */

@Data
@ApiModel(value = "BaseAddressVO", description = "地址库表返回对象")
public class BaseAddressVO {

    @ApiModelProperty("地址id")
    private Long id;

    @ApiModelProperty(value = "联系人")
    private String contacts;

    @ApiModelProperty(value = "联系人手机号")
    private String contactsPhone;

    @ApiModelProperty(value = "省ID")
    private Integer provinceId;

    @ApiModelProperty("省份名称")
    private String province;

    @ApiModelProperty(value = "城市ID")
    private Integer cityId;

    @ApiModelProperty("城市名称")
    private String city;

    @ApiModelProperty(value = "区ID")
    private Integer areaId;

    @ApiModelProperty("区名称")
    private String area;

    @ApiModelProperty("街道id")
    private Integer streetId;

    @ApiModelProperty("街道名称")
    private String street;

    @ApiModelProperty(value = "详细省市区")
    private String district;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "邮编")
    private String postalCode;

    @ApiModelProperty(value = "是否默认发货地址")
    private Boolean izDefaultSendAddress;

    @ApiModelProperty(value = "是否默认收货地址")
    private Boolean izDefaultReceiveAddress;

    public String getDistrict() {
        return StrUtil.emptyIfNull(province) + StrUtil.emptyIfNull(city) + StrUtil.emptyIfNull(area) + StrUtil.emptyIfNull(street);
    }

    public BaseAddressSimpleVO simpleBuilder() {
        return BeanUtil.copyProperties(this, BaseAddressSimpleVO.class);
    }
    

}
