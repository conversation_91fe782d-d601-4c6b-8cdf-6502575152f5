package com.hishop.wine.model.vo.module;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 子模块返回对象
 *
 * @author: chenpeng
 * @date: 2023-07-14
 */

@Data
@ApiModel(value = "BindModuleVO", description = "子模块返回对象")
public class BindModuleVO extends ModuleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("是否是默认")
    private Boolean izDefault;
}
