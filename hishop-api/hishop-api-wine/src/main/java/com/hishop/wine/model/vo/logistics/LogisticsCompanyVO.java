package com.hishop.wine.model.vo.logistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**   
 * 物流公司表 返回对象
 * @author: chenpeng
 * @date: 2023-07-08
 */

@Data
@ApiModel(value = "LogisticsCompanyVO", description = "物流公司表返回对象")
public class LogisticsCompanyVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
	private Integer id;
    
    @ApiModelProperty(value = "物流公司名称")
	private String companyName;

    @ApiModelProperty(value = "物流公司图片")
    private String companyUrl;

    @ApiModelProperty(value = "物流公司编码")
    private String shipperCode;

    @ApiModelProperty(value = "是否选中")
    private boolean izSelected;

}
