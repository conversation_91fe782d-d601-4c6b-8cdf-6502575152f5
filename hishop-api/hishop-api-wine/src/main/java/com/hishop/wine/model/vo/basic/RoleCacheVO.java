package com.hishop.wine.model.vo.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.bouncycastle.asn1.x509.BasicConstraints;

import java.io.Serializable;

/**
 * 角色信息
 *
 * <AUTHOR>
 * @date : 2023/6/17
 */
@Data
@ApiModel("角色信息")
public class RoleCacheVO implements Serializable {

    private static final long serialVersionUID = -6857202918158384344L;

    @ApiModelProperty("角色id")
    private Long id;

    @ApiModelProperty("角色名称")
    private String name;

    @ApiModelProperty("人数")
    private Integer num;

    @ApiModelProperty("是否可以编辑")
    private Boolean editAble;

    public static RoleCacheVO ofAllRole(Integer num) {
        RoleCacheVO vo = new RoleCacheVO();
        vo.setId(0L);
        vo.setName("全部");
        vo.setNum(num);
        vo.setEditAble(Boolean.FALSE);
        return vo;
    }
}
