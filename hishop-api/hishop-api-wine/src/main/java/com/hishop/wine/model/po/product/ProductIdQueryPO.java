package com.hishop.wine.model.po.product;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询产品id po
 *
 * <AUTHOR>
 * @date : 2023/7/4
 */
@Data
public class ProductIdQueryPO {

    @ApiModelProperty(value = "筛选值")
    private String searchValue;

    /**
     * {@link com.hishop.wine.enums.ProductTypeEnums}
     */
    @ApiModelProperty(value = "产品类型 1-商品 2-礼品 ")
    private Integer productType;

    @ApiModelProperty(value = "产品分类id")
    private Long productCategoryId;

}
