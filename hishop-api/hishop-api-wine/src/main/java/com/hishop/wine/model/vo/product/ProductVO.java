package com.hishop.wine.model.vo.product;

import com.hishop.common.annotation.log.OperationColumn;
import com.hishop.common.pojo.media.VideoBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 产品表 返回对象
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
@Data
@ApiModel(value = "ProductVO", description = "产品表返回对象")
public class ProductVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "产品类型 1-商品 2-礼品")
    @OperationColumn(value = "产品类型", expression = "#productType == 1 ? '商品' : '礼品'")
    private Integer productType;

    @ApiModelProperty(value = "产品名称")
    private String productName;

    @ApiModelProperty(value = "产品编码")
    private String productCode;

    @ApiModelProperty(value = "产品单位")
    private String productUnit;

    @ApiModelProperty(value = "产品分类id")
    @OperationColumn(ignore = true)
    private Long productCategoryId;

    @ApiModelProperty("分类名称")
    private String productCategoryName;

    @ApiModelProperty(value = "市场价格")
    private BigDecimal marketPrice;

    @ApiModelProperty(value = "产品图片列表")
    private List<String> productImgList;

    @ApiModelProperty(value = "产品视频")
    private VideoBean mainVideo;

    @ApiModelProperty(value = "产品详情图列表")
    private List<String> productDetailImgList;

}
