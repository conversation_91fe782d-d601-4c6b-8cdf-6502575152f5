package com.hishop.wine.model.po.address;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Date;

/**   
 * 地址库表 新增入参对象
 * @author: HuBiao
 * @date: 2023-07-17
 */

@Data
@ApiModel(value = "BaseAddressCreatePO", description = "地址库表新增入参对象")
public class BaseAddressCreatePO {

    @ApiModelProperty(value = "联系人", required = true)
    @NotBlank(message = "请填写联系人")
    @Size(max = 10, message = "联系人不能超过10字")
    private String contacts;

    @ApiModelProperty(value = "联系电话", required = true)
    @NotBlank(message = "请填写联系电话")
    @Pattern(regexp = "^1[3|4|5|6|7|8|9][0-9]\\d{8}$", message = "手机号格式不正确")
    private String contactsPhone;

    @ApiModelProperty(value = "最后一级的id", required = true)
    @NotNull(message = "请选择街道")
    private Integer streetId;

    @ApiModelProperty(value = "详细地址", required = true)
    @NotBlank(message = "请填写详细地址")
    private String address;

    @ApiModelProperty(value = "邮编")
    private String postalCode;

    @ApiModelProperty(value = "是否默认发货地址")
    private Boolean izDefaultSendAddress;

    @ApiModelProperty(value = "是否默认收货地址")
    private Boolean izDefaultReceiveAddress;

}
