package com.hishop.wine.model.po.setting;

import cn.hutool.core.lang.Assert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date : 2023/7/19
 */
@Data
@ApiModel(value = "LogisticsSettingPO", description = "物流配置参数")
public class LogisticsSettingPO {

    @ApiModelProperty(value = "appKey", required = true)
    @NotBlank(message = "appKey不能为空")
    private String appKey;

    @ApiModelProperty(value = "appSecret", required = true)
    @NotBlank(message = "appSecret不能为空")
    private String appSecret;

    public void check() {
        Assert.isTrue(!appKey.contains("*") && !appSecret.contains("*"), "appKey或appSecret不能包含*号");
    }

}
