package com.hishop.wine.api;

import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.po.logisticsCode.BatchLogisticsCodeStatusPo;
import com.hishop.wine.model.po.logisticsCode.LogisticsCodeFeignPo;
import com.hishop.wine.model.vo.logisticsCode.LogisticsCodeVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;
import java.util.Set;

/**
 * @description: 物流码微服务接口
 * @author: chenzw
 * @date: 2024/7/10 15:37
 */
@FeignClient(name = "basic-system", contextId = "hishop-wine-logisticsCode", url = "${feign.url.basic-system:basic-system}", path = "/wine/logisticsCode")
public interface LogisticsCodeFeign {

    /**
     * 获取物流码信息
     * @param logisticsCode 物流码
     * @return 物流码信息
     */
    @GetMapping("/getLogisticsCodeInfo")
    ResponseBean<LogisticsCodeVo> getLogisticsCodeInfo(@RequestParam String logisticsCode);

    /**
     * 批量更新物流码使用状态
     * @param po 入参
     * @return 已使用的物流码
     */
    @PostMapping("/batchUpdateLogisticsCodesStatus")
    ResponseBean<Set<String>> batchUpdateLogisticsCodesStatus(@RequestBody @Valid BatchLogisticsCodeStatusPo po);

    /**
     * 根据批次id获取物流码信息
     * @param po 入参
     * @return 物流码信息
     */
    @PostMapping("/getLogisticsCodeByFileImportIds")
    ResponseBean<List<LogisticsCodeVo>> getLogisticsCodeByFileImportIds(@RequestBody @Valid LogisticsCodeFeignPo po);

    /**
     * 批量更新物流码未使用状态
     * @param po 入参
     * @return 已使用的物流码
     */
    @PostMapping("/batchUpdateLogisticsCodesStatusNoUsed")
    ResponseBean<Void> batchUpdateLogisticsCodesStatusNoUsed(@RequestBody @Valid BatchLogisticsCodeStatusPo po);
}
