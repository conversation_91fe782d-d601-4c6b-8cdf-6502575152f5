package com.hishop.wine.model.po.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新增地区入参
 *
 * <AUTHOR>
 * @date : 2023/7/17
 */
@Data
@ApiModel(value = "DistrictCreatePO", description = "新增地区入参")
public class DistrictCreatePO {

    @NotBlank(message = "地区名称不能为空")
    @ApiModelProperty(value = "地区名称", required = true)
    private String name;

    @ApiModelProperty(value = "上级id 如果是新增一级可不传")
    private Integer parentId;

}
