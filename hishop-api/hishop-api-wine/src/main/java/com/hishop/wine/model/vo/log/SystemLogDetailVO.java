package com.hishop.wine.model.vo.log;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.hishop.common.annotation.Desensitized;
import com.hishop.common.enums.SensitiveTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**   
 * 操作日志表 返回对象
 * @author: HuBiao
 * @date: 2023-08-01
 */

@Data
@ApiModel(value = "SystemLogVO", description = "操作日志表返回对象")
public class SystemLogDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;
	
    @ApiModelProperty(value = "主键id")
	private Long id;

    @ApiModelProperty(value = "操作类型 CREATE/UPDATE/DELETE")
    private String operationType;
    
    @ApiModelProperty(value = "模块编码")
	private String moduleCode;

    @ApiModelProperty("操作账号")
    private String operatorUsername;

    @ApiModelProperty("操作手机号")
    @Desensitized(type = SensitiveTypeEnum.MOBILE)
    private String operatorMobile;

    @ApiModelProperty("操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("操作名称")
    private String operationName;

    @ApiModelProperty("业务描述")
    private String businessDesc;

    @ApiModelProperty("变动情况")
    private JSONArray changeDataList;
    

}
