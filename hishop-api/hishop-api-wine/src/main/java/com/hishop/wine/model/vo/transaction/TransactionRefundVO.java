package com.hishop.wine.model.vo.transaction;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;


/**
 * 发起退款返回值
 *
 * <AUTHOR>
 * @date : 2023/6/28
 */
@Data
@ApiModel(value = "TransactionRefundVO", description = "发起退款返回值")
public class TransactionRefundVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("内部交易id")
    private Long transactionId;

    @ApiModelProperty("内部交易流水号")
    private String transactionNo;

    public static TransactionRefundVO of(Long transactionId, String transactionNo) {
        TransactionRefundVO vo = new TransactionRefundVO();
        vo.setTransactionId(transactionId);
        vo.setTransactionNo(transactionNo);
        return vo;
    }

}
