package com.hishop.wine.model.vo.transaction;

import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;


/**
 * 交易汇总
 *
 * <AUTHOR>
 * @date : 2023/6/28
 */
@Data
@ApiModel(value = "TransactionSummaryVO", description = "交易汇总vo")
public class TransactionSummaryVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty("业务编码")
    private String bizCode;

    @ApiModelProperty("交易明细")
    private List<TransactionInfoVO> infoList;

    public static TransactionSummaryVO of(List<TransactionInfoVO> infoList) {
        if (CollectionUtils.isEmpty(infoList)) {
            return ofEmpty();
        }
        TransactionSummaryVO vo = new TransactionSummaryVO();
        vo.setTotalAmount(infoList.stream().map(TransactionInfoVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        vo.setBizCode(infoList.get(0).getBizCode());
        vo.setInfoList(infoList);
        return vo;
    }

    public static TransactionSummaryVO ofEmpty() {
        TransactionSummaryVO vo = new TransactionSummaryVO();
        vo.setTotalAmount(BigDecimal.ZERO);
        vo.setBizCode(StrUtil.EMPTY);
        vo.setInfoList(Collections.EMPTY_LIST);
        return vo;
    }

}
