package com.hishop.wine.model.po.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 产品分类表 新增入参对象
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
@Data
@ApiModel(value = "ProductCategoryCreatePO", description = "产品分类表新增入参对象")
public class ProductCategoryCreatePO {

    @ApiModelProperty(value = "分类名称", required = true)
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 20, message = "分类名称最大长度为20")
    private String categoryName;

    @ApiModelProperty(value = "排序, 默认0")
    private Integer orderNum;

    @ApiModelProperty(value = "状态 0：禁用  1：正常, 默认为正常")
    private Boolean status = Boolean.TRUE;
}
