package com.hishop.wine.model.vo.setting;

import cn.hutool.core.util.StrUtil;
import com.hishop.common.annotation.Desensitized;
import com.hishop.common.enums.SensitiveTypeEnum;
import com.hishop.setting.AbstractSetting;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 短信配置
 *
 * <AUTHOR>
 * @date : 2023/7/12
 */
@Data
@ApiModel(value = "SmsSettingVO", description = "短信配置")
public class SmsSettingVO extends AbstractSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "appKey")
    @Desensitized(type = SensitiveTypeEnum.ALL)
    private String appKey;

    @ApiModelProperty(value = "appSecret")
    @Desensitized(type = SensitiveTypeEnum.ALL)
    private String appSecret;

    @Override
    protected void initDefault() {
        this.setAppKey(StrUtil.EMPTY);
        this.setAppSecret(StrUtil.EMPTY);
    }
}
