package com.hishop.wine.model.vo.payment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 支付设置下拉框 返回对象
 *
 * @author: HuBiao
 * @date: 2023-07-18
 */
@Data
@ApiModel(value = "PaymentSettingSelectVO", description = "支付设置下拉框")
public class PaymentSettingSelectVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("支付设置id")
    private Long id;

    @ApiModelProperty(value = "支付方式名称")
    private String paymentName;

    @ApiModelProperty(value = "支付方式类型")
    private String paymentType;

    @ApiModelProperty(value = "线下支付渠道")
    private String offlineChannel;
}
