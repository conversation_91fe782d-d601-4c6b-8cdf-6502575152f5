package com.hishop.wine.model.vo.storeBar;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 底部导航返回对象
 *
 * @author: Hubiao
 * @date: 2023-07-18
 */
@Data
@ApiModel(value = "StoreBarVO", description = "底部导航返回值")
public class StoreBarVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty("背景颜色")
    private String bgColor;

    @ApiModelProperty("字体颜色")
    private String fontColor;

    @ApiModelProperty("选中颜色")
    private String selectColor;

    @ApiModelProperty(value = "装修设置json串")
    private String barJson;

}
