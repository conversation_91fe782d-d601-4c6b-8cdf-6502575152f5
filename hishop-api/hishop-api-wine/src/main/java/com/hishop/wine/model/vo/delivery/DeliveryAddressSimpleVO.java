package com.hishop.wine.model.vo.delivery;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**   
 * 收货地址表 返回对象
 * @author: HuBiao
 * @date: 2023-06-29
 */

@Data
@ApiModel(value = "DeliveryAddressSimpleVO", description = "收货地址列表返回对象")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DeliveryAddressSimpleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("收货地址id")
    private Long id;

    @ApiModelProperty(value = "收货人")
    private String consignee;

    @ApiModelProperty(value = "收货人手机号")
    private String consigneePhone;

    @ApiModelProperty(value = "详细省市区")
    private String district;

    @ApiModelProperty(value = "最后一级区划id")
    private Integer regionId;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "是否是默认地址 true-是 false 不是")
    private Boolean izDefault;

    @ApiModelProperty(value = "是否默认发货地址")
    private Boolean izDefaultSendAddress;

    @ApiModelProperty(value = "是否默认收货地址")
    private Boolean izDefaultReceiveAddress;
}
