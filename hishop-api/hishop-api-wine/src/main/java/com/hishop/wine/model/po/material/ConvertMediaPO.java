package com.hishop.wine.model.po.material;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(value = "MaterialCreatePO", description = "转码参数")
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConvertMediaPO {
    @JsonProperty("event_type")
    private String eventType;
    @JsonProperty("transcode_info")
    private TranscodeInfoPO transcodeInfo;

    @JsonProperty("thumbnail_info")
    private ThumbnailInfoPO thumbnailInfoPO;
}
