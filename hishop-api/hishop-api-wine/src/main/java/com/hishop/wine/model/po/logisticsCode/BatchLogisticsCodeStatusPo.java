package com.hishop.wine.model.po.logisticsCode;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/07/16/ $
 * @description:
 */
@Data
@ApiModel("批量更新物流码使用状态请求参数")
public class BatchLogisticsCodeStatusPo {

    @ApiModelProperty(value = "物流码列表")
    @NotEmpty(message = "物流码列表不能为空")
    private List<String> codeList;
}
