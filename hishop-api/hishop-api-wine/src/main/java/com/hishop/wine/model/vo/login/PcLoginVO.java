package com.hishop.wine.model.vo.login;

import com.hishop.common.pojo.login.LoginResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * pc端登录返回值
 *
 * <AUTHOR>
 * @date : 2023/7/6
 */
@Data
@ApiModel(value = "PcLoginVO", description = "PC端登录返回值")
public class PcLoginVO extends LoginResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("用户信息")
    private PcLoginUserVO userInfo;

    @ApiModelProperty("默认跳转路径")
    private String defaultUrl;

    @ApiModelProperty("授权模块列表")
    private AuthModuleVO authModule;

}
