package com.hishop.wine.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *  串联
 */
@Data
public class SeriesDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "串联名称")
    private String name;

    @ApiModelProperty(value = "数据")
    private List<DataDto> data;

}
