package com.hishop.wine.api;

import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.po.wechat.WechatCodeCreatePO;
import com.hishop.wine.model.po.wechat.WechatSavePO;
import com.hishop.wine.model.vo.wechat.WechatCodeVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2023/6/25
 */
@FeignClient(name = "basic-system",
        contextId = "hishop-wine-weiXinCode",
        url = "${feign.url.basic-system:basic-system}",
        path = "/wine/wechat/code")
public interface WechatCodeFeign {

    /**
     * 保存微信二维码，主要是为了不每次生成上传等
     * <AUTHOR>
     * @date 2023/6/26
     */
    @PostMapping("/save")
    ResponseBean<Void> saveWechatCode(@RequestBody WechatSavePO savePO);

    /**
     * 根据key获取微信二维码信息
     * @param codeKey 二维码唯一key
     * @return 微信二维码对象
     */
    @GetMapping("/getByKey")
    ResponseBean<WechatCodeVO> getByKey(@RequestParam(name = "codeKey") String codeKey);

    @ApiOperation(value = "生成小程序二维码(ma=mini-app)", httpMethod = "POST")
    @PostMapping("/genMaCode")
    ResponseBean<String> genMaCode(@RequestBody @Valid WechatCodeCreatePO createPo);

    @ApiOperation(value = "生成小程序二维码(ma=mini-app)", httpMethod = "POST")
    @PostMapping("/genMaCodeByModule")
    ResponseBean<String> genMaCodeByModule(@RequestBody @Valid WechatCodeCreatePO createPo);

}
