package com.hishop.wine.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 微信相关枚举
 *
 * <AUTHOR>
 * @date 2023/6/20
 */
public class WechatEnum {

    public enum WxCodeTypeEnum {

        MINI_APP(1, "小程序二维码"),
        OFFICIAL(2, "公众号二维码");

        private final Integer code;
        private final String desc;

        WxCodeTypeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum WxCodeFromEnum {

        BASIC_SYSTEM("基础库"),
        SCAN_MARKETING("扫码营销"),
        FANS_CLUB("粉丝俱乐部");

        private final String desc;

        WxCodeFromEnum(String desc) {
            this.desc = desc;
        }


        public String getDesc() {
            return desc;
        }
    }

    /**
     * 微信网页应用二维码类型
     */
    @Getter
    public enum WxWebQuCodeTypeEnum {

        /**
         * 绑定微信用户的二维码
         */
        QRCODE_FOR_BIND(1, "snsapi_login"),
        /**
         * 登录用的二维码
         */
        QRCODE_FOR_LOGIN(2, "snsapi_login");

        private final Integer type;
        private final String scope;

        WxWebQuCodeTypeEnum(Integer type, String scope) {
            this.type = type;
            this.scope = scope;
        }

        public static WxWebQuCodeTypeEnum getByType(Integer type) {
            return Arrays.stream(values())
                    .filter(e -> e.getType().equals(type))
                    .findFirst()
                    .orElse(null);
        }
    }

}
