package com.hishop.wine.model.vo.member;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 会员信息
 *
 * <AUTHOR>
 * @date : 2023/7/25
 */
@Data
@ApiModel(value = "MemberSelectVO", description = "会员下拉选择vo")
public class MemberSelectVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("会员id")
    @ExcelIgnore
    private Long id;

    @ApiModelProperty("昵称")
    @ExcelProperty("昵称")
    private String nickName;

    @ApiModelProperty("手机号")
    @ExcelProperty("手机号")
    private String mobile;

}
