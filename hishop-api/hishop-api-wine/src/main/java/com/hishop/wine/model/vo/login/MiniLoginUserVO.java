package com.hishop.wine.model.vo.login;

import com.hishop.common.annotation.Desensitized;
import com.hishop.common.enums.SensitiveTypeEnum;
import com.hishop.wine.model.vo.sale.SaleAreaSimpleVo;
import com.hishop.wine.model.vo.terminate.MiniTerminateVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 小程序登录用户信息
 *
 * <AUTHOR>
 * @date : 2023/7/7
 */
@Data
@ApiModel(value = "MiniLoginUserVO", description = "小程序登录用户信息")
public class MiniLoginUserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("昵称")
    private String nickName;

    @ApiModelProperty("头像")
    private String avatarUrl;

    @ApiModelProperty("手机号")
    @Desensitized(type = SensitiveTypeEnum.MOBILE)
    private String mobile;

    @ApiModelProperty("身份类型 1-管理员 2-消费者 3-经销商 4-终端/门店 5-业务员")
    private Integer identityType;

    @ApiModelProperty("头衔名称")
    private String rankName;

    @ApiModelProperty("门店信息")
    private MiniTerminateVo terminate;

    @ApiModelProperty(value = "销售区域列表")
    private List<List<SaleAreaSimpleVo>> saleAreaList;

    @ApiModelProperty("部门名称")
    private String departmentName;

    @ApiModelProperty("电话")
    private String phone;
}
