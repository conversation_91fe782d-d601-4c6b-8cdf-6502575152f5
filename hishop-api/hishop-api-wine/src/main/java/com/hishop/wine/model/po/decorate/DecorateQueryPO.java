package com.hishop.wine.model.po.decorate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**   
 * 装修表查询入参对象
 * @author: chenpeng
 * @date: 2023-07-11
 */

@Data
@ApiModel(value = "DecorateQueryPO", description = "装修表查询入参对象")
public class DecorateQueryPO {

    @ApiModelProperty(value = "小程序appId")
    private String appId;

    @ApiModelProperty(value = "系统类型(扫码营销：scan_marketing,粉丝俱乐部：fans_club,封坛酒：fengtan_wine)")
    @NotEmpty(message = "系统类型不能为空")
    private String moduleCode;

    @ApiModelProperty(value = "装修类型(首页装修：homePage，主题配色：theme，个人中心：userCenter，底部导航：navigation)")
    @NotEmpty(message = "装修类型不能为空")
    private String decorateType;

}
