package com.hishop.wine.model.vo.micropage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "MicropageVO", description = "微页面vo")
public class MicropageDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "分组id")
    private Long categoryId;

    @ApiModelProperty(value = "发布状态 1-草稿 2-上架")
    private Integer status;

    @ApiModelProperty(value = "微页面配置json")
    private String settingJson;
}
