package com.hishop.wine.model.vo.points;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 会员积分明细 返回对象
 *
 * @author: LiGuoQiang
 * @date: 2023-06-25
 */
@Data
@ApiModel(value = "MemberPointsInnerDetailsVO", description = "积分明细")
public class MemberPointsInnerDetailsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户ID")
    private Long userId;
    @ApiModelProperty(value = "身份类型 1-管理员 2-消费者 3-经销商 4-终端")
    private Integer identityType;
    @ApiModelProperty(value = "身份类型 1-管理员 2-消费者 3-经销商 4-终端")
    private String identityTypeDesc;
    @ApiModelProperty(value = "用户昵称")
    private String nickName;
    @ApiModelProperty(value = "用户手机号码")
    private String userPhone;
    @ApiModelProperty(value = "变更类型。-1：减积分/积分消耗；0：积分清零；1：加积分(积分发放)。方便区分")
    private Integer modifiedType;
    @ApiModelProperty(value = "变更类型。-1：减积分/积分消耗；0：积分清零；1：加积分(积分发放")
    private String modifiedTypeDesc;
    @ApiModelProperty(value = "变更的积分。通过正负值表示增减，方便统计")
    private Integer modifiedPoints;
    @ApiModelProperty(value = "来源系统。1：基础库；2：积分商城；3：扫码营销；4：粉丝俱乐部。积分清零固定为0")
    private Integer fromSystem;
    @ApiModelProperty(value = "来源系统。1：基础库；2：积分商城；3：扫码营销；4：粉丝俱乐部。积分清零固定为0")
    private String fromSystemDesc;
    @ApiModelProperty(value = "具体的业务类型，枚举与来源系统保持一致。积分清零固定为0")
    private Integer bizType;
    @ApiModelProperty(value = "具体的业务类型描述，业务系统赋值")
    private String bizTypeDesc;
    @ApiModelProperty(value = "修改说明")
    private String modifiedRemark;
    @ApiModelProperty(value = "创建时间，即积分变更时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
