package com.hishop.wine.model.po.logistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date : 2023/8/25
 */
@Data
@ApiModel("物流查询参数")
public class LogisticsQueryPO {

    @ApiModelProperty(value = "快递公司编码")
    @NotBlank(message = "快递公司编码不能为空")
    private String shipperCode;

    @ApiModelProperty(value = "物流单号")
    @NotBlank(message = "物流单号不能为空")
    private String logisticCode;


    @ApiModelProperty(value = "手机尾号4位 （顺丰必传）")
    private String phoneLastNumber;

}
