package com.hishop.wine.model.vo.product;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品分页vo
 *
 * <AUTHOR>
 * @date : 2023/6/19
 */
@Data
@ApiModel(value = "ProductPageVO", description = "产品分页对象")
public class ProductPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("产品id")
    private Long id;

    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("产品图片")
    private String productImg;

    @ApiModelProperty("产品编码")
    private String productCode;

    @ApiModelProperty("市场价")
    private BigDecimal marketPrice;

    @ApiModelProperty("产品类型")
    private String productTypeName;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
