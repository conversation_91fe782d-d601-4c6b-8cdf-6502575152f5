package com.hishop.wine.model.po.material;

import com.hishop.common.pojo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @description: 资源查询类
 * @author: tomliu
 * @create: 2022/06/25 13:20
 **/
@Data
@ApiModel("资源查询对象")
public class MaterialQueryPO extends PageParam {

    /**
     * 资源分组id
     */
    @ApiModelProperty(value = "资源分组id", required = true)
    @NotNull(message = "资源分组id不能为空")
    private Long materialCategoryId;

    @ApiModelProperty("标题关键字")
    private String titleKeywords;

}
