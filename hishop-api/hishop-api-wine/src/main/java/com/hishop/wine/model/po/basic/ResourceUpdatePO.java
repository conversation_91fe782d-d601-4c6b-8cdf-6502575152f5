package com.hishop.wine.model.po.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 资源表 更新入参对象
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */

@Data
@ApiModel(value = "ResourceUpdatePO", description = "资源表更新入参对象")
public class ResourceUpdatePO {

    @ApiModelProperty(value = "主键id", required = true)
    @NotNull(message = "资源id不能为空")
    private Long id;

    @ApiModelProperty(value = "上级资源id, 一级资源为0")
    private Long parentId;

    @ApiModelProperty(value = "资源名称", required = true)
    @NotBlank(message = "资源名称不能为空")
    @Size(max = 20, message = "资源名称最大长度为20")
    private String name;

    @ApiModelProperty(value = "资源编码", required = true)
    @NotBlank(message = "请输入资源编码")
    @Size(max = 20, message = "资源编码最大长度为20")
    private String privilege;

    @ApiModelProperty(value = "类型   1：目录  2：菜单 3：按钮", required = true)
    @NotNull(message = "请选择资源类型")
    private Integer type;

    @ApiModelProperty(value = "菜单图标")
    private String icon;

    @ApiModelProperty(value = "排序")
    private Integer orderNum;


}
