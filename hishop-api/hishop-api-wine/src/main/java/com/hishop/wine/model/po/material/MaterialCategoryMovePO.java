package com.hishop.wine.model.po.material;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 素材库分组 移动入参对象
 *
 * @author: HuBiao
 * @date: 2023-06-20
 */
@Data
@ApiModel(value = "MaterialCategoryMovePO", description = "素材库分组移动入参对象")
public class MaterialCategoryMovePO {

    @ApiModelProperty(value = "待移动的分组id集合", required = true)
    @NotNull(message = "待移动的分组id集合不能为空")
    @Size(min = 1, message = "请至少选择一个待移动的分组")
    private List<Long> ids;

    @ApiModelProperty(value = "目标上级分组id", required = true)
    @NotNull(message = "目标上级分组id不能为空")
    private Long topId;
}
