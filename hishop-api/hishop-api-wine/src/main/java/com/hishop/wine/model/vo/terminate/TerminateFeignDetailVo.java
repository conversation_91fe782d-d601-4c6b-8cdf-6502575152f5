package com.hishop.wine.model.vo.terminate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 门店返回对象
 * @author: chenzw
 * @date: 2024/7/6 15:47
 */
@Data
@ApiModel(value = "TerminateFeignDetailVo", description = "门店返回对象")
public class TerminateFeignDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "门店编码")
    private String code;

    @ApiModelProperty(value = "门店名称")
    private String name;

    @ApiModelProperty(value = "类型id")
    private Long terminateTypeId;

    @ApiModelProperty(value = "类型名称")
    private String terminateTypeName;

    @ApiModelProperty(value = "经销商id")
    private Long dealerId;

    @ApiModelProperty(value = "经销商名称")
    private String dealerName;

    @ApiModelProperty(value = "业务员id")
    private Long businessUserId;

    @ApiModelProperty(value = "业务员名称")
    private String businessUserName;

    @ApiModelProperty(value = "业务员电话")
    private String businessUserPhone;

    @ApiModelProperty(value = "省份id")
    private Integer provinceId;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "城市id")
    private Integer cityId;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "区县id")
    private Integer districtId;

    @ApiModelProperty(value = "区县名称")
    private String districtName;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "标注位置")
    private String markLocation;

    @ApiModelProperty(value = "经度")
    private Double lng;

    @ApiModelProperty(value = "纬度")
    private Double lat;

    @ApiModelProperty(value = "负责人姓名")
    private String dutyName;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "电子邮箱")
    private String mail;

    @ApiModelProperty(value = "门店照片")
    private List<String> photoList;

    @ApiModelProperty(value = "门店电话")
    private String storePhone;

    @ApiModelProperty(value = "营业开始时间")
    private String tradeStartTime;

    @ApiModelProperty(value = "营业结束时间")
    private String tradeEndTime;

    @ApiModelProperty(value = "营业星期")
    private List<String> tradeWeekList;

    @ApiModelProperty(value = "门店介绍")
    private String introduce;

    @ApiModelProperty(value = "门店面积")
    private String terminateSquare;
}
