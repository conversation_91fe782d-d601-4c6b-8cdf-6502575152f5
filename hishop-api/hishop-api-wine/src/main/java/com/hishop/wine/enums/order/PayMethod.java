package com.hishop.wine.enums.order;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 支付方式
 *
 * <AUTHOR>
 * @date 2024/1/9 10:49
 */
@Getter
@AllArgsConstructor
public enum PayMethod {

    /**
     * 在线支付
     */

    ONLINE_PAY("ONLINE_PAY", "在线支付"),

    /**
     * 线下支付
     */
    OFFLINE_PAY("OFFLINE_PAY", "线下支付");

    @EnumValue
    private final String value;

    private final String desc;

    // 静态方法，根据字符串查找对应的枚举值
    public static String fromStringDesc(String value) {
        for (PayMethod method : PayMethod.values()) {
            if (method.value.equals(value)) {
                return method.getDesc();
            }
        }
        // 如果没有匹配的枚举值，你可以选择抛出异常或者返回一个默认值，这里返回null作为示例
        return null;
    }
}
