package com.hishop.wine.model.po.transaction;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hishop.common.pojo.page.PageParam;
import com.hishop.wine.enums.TransactionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 交易明细查询
 *
 * <AUTHOR>
 * @date : 2023/6/28
 */
@Data
@ApiModel(value = "TransactionUpdateStatusPO", description = "更新交易状态入参")
public class TransactionUpdateStatusPO extends PageParam {

    @ApiModelProperty(value = "交易流水")
    private String transactionNo;

    @ApiModelProperty(value = "状态 1-待确认 2-交易成功 3-交易失败")
    private TransactionEnum.Status status;

    @ApiModelProperty(value = "失败原因")
    private String failReason;

    @ApiModelProperty(value = "完成時間")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date finishTime;

}
