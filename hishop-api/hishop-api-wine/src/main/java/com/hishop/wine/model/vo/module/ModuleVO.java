package com.hishop.wine.model.vo.module;

import com.hishop.wine.enums.ModuleEnums;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 子模块返回对象
 *
 * @author: chenpeng
 * @date: 2023-07-14
 */

@Data
@ApiModel(value = "ModuleVO", description = "子模块返回对象")
public class ModuleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "子模块名称(扫码营销：scan_marketing,粉丝俱乐部：fans_club)")
    private String name;

    @ApiModelProperty(value = "子模块编码(扫码营销：scan_marketing,粉丝俱乐部：fans_club)")
    private String code;

    /**
     * 构建基础系统
     *
     * @return 基础系统
     */
    public static ModuleVO ofBasicSystem() {
        ModuleVO moduleVO = new ModuleVO();
        moduleVO.setCode(ModuleEnums.basic_system.name());
        moduleVO.setName(ModuleEnums.basic_system.getDesc());
        return moduleVO;
    }
}
