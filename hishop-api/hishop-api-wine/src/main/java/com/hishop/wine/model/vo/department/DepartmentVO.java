package com.hishop.wine.model.vo.department;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**   
 * @Description: 部门表 返回对象
 * @Author: chenpeng
 * @since: 2023-04-25 10:50:00
 */

@Data
@ApiModel(value = "DepartmentVO", description = "部门表返回对象")
public class DepartmentVO implements Serializable {

    private static final long serialVersionUID = 1L;
	
    @ApiModelProperty(name = "id" , value = "部门id")
	private Long id;
    
    @ApiModelProperty(name = "departmentCode" , value = "部门编号")
	private String departmentCode;
    
    @ApiModelProperty(name = "departmentName" , value = "部门名称")
	private String departmentName;
    
    @ApiModelProperty(name = "parentId" , value = "上级部门ID")
	private Long parentId;

    @ApiModelProperty(name = "sort" , value = "排序")
    private Integer sort;

    private List<DepartmentVO> sunDepartmentList;

}
