package com.hishop.wine.api;

import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.po.points.ChangePointsPO;
import com.hishop.wine.model.po.points.ClearExpirePointsPO;
import com.hishop.wine.model.po.points.MemberPointsQueryPO;
import com.hishop.wine.model.vo.points.MemberPointsDetailsVO;
import com.hishop.wine.model.vo.points.MemberPointsSummaryVO;
import com.hishop.wine.model.vo.points.MemberPointsVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * 会员积分表 微服务接口
 * @author: LiGuoQiang
 * @date: 2023-06-25
 */

@FeignClient(name = "basic-system",
        contextId = "hishop-wine-memberPoints",
        url = "${feign.url.basic-system:basic-system}",
        path = "/wine/memberPoints")
public interface MemberPointsFeign {

    /**
    * 分页查询会员积分列表
    * @author: LiGuoQiang
    * @date: 2023-06-25
    */
    @PostMapping({"/pageList"})
    ResponseBean<PageResult<MemberPointsVO>> pageList(@RequestBody MemberPointsQueryPO qryPo);

    /**
     * 获取用户积分汇总
     * <AUTHOR>
     * @date 2023/6/25
     */
    @PostMapping("/summary")
    ResponseBean<MemberPointsSummaryVO> summary(@RequestBody MemberPointsQueryPO pagePo);

    /**
     * 分页获取 分页查询会员积分明细
     */
    @PostMapping("/pageDetail")
    ResponseBean<PageResult<MemberPointsDetailsVO>> pageDetail(@RequestBody MemberPointsQueryPO pagePo);

    /**
     * 给积分
     */
    @PostMapping("/change")
    ResponseBean<Void> change(@RequestBody ChangePointsPO pointsPo);

    /**
     * 获取用户积分
     * <AUTHOR>
     * @date 2023/6/26
     */
    @GetMapping("/getUserPoints")
    ResponseBean<MemberPointsVO> getUserPoints(@RequestParam("userId") Long userId,
                                               @RequestParam(name = "identityType") Integer identityType);

    /**
     * 查询积分明细
     * @param bizType 业务类型
     * @param bizCode 业务编码
     * @return 积分明细
     */
    @GetMapping("/getByBiz")
    ResponseBean<MemberPointsDetailsVO> getByBiz(@RequestParam("bizType") Integer bizType,
                                                 @RequestParam("bizCode") String bizCode);

    /**
     * 清除过期积分
     * <AUTHOR>
     * @date 2023/8/3
     */
    @PostMapping("/clearExpirePoints")
    ResponseBean<Void> clearExpirePoints(@RequestBody @Valid ClearExpirePointsPO clearExpirePointsPO);
}