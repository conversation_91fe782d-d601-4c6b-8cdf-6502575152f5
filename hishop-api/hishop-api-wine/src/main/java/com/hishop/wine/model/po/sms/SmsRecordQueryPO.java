package com.hishop.wine.model.po.sms;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hishop.common.pojo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**   
 * 短信发送记录表 查询入参对象
 * @author: HuBiao
 * @date: 2023-07-12
 */

@Data
@ApiModel(value = "SmsRecordQueryPO", description = "短信发送记录表查询入参对象")
public class SmsRecordQueryPO extends PageParam {

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty(value = "开始时间。yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "结束时间。yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;


}
