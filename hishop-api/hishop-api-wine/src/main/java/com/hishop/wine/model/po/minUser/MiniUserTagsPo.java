package com.hishop.wine.model.po.minUser;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 小程序用户打标入参
 * <AUTHOR>
 * @since 2024-01-26 17:06:07
 */
@Data
@ApiModel(value = "MiniUserTagsPo", description = "小程序用户打标入参")
public class MiniUserTagsPo {

    @ApiModelProperty(value = "标签id列表")
    @NotNull(message = "标签id列表不能为空")
    @Size(min = 1, message = "请至少选择1个标签")
    @Size(max = 15, message = "最多不能超过15个标签")
    private List<Long> tagIdList;

    @ApiModelProperty(value = "小程序用户选择1个用户")
    @NotNull(message = "小程序用户不能为空")
    @Size(min = 1, message = "请至少选择1个小程序用户")
    private List<Long> idList;
}
