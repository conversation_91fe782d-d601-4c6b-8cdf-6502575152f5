package com.hishop.wine.model.po.minUser;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 *
 */
@Data
@ApiModel(value = "MiniUserRemarkNamePo", description = "PC-备注名")
public class MiniUserRemarkNamePo {

    @ApiModelProperty(value = "小程序用户id")
    @NotNull(message = "小程序用户id不能为空")
    private Long id;

    @ApiModelProperty(value = "备注名")
    private String remarkName;

}
