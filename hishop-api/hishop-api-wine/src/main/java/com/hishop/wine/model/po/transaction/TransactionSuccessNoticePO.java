package com.hishop.wine.model.po.transaction;

import lombok.Data;

import java.util.Date;

/**
 * 交易成功通知业务系统
 *
 * <AUTHOR>
 * @date : 2023/8/4
 */
@Data
public class TransactionSuccessNoticePO {

    /**
     * 模块编码
     */
    private String moduleCode;

    /**
     * 内部交易id
     */
    private Long transactionId;

    /**
     * 第三方交易流水
     */
    private String thirdTransactionNo;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 业务编码
     */
    private String bizCode;

    /**
     * 成交时间
     */
    private Date finishTime;

    /**
     * 交易是否成功
     */
    private Boolean success;

    /**
     * 交易状态 {@link com.hishop.wine.enums.TransactionEnum.Status}
     */
    private Integer status;
}
