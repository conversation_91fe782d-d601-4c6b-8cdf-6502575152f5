package com.hishop.wine.model.vo.moderation;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**   
 * 内容审核表 返回对象
 * @author: HuBiao
 * @date: 2023-09-11
 */

@Data
@ApiModel(value = "ContentModerationVO", description = "内容审核表返回对象")
public class ContentModerationVO implements Serializable {

    private static final long serialVersionUID = 1L;
	
    @ApiModelProperty(value = "主键id")
	private Long id;
    
    @ApiModelProperty(value = "模块编码")
	private String moduleCode;
    
    @ApiModelProperty(value = "业务类型")
	private String bizType;
    
    @ApiModelProperty(value = "业务编码")
	private String bizCode;
    
    @ApiModelProperty(value = "业务描述")
	private String bizDesc;
    
    @ApiModelProperty(value = "待审核-WAITING 审核中-AUDITING SUCCESS-审核通过 FAIL-审核失败")
	private String status;
    
    @ApiModelProperty(value = "创建者ID")
	private Long createBy;
    
    @ApiModelProperty(value = "创建时间")
	private Date createTime;
    
    @ApiModelProperty(value = "更新者ID")
	private Long updateBy;
    
    @ApiModelProperty(value = "更新时间")
	private Date updateTime;
    

}
