package com.hishop.wine.model.vo.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 管理员列表返回参数
 *
 * <AUTHOR>
 * @date : 2023/6/21
 */
@Data
@ApiModel(value = "AdminDetailVO", description = "管理员详情返回参数")
public class AdminDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "管理员id")
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "头像")
    private String icon;

    @ApiModelProperty(value = "角色id")
    private Long roleId;

    @ApiModelProperty("管理员状态 true-启用 false-禁用")
    private Boolean status;

}
