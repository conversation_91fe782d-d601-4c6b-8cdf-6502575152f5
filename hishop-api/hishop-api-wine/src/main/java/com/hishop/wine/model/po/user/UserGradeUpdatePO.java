package com.hishop.wine.model.po.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;


@Data
@ApiModel(value = "UserGradePO", description = "用户会员等级查询入参")
public class UserGradeUpdatePO {

    @ApiModelProperty(value = "电话")
    private String phone;

    @ApiModelProperty(value = "会员等级ID")
    private Long gradeId;

    @ApiModelProperty("会员等级容量")
    private BigDecimal gradeCapacity;
}
