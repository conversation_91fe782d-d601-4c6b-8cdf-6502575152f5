package com.hishop.wine.api;

import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.vo.logistics.LogisticsCompanyVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import javax.validation.Valid;
import java.util.List;

/**
 * 物流公司 微服务接口
 *
 * @author: guoyufeng
 * @date: 2024-01-10
 */
@FeignClient(name = "basic-system", contextId = "hishop-wine-logistics", url = "${feign.url.basic-system:basic-system}", path = "/wine/logisticsCompany")
public interface LogisticsCompanyFeign {

    /**
     * 物流查询
     * @return 物流信息
     */
    @PostMapping("/pc/listLogisticsCompany")
    ResponseBean<List<LogisticsCompanyVO>> listLogisticsCompany();
}