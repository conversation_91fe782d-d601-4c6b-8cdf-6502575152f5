package com.hishop.wine.model.vo.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/7/11
 */
@Data
@ApiModel(value = "RegionVO", description = "大区返回值")
public class RegionVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("大区名称")
    private String regionName;

    @ApiModelProperty("地区集合")
    private List<DistrictVO> districtList;

}
