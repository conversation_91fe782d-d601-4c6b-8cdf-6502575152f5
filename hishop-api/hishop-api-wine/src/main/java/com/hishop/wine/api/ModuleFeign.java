package com.hishop.wine.api;

import com.hishop.common.response.ResponseBean;
import com.hishop.moderation.model.*;
import com.hishop.wine.model.po.moderation.ContentModerationCreatePO;
import com.hishop.wine.model.vo.module.ModuleVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * 模块
 * <AUTHOR>
 * @date 2024/7/17
 */
@FeignClient(name = "basic-system",
        contextId = "hishop-wine-module",
        url = "${feign.url.basic-system:basic-system}",
        path = "/wine/module")
public interface ModuleFeign {

    /**
     * PC-查询模块列表(包含基础模块)
     * @return 模块信息
     */
    @GetMapping({"/pc/listAuthModuleIncludeBasic"})
    ResponseBean<List<ModuleVO>> listAuthModuleIncludeBasic();
}
