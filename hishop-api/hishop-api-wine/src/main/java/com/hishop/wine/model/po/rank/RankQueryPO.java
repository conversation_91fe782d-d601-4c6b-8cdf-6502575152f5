package com.hishop.wine.model.po.rank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.hishop.common.pojo.page.PageParam;

/**
 * 头衔表 查询入参对象
 *
 * @author: HuBiao
 * @date: 2023-07-25
 */
@Data
@ApiModel(value = "RankQueryPO", description = "头衔表查询入参对象")
public class RankQueryPO extends PageParam {

    @ApiModelProperty(value = "头衔名称")
    private String rankName;

    @ApiModelProperty(value = "模块编码")
    private String moduleCode;

}
