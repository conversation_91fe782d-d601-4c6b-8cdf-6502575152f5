package com.hishop.wine.api;

import com.hishop.common.pojo.IdBatchPO;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.vo.delivery.DeliveryAddressVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 收货地址表 微服务接口
 *
 * @author: HuBiao
 * @date: 2023-06-29
 */

@FeignClient(name = "basic-system", contextId = "hishop-wine-deliveryAddress", url = "${feign.url.basic-system:basic-system}", path = "/wine/deliveryAddress")
public interface DeliveryAddressFeign {

    /**
     * 查询收货地址详情
     *
     * @param id 收货地址id
     * @return 收货地址详情
     */
    @GetMapping("/mini/getById")
    ResponseBean<DeliveryAddressVO> getById(@RequestParam(name = "id") Long id);

    /**
     * 查询收货地址详情
     *
     * @param idBatchPO 收货地址id的集合
     * @return 收货地址集合
     */
    @ApiOperation(value = "feign-查询收货地址详情", httpMethod = "POST")
    @PostMapping("/inner/getByIds")
    ResponseBean<List<DeliveryAddressVO>> getByIds(@RequestBody IdBatchPO<Long> idBatchPO);

    /**
     * 获取默认收货地址
     *
     * @param userId 用户id
     * @return 收货地址详情
     */
    @GetMapping("/inner/getDefaultAddress")
    ResponseBean<DeliveryAddressVO> getDefaultAddress(@RequestParam(name = "userId") Long userId);

}