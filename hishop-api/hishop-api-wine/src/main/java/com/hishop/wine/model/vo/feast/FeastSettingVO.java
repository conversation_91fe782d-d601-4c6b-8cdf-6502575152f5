package com.hishop.wine.model.vo.feast;

import cn.hutool.core.util.StrUtil;
import com.hishop.common.annotation.Desensitized;
import com.hishop.common.enums.SensitiveTypeEnum;
import com.hishop.setting.AbstractSetting;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**   
 * 宴席酒配置返回对象
 * @author: guoyufeng
 * @date: 2024-07-11
 */

@Data
@ApiModel(value = "FeastSettingVO", description = "宴席酒配置")
public class FeastSettingVO extends AbstractSetting implements Serializable {

    private static final long serialVersionUID = 1L;
	
    @ApiModelProperty("宴席酒appId")
    private String appId;

    @ApiModelProperty("要打开的小程序版本。正式版为 release，体验版为 trial，开发版为 develop。默认是正式版。")
    private String envVersion;


    @Override
    protected void initDefault() {
        this.setAppId(StrUtil.EMPTY);
        this.setEnvVersion(StrUtil.EMPTY);
    }

}
