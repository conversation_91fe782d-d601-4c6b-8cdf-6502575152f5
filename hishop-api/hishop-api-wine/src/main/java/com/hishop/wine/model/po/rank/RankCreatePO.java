package com.hishop.wine.model.po.rank;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**   
 * 头衔表 新增入参对象
 * @author: HuBiao
 * @date: 2023-07-25
 */

@Data
@ApiModel(value = "RankCreatePO", description = "头衔表新增入参对象")
public class RankCreatePO {

    @ApiModelProperty(value = "主键id", required = true)
    @NotNull(message = "主键id不能为空")
    private Long id;

    @ApiModelProperty(value = "头衔名称", required = true)
    @NotBlank(message = "头衔名称不能为空")
	private String rankName;

    @ApiModelProperty(value = "模块编码", required = true)
    @NotBlank(message = "模块编码不能为空")
	private String moduleCode;

}
