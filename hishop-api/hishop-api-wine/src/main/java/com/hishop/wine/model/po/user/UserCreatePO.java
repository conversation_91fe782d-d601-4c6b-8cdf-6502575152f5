package com.hishop.wine.model.po.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户入参
 *
 * <AUTHOR>
 * @date : 2023/9/7
 */
@Data
@ApiModel(value = "UserCreatePO", description = "用户入参")
public class UserCreatePO {

    @ApiModelProperty(value = "电话")
    private String mobile;

    @ApiModelProperty(value = "用户名称")
    private String userName;
}
