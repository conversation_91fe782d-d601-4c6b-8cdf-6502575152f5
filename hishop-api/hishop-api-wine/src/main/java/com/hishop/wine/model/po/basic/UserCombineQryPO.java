package com.hishop.wine.model.po.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/6/30
 */
@Data
@ApiModel(value = "UserCombineQryPO", description = "用户组合信息查询参数，可能会关联用户+身份+微信信息")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserCombineQryPO {

    @ApiModelProperty(value = "昵称-模糊查询")
    private String nickNameLike;
    @ApiModelProperty(value = "手机号-模糊查询")
    private String mobileLike;
    @ApiModelProperty(value = "搜索关键字-模糊查询，目前支持用户名、昵称、手机号")
    private String searchKey;

}
