package com.hishop.wine.model.vo.dealer;

import com.hishop.wine.model.vo.sale.SaleAreaSimpleVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 经销商表(Dealer)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-04 15:53:16
 */
@Data
@ApiModel(value = "DealerVo", description = "经销商表")
public class DealerDetailVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "经销商ID=主键")
    private Long id;

    @ApiModelProperty(value = "经销商编码")
    private String dealerCode;

    @ApiModelProperty(value = "经销商名称")
    private String dealerName;

    @ApiModelProperty(value = "负责人姓名")
    private String dutyName;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "省份ID")
    private Integer provinceId;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "城市ID")
    private Integer cityId;

    @ApiModelProperty(value = "区县名称")
    private String districtName;

    @ApiModelProperty(value = "区县ID")
    private Integer districtId;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "是否启用")
    private Boolean izEnable;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "业务员id")
    private Long businessUserId;

    @ApiModelProperty(value = "业务员名称")
    private String businessUserName;

    @ApiModelProperty(value = "销售区域列表")
    private List<List<SaleAreaSimpleVo>> saleAreaList;
}
