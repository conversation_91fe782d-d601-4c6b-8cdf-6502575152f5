package com.hishop.wine.model.po.login;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 刷新token 参数
 *
 * <AUTHOR>
 * @date : 2023/6/25
 */
@Data
@ApiModel("刷新token参数")
public class UserRefreshTokenPO {

    @NotBlank(message = "refreshToken不能为空")
    @ApiModelProperty(value = "刷新token", required = true)
    private String refreshToken;

    @ApiModelProperty(value = "身份类型 1-管理员 2-消费者 3-经销商 4-终端, 不传则以当前登录用户身份为准")
    private Integer identityType;

}
