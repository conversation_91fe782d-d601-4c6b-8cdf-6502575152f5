package com.hishop.wine.model.vo.map;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/20
 */
@Data
public class TecentAddressDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private LocationDTO location;
    private String address;
    @JSONField(name = "formatted_addresses")
    private FormattedAddressDTO formattedAddresses;
    @JSONField(name = "address_component")
    private AddressComponentDTO addressComponent;
    @JSONField(name = "ad_info")
    private AdInfoDTO adInfo;

}
