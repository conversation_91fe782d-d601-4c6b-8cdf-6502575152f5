package com.hishop.wine.model.vo.map;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/20
 */
@Data
public class AdInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @JSONField(name = "nation_code")
    private String nationCode;
    private String adcode;
    @JSONField(name = "phone_area_code")
    private String phoneAreaCode;
    @JSONField(name = "city_code")
    private String cityCode;
    private String name;
    private LocationDTO location;
    private String nation;
    private String province;
    private String city;
    private String district;

}
