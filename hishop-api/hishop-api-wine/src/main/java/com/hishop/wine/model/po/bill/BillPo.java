package com.hishop.wine.model.po.bill;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hishop.wine.enums.bill.TransactionType;
import com.hishop.wine.enums.order.PayMethod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 收入账单表(Bill)表实体类
 *
 * <AUTHOR>
 * @since 2024-01-29 15:17:19
 */
@Data
@ApiModel(value = "BillPo", description = "收入账单表")
@Builder
@AllArgsConstructor
public class BillPo {
    
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "模块编码")
    private String moduleCode;

    @ApiModelProperty(value = "交易类型 seal_wine_order封坛酒订单支付,extraction_wine_order取酒订单支付,storage_fee保管费支付")
    private String transactionType;
    
    @ApiModelProperty(value = "业务单号")
    private String businessNo;
    
    @ApiModelProperty(value = "交易流水号,对应交易表id")
    private Long transactionId;
    
    @ApiModelProperty(value = "第三方交易流水号")
    private String thirdTransactionNo;
    
    @ApiModelProperty(value = "支付方式 online_pay:在线支付 offline_pay:线下支付")
    private String payMethod;
    
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;
    
    @ApiModelProperty(value = "用户id")
    private Long userId;
    
    @ApiModelProperty(value = "用户名称")
    private String userName;

    @ApiModelProperty(value = "创建的年份yyyy")
    private String createYear;

    @ApiModelProperty(value = "创建的月份yyyy-MM")
    private String createMonth;

    @ApiModelProperty(value = "创建的天yyyy-MM-dd")
    private String createDay;

    @ApiModelProperty(value = "入账时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
