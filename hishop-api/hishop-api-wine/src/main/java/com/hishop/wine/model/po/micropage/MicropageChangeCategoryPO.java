package com.hishop.wine.model.po.micropage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@ApiModel(value = "MicropageChangeCategoryPO", description = "微页面批量修改分组对象")
public class MicropageChangeCategoryPO {

    @ApiModelProperty(value = "分组id", required = true)
    @NotNull(message = "分组id不能为空")
    private Long categoryId;

    @ApiModelProperty(value = "微页面id集合", required = true)
    @NotNull(message = "微页面id集合不能为空")
    @Size(min = 1, message = "微页面id集合不能为空")
    private List<Long> ids;

}
