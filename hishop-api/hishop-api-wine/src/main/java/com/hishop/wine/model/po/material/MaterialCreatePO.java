package com.hishop.wine.model.po.material;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 素材库 新增入参对象
 *
 * @author: HuBiao
 * @date: 2023-06-20
 */
@Data
@ApiModel(value = "MaterialCreatePO", description = "素材库新增入参对象")
public class MaterialCreatePO {

    @ApiModelProperty(value = "资源分组id", required = true)
    @NotNull(message = "资源分组id不能为空")
    private Long materialCategoryId;

    @ApiModelProperty(value = "资源标题(带扩展名)", required = true)
    @NotBlank(message = "资源标题不能为空")
    private String title;

    @ApiModelProperty(value = "音视频封面")
    private String bannerPath;

    @ApiModelProperty(value = "文件大小")
    private Long fileSize;
    @ApiModelProperty(value = "视频时长")
    private Integer  duration;
    @ApiModelProperty(value = "视频路径")
    private String  path;
}
