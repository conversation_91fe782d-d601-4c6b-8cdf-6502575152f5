package com.hishop.wine.model.po.moderation;

import com.hishop.wine.enums.ModerationEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * 内容审核回调参数
 * @author: HuBiao
 * @date: 2023-09-11
 */

@Data
@ApiModel(value = "ContentModerationCallbackPO", description = "内容审核回调参数")
public class ContentModerationCallbackPO {

    @ApiModelProperty(value = "业务类型")
	private ModerationEnum.BizType bizType;

    @ApiModelProperty(value = "业务编码")
	private String bizCode;

    @ApiModelProperty(value = "审核状态 待审核-WAITING 审核中-AUDITING SUCCESS-审核通过 FAIL-审核失败")
    private String status;

    @ApiModelProperty("是否成功")
    private Boolean izSuccess;

    @ApiModelProperty("失败原因")
    private String message;


}
