package com.hishop.wine.model.po.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 产品分类表 更新入参对象
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
@Data
@ApiModel(value = "ProductCategoryStatusUpdatePO", description = "产品分类状态更新入参对象")
public class ProductCategoryStatusUpdatePO {

    @ApiModelProperty(value = "主键id", required = true)
    @NotNull(message = "分类id不能为空")
    private Long id;

    @ApiModelProperty(value = "状态 0：禁用  1：正常", required = true)
    @NotNull(message = "状态不能为空")
    private Boolean status;

}
