package com.hishop.wine.model.vo.map;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/20
 */
@Data
public class FormattedAddressDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String recommend;
    private String rough;
    @JSONField(name = "standard_address")
    private String standardAddress;

}
