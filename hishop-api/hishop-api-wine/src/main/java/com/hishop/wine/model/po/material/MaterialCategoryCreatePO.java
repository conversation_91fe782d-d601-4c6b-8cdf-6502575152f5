package com.hishop.wine.model.po.material;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 素材库分组 新增入参对象
 *
 * @author: HuBiao
 * @date: 2023-06-20
 */
@Data
@ApiModel(value = "MaterialCategoryCreatePO", description = "素材库分组新增入参对象")
public class MaterialCategoryCreatePO {

    @ApiModelProperty(value = "父级id", required = true)
    @NotNull(message = "父级id不能为空")
    private Long parentId;

    @ApiModelProperty(value = "分组名称", required = true)
    @NotNull(message = "分组名称不能为空")
    private String name;

    @ApiModelProperty(value = "分组类型 1-图片 2-视频 3-音频", required = true)
    @NotNull(message = "分组类型不能为空")
    @Min(value = 1, message = "分组类型不正确, 取值范围为1-3")
    @Max(value = 3, message = "分组类型不正确, 取值范围为1-3")
    private Integer type;

}
