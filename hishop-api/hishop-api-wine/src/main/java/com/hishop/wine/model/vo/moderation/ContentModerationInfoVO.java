package com.hishop.wine.model.vo.moderation;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**   
 * 内容审核明细表 返回对象
 * @author: HuBiao
 * @date: 2023-09-11
 */

@Data
@ApiModel(value = "ContentModerationInfoVO", description = "内容审核明细表返回对象")
public class ContentModerationInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;
	
    @ApiModelProperty(value = "主键id")
	private Long id;
    
    @ApiModelProperty(value = "审核id")
	private Long moderationId;
    
    @ApiModelProperty(value = "审核类型 TEXT-文本 IMAGE-图片 VIDEO-视频 AUDIO-音频")
	private String moderationType;
    
    @ApiModelProperty(value = "审核建议 block：包含敏感信息，不通过。pass：不包含敏感信息，通过。review：需要人工复查。（华为云返回）")
	private String suggestionType;
    
    @ApiModelProperty(value = "提示信息，失败和不通过时会有（华为云返回）")
	private String message;
    
    @ApiModelProperty(value = "如果审核不通过，则会有具体的提示，每种类型的都不一样(华为云返回)")
	private String moderationResult;
    
    @ApiModelProperty(value = "异步审核时，与审核服务的跟踪号，用于后续查询异步结果(华为云返回)")
	private String traceNo;
    
    @ApiModelProperty(value = "待审核-WAITING 审核中-AUDITING SUCCESS-审核通过 FAIL-审核失败")
	private String status;
    
    @ApiModelProperty(value = "备注信息")
	private String remark;
    
    @ApiModelProperty(value = "创建者ID")
	private Long createBy;
    
    @ApiModelProperty(value = "创建时间")
	private Date createTime;
    
    @ApiModelProperty(value = "更新者ID")
	private Long updateBy;
    
    @ApiModelProperty(value = "更新时间")
	private Date updateTime;
    

}
