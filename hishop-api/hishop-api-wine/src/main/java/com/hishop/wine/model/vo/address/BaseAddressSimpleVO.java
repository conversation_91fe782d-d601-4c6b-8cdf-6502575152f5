package com.hishop.wine.model.vo.address;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.hishop.common.annotation.Desensitized;
import com.hishop.common.enums.SensitiveTypeEnum;
import com.hishop.wine.model.vo.delivery.DeliveryAddressSimpleVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * 地址库表 返回对象
 * @author: HuBiao
 * @date: 2023-07-17
 */

@Data
@ApiModel(value = "BaseAddressSimpleVO", description = "地址库表返回对象")
public class BaseAddressSimpleVO {

    @ApiModelProperty("地址id")
    private Long id;

    @ApiModelProperty(value = "联系人")
    private String contacts;

    @ApiModelProperty(value = "联系人手机号")
    @Desensitized(type = SensitiveTypeEnum.MOBILE)
    private String contactsPhone;

    @ApiModelProperty(value = "详细省市区")
    private String district;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "邮编")
    private String postalCode;

    @ApiModelProperty(value = "是否默认发货地址")
    private Boolean izDefaultSendAddress;

    @ApiModelProperty(value = "是否默认收货地址")
    private Boolean izDefaultReceiveAddress;

}
