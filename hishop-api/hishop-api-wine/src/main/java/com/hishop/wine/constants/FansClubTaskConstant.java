package com.hishop.wine.constants;

/**
 * 子任务常量 跟粉丝俱乐部模块子任务类型枚举保持一致
 *
 * <AUTHOR>
 * @date : 2023/8/3
 */
public interface FansClubTaskConstant {

    /**
     * 签到
     */
    String SIGN_IN = "SIGN_IN";

    /**
     * 连续签到
     */
    String CONTINUOUS_SIGN_IN = "CONTINUOUS_SIGN_IN";

    /**
     * 邀请新人
     */
    String INVITE_NEW_PERSON = "INVITE_NEW_PERSON";

    /**
     * 发布社区文章
     */
    String PUBLISH_ARTICLE = "PUBLISH_ARTICLE";

    /**
     * 点赞社区文章
     */
    String LIKE_ARTICLE = "LIKE_ARTICLE";

    /**
     * 评论社区文章
     */
    String COMMENT_ARTICLE = "COMMENT_ARTICLE";

}
