package com.hishop.wine.model.vo.miniApp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2024/05/06/ $
 * @description:
 */
@Data
@ApiModel(value = "MinAppTokenVo", description = "小程序token")
public class MinAppTokenVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("凭证")
    private String accessToken;

    @ApiModelProperty("凭证有效时间")
    private Integer expiresIn;

    @ApiModelProperty("凭证过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expirationTime;

}
