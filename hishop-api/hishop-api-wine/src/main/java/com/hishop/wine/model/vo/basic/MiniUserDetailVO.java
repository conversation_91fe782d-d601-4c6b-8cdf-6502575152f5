package com.hishop.wine.model.vo.basic;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 小程序用户详情对象
 * @author: guoyufeng
 * @date: 2024-01-25
 */

@Data
@ApiModel(value = "MiniUserDetailVO", description = "小程序用户详情对象")
public class MiniUserDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    @ApiModelProperty("微信头像")
    private String avatarUrl;

    @ApiModelProperty(value = "小程序app_id")
    private String appId;

    @ApiModelProperty(value = "注册模块编码")
    private String registerModuleCode;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "unionid")
    private String unionId;

    @ApiModelProperty(value = "openid")
    private String openId;

    @ApiModelProperty(value = "最后一次登录的sessionKey")
    private String sessionKey;

    @ApiModelProperty(value = "是否黑名单 0：否 1：是")
    private Boolean izBlacklist;

    @ApiModelProperty(value = "拉黑时间")
    private Date blacklistDate;

    @ApiModelProperty(value = "拉黑操作人")
    private Long blacklistOperateId;

    @ApiModelProperty(value = "拉黑操作人姓名")
    private String blacklistOperateName;

    @ApiModelProperty(value = "拉黑操作人电话")
    private String blacklistOperatePhone;

    @ApiModelProperty(value = "客户手机")
    private String mobile;

    @ApiModelProperty(value = "客户标签")
    private List<UserTagsVO> userTags;

    @ApiModelProperty(value = "客户编号")
    private String userNo;

    @ApiModelProperty(value = "备注名")
    private String remarkName;
}
