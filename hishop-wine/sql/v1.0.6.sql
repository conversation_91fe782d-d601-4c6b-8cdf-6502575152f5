CREATE TABLE `hishop_login_error` (
              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
              `mobile` varchar(11) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '手机号',
              `app_id` varchar(50) CHARACTER SET utf8mb4 NOT NULL COMMENT '小程序app_id',
              `open_id` varchar(50) CHARACTER SET utf8mb4 NOT NULL COMMENT 'openid',
              `type` int(2) DEFAULT NULL COMMENT '异常类型 0微信号绑定其他手机 1手机号绑定其他微信',
              `status` bit(1) DEFAULT b'0' COMMENT '重置状态 0未重置 1已重置',
              `create_by` bigint(20) DEFAULT '1' COMMENT '创建者ID',
              `update_by` bigint(20) DEFAULT NULL COMMENT '更新者ID',
              `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
              `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
              `iz_delete` bit(1) DEFAULT b'0' COMMENT '是否删除 0否 1是',
              PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_german2_ci COMMENT='登录异常表';

INSERT INTO `hishop_resource` (`id`, `module_code`, `parent_id`, `parent_ids`, `name`, `path`, `privilege`, `type`, `icon`, `order_num`, `status`, `iz_show`, `iz_tab`, `iz_group`, `menu_layout`, `default_resource_id`, `default_resource_path`, `iz_delete`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES (110502, 'basic_system', 1105, '1,1105,110502', '会员登录异常', '/member/loginError', '', 2, 'icon-customer', 0, b'1', b'1', b'0', b'0', 0, 0, '', 0, 2, '2023-08-24 11:38:52', 2, '2023-08-24 11:38:52');
