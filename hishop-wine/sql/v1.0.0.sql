# 创建数据库
create database IF NOT EXISTS hishop_wine_basic;
use hishop_wine_basic;

/*
 Navicat Premium Data Transfer

 Source Server         : jiukeduo_dev
 Source Server Type    : MySQL
 Source Server Version : 50738
 Source Host           : ************:3306
 Source Schema         : hishop_wine_basic

 Target Server Type    : MySQL
 Target Server Version : 50738
 File Encoding         : 65001

 Date: 15/09/2023 15:26:02
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for hishop_base_address
-- ----------------------------
DROP TABLE IF EXISTS `hishop_base_address`;
CREATE TABLE `hishop_base_address`  (
                                        `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '地址id',
                                        `contacts` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系人',
                                        `contacts_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系人手机号',
                                        `province_id` int(11) NOT NULL COMMENT '省ID',
                                        `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '省',
                                        `city_id` int(11) NOT NULL COMMENT '城市ID',
                                        `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '城市',
                                        `area_id` int(11) NOT NULL COMMENT '区ID',
                                        `area` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '区',
                                        `street_id` int(11) NULL DEFAULT 0 COMMENT '街道id',
                                        `street` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '街道名称',
                                        `address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细地址',
                                        `postal_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '邮政编码',
                                        `iz_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                        `iz_default_send_address` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否默认发货地址',
                                        `iz_default_receive_address` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否默认收货地址',
                                        `create_by` bigint(20) NOT NULL COMMENT '创建者ID',
                                        `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                        `update_by` bigint(20) NOT NULL COMMENT '更新者ID',
                                        `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '地址库表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_basic_setting
-- ----------------------------
DROP TABLE IF EXISTS `hishop_basic_setting`;
CREATE TABLE `hishop_basic_setting`  (
                                         `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                         `setting_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置名称(SMS_SETTING-短信配置 SYSTEM_SETTING-系统配置)',
                                         `setting_value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置值（json格式）',
                                         `create_by` bigint(20) NULL DEFAULT 0 COMMENT '创建者',
                                         `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                         `update_by` bigint(20) NULL DEFAULT NULL COMMENT '更新者',
                                         `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                         PRIMARY KEY (`id`) USING BTREE,
                                         UNIQUE INDEX `un_idx_setting_name`(`setting_name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '公共设置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_content_moderation
-- ----------------------------
DROP TABLE IF EXISTS `hishop_content_moderation`;
CREATE TABLE `hishop_content_moderation`  (
                                              `id` bigint(20) UNSIGNED NOT NULL COMMENT '审核id',
                                              `module_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模块编码',
                                              `biz_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务类型',
                                              `biz_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务编码',
                                              `biz_desc` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '业务描述',
                                              `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '待审核-WAITING 审核中-AUDITING SUCCESS-审核通过 FAIL-审核失败',
                                              `create_by` bigint(20) NOT NULL COMMENT '创建者ID',
                                              `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                              `update_by` bigint(20) NULL DEFAULT NULL COMMENT '更新者ID',
                                              `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '内容审核表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_content_moderation_info
-- ----------------------------
DROP TABLE IF EXISTS `hishop_content_moderation_info`;
CREATE TABLE `hishop_content_moderation_info`  (
                                                   `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '审核明细id',
                                                   `moderation_id` bigint(20) NOT NULL COMMENT '审核id',
                                                   `moderation_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '审核类型 TEXT-文本 IMAGE-图片 VIDEO-视频 AUDIO-音频',
                                                   `moderation_data` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核内容 如果审核类型为TEXT, 则是文本信息, 其余为远程地址',
                                                   `suggestion` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '审核建议 block：包含敏感信息，不通过。pass：不包含敏感信息，通过。review：需要人工复查。（华为云返回）',
                                                   `message` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '提示信息，失败和不通过时会有（华为云返回）',
                                                   `moderation_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '如果审核不通过，则会有具体的提示，每种类型的都不一样(华为云返回)',
                                                   `trace_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '异步审核时，与审核服务的跟踪号，用于后续查询异步结果(华为云返回)',
                                                   `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '待审核-WAITING 审核中-AUDITING SUCCESS-审核通过 FAIL-审核失败',
                                                   `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注信息',
                                                   `create_by` bigint(20) NOT NULL COMMENT '创建者ID',
                                                   `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                                   `update_by` bigint(20) NULL DEFAULT NULL COMMENT '更新者ID',
                                                   `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                                   PRIMARY KEY (`id`) USING BTREE,
                                                   INDEX `idx_moderation_id`(`moderation_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 49 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '内容审核明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_decorate
-- ----------------------------
DROP TABLE IF EXISTS `hishop_decorate`;
CREATE TABLE `hishop_decorate`  (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '装修ID-主键',
                                    `app_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '小程序APPID',
                                    `module_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '子系统编码',
                                    `decorate_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '装修类型。前端自己定义关键字',
                                    `setting_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '装修设置json串',
                                    `iz_default` bit(1) NULL DEFAULT b'0' COMMENT '是否小程序默认使用装修',
                                    `iz_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                    `create_by` bigint(20) NOT NULL COMMENT '创建者ID',
                                    `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                    `update_by` bigint(20) NOT NULL COMMENT '更新者ID',
                                    `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小程序装修表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_delivery_address
-- ----------------------------
DROP TABLE IF EXISTS `hishop_delivery_address`;
CREATE TABLE `hishop_delivery_address`  (
                                            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '收货地址id',
                                            `user_id` bigint(20) NOT NULL COMMENT '用户ID',
                                            `identity_id` bigint(20) NOT NULL COMMENT '身份id',
                                            `consignee` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收货人',
                                            `consignee_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '收货人手机号',
                                            `province_id` int(11) NOT NULL COMMENT '省ID',
                                            `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '省',
                                            `city_id` int(11) NOT NULL COMMENT '城市ID',
                                            `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '城市',
                                            `area_id` int(11) NOT NULL COMMENT '区ID',
                                            `area` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '区',
                                            `street_id` int(11) NULL DEFAULT 0 COMMENT '街道id',
                                            `street` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '街道名称',
                                            `address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细地址',
                                            `iz_default` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是默认地址',
                                            `create_by` bigint(20) NOT NULL COMMENT '创建者ID',
                                            `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                            `update_by` bigint(20) NOT NULL COMMENT '更新者ID',
                                            `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 61 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '收货地址表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_district
-- ----------------------------
DROP TABLE IF EXISTS `hishop_district`;
CREATE TABLE `hishop_district`  (
                                    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                    `region_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '大区名称',
                                    `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '地区名称',
                                    `full_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '完整的名称',
                                    `parent_id` int(11) NOT NULL COMMENT '父级id',
                                    `parent_ids` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '父级链',
                                    `lng` decimal(10, 6) NOT NULL COMMENT '经度',
                                    `lat` decimal(10, 6) NOT NULL COMMENT '纬度',
                                    `level` tinyint(1) NOT NULL COMMENT '等级',
                                    `has_child` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否有子级',
                                    `create_by` bigint(20) NULL DEFAULT NULL COMMENT '创建者ID',
                                    `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                    `update_by` bigint(20) NULL DEFAULT NULL COMMENT '更新者ID',
                                    `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                    PRIMARY KEY (`id`) USING BTREE,
                                    INDEX `idx_parent_id`(`parent_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1568669697 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '地区表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_identity
-- ----------------------------
DROP TABLE IF EXISTS `hishop_identity`;
CREATE TABLE `hishop_identity`  (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '身份id',
                                    `user_id` bigint(20) NOT NULL COMMENT '用户id',
                                    `identity_type` tinyint(1) NOT NULL COMMENT '身份类型 1-管理员 2-消费者 3-经销商 4-终端',
                                    `module_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模块编码',
                                    `role_id` bigint(20) NULL DEFAULT NULL COMMENT '角色id null表示没有角色',
                                    `status` bit(1) NOT NULL DEFAULT b'1' COMMENT '状态 0：禁用  1：正常',
                                    `inviter_user_id` bigint(20) NULL DEFAULT 0 COMMENT '邀请用户id 为0表示无邀请人',
                                    `register_channel` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '注册渠道',
                                    `register_biz_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '注册业务编码',
                                    `register_handle` bit(1) NULL DEFAULT b'0' COMMENT '注册后处理是否完成(用来做幂等)',
                                    `iz_delete` bigint(20) NOT NULL DEFAULT 0 COMMENT '逻辑删除状态 0-未删除 其余为已删除',
                                    `create_by` bigint(20) NOT NULL COMMENT '创建者ID',
                                    `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                    `update_by` bigint(20) NOT NULL COMMENT '更新者ID',
                                    `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                    PRIMARY KEY (`id`) USING BTREE,
                                    UNIQUE INDEX `un_idx_user_id_identity_type_module_code`(`user_id`, `identity_type`, `module_code`, `iz_delete`) USING BTREE,
                                    INDEX `idx_role_id`(`role_id`) USING BTREE,
                                    INDEX `idx_register`(`register_channel`, `register_biz_code`, `inviter_user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 130 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户身份表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_logistics_company
-- ----------------------------
DROP TABLE IF EXISTS `hishop_logistics_company`;
CREATE TABLE `hishop_logistics_company`  (
                                             `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
                                             `company_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '物流公司名称',
                                             `company_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物流公司图片',
                                             `shipper_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流公司编码',
                                             `iz_selected` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否选中',
                                             `create_by` bigint(20) NOT NULL COMMENT '创建者ID',
                                             `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                             `update_by` bigint(20) NOT NULL COMMENT '更新者ID',
                                             `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '物流公司表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_material
-- ----------------------------
DROP TABLE IF EXISTS `hishop_material`;
CREATE TABLE `hishop_material`  (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    `material_category_id` bigint(20) NOT NULL COMMENT '资源所属分组',
                                    `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
                                    `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源路径',
                                    `banner_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面路径',
                                    `type` tinyint(4) NOT NULL COMMENT '资源类型',
                                    `size` bigint(20) NULL DEFAULT NULL COMMENT '资源大小',
                                    `status` tinyint(4) NOT NULL COMMENT '状态',
                                    `job_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '转码任务id',
                                    `used_times` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数',
                                    `duration` int(11) NOT NULL DEFAULT 0 COMMENT '单视频长度(秒)',
                                    `create_by` bigint(20) NOT NULL COMMENT '创建者ID',
                                    `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                    `update_by` bigint(20) NOT NULL COMMENT '更新者ID',
                                    `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                    PRIMARY KEY (`id`) USING BTREE,
                                    INDEX `idx_category_id`(`material_category_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 726 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '素材库' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_material_category
-- ----------------------------
DROP TABLE IF EXISTS `hishop_material_category`;
CREATE TABLE `hishop_material_category`  (
                                             `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                             `parent_id` bigint(20) NOT NULL COMMENT '父级id',
                                             `parent_ids` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '上级id的集合',
                                             `name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分组名称',
                                             `type` tinyint(4) NOT NULL COMMENT '分组类型',
                                             `level` smallint(6) NOT NULL COMMENT '层级',
                                             `path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '从顶级到当前级的完整路径(不包括当前分组)',
                                             `total` int(11) NOT NULL DEFAULT 0 COMMENT '当前分组下资源文件总数',
                                             `create_by` bigint(20) NOT NULL COMMENT '创建者ID',
                                             `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                             `update_by` bigint(20) NOT NULL COMMENT '更新者ID',
                                             `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 80 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '素材库分组' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_member_points
-- ----------------------------
DROP TABLE IF EXISTS `hishop_member_points`;
CREATE TABLE `hishop_member_points`  (
                                         `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                         `user_id` bigint(20) NOT NULL COMMENT '用户ID',
                                         `identity_type` tinyint(4) NOT NULL COMMENT '身份类型 1-管理员 2-消费者 3-经销商 4-终端',
                                         `user_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户手机号码',
                                         `total_points` int(11) NOT NULL DEFAULT 0 COMMENT '用户历史累计总积分',
                                         `available_points` int(11) NOT NULL DEFAULT 0 COMMENT '可用积分=当前剩余总积分',
                                         `consumed_points` int(11) NOT NULL DEFAULT 0 COMMENT '已消耗积分=用户正常使用掉的',
                                         `expired_points` int(11) NOT NULL DEFAULT 0 COMMENT '过期清零的积分',
                                         `frozen_points` int(11) NOT NULL DEFAULT 0 COMMENT '已冻结的积分',
                                         `consumed_last_detail_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '积分消耗对应的明细指针',
                                         `last_detail_remain_point` int(11) NOT NULL DEFAULT 0 COMMENT '明细指针剩余积分',
                                         PRIMARY KEY (`id`) USING BTREE,
                                         UNIQUE INDEX `idx_mp_common_qry`(`user_id`, `identity_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 117 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员积分表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_member_points_details
-- ----------------------------
DROP TABLE IF EXISTS `hishop_member_points_details`;
CREATE TABLE `hishop_member_points_details`  (
                                                 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '明细ID，主键，用雪花算法生成',
                                                 `user_id` bigint(20) NOT NULL COMMENT '用户ID',
                                                 `identity_type` tinyint(4) NOT NULL COMMENT '身份类型 1-管理员 2-消费者 3-经销商 4-终端',
                                                 `user_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户手机号码',
                                                 `modified_type` tinyint(4) NOT NULL COMMENT '变更类型。-1：减积分；0：积分清零；1：加积分。方便区分',
                                                 `modified_points` int(11) NOT NULL COMMENT '变更的积分。通过正负值表示增减，方便统计',
                                                 `module_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '来源系统。BASIC_SYSTEM：基础库；SCAN_MARKETING：扫码营销；FANS_CLUB：粉丝俱乐部',
                                                 `biz_type` tinyint(4) NOT NULL COMMENT '具体的业务类型，枚举与来源系统保持一致。积分清零固定为0',
                                                 `biz_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关联的业务编码，方便回溯',
                                                 `trace_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '跟踪号，比如消费消息的唯一编码',
                                                 `create_time` datetime(0) NOT NULL COMMENT '创建时间，即积分变更时间',
                                                 `expire_time` datetime(0) NULL DEFAULT NULL COMMENT '积分过期时间',
                                                 `create_by` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建人。如果是手工变更的，记录变更人。0代表系统变更',
                                                 `modified_remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '变更备注',
                                                 PRIMARY KEY (`id`) USING BTREE,
                                                 INDEX `idx_user_id_identity_type`(`user_id`, `identity_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1702580487213359106 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员积分明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_micropage
-- ----------------------------
DROP TABLE IF EXISTS `hishop_micropage`;
CREATE TABLE `hishop_micropage`  (
                                     `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                     `category_id` int(11) NULL DEFAULT 0 COMMENT '分组id, 如果为0表示未分组',
                                     `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '微页面名称',
                                     `setting_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置json',
                                     `pv` int(11) NOT NULL DEFAULT 0 COMMENT '访问量',
                                     `uv` int(11) NOT NULL DEFAULT 0 COMMENT '访问人数',
                                     `status` tinyint(2) NOT NULL COMMENT '1-草稿 2-上架',
                                     `iz_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除 true-删除 false-未删除',
                                     `create_by` bigint(20) NULL DEFAULT NULL COMMENT '创建者ID',
                                     `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                     `update_by` bigint(20) NULL DEFAULT NULL COMMENT '更新者ID',
                                     `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微页面表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_micropage_category
-- ----------------------------
DROP TABLE IF EXISTS `hishop_micropage_category`;
CREATE TABLE `hishop_micropage_category`  (
                                              `id` int(11) NOT NULL AUTO_INCREMENT,
                                              `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                              `parent_id` bigint(20) NULL DEFAULT NULL,
                                              `create_by` bigint(20) NULL DEFAULT NULL,
                                              `update_by` bigint(20) NULL DEFAULT NULL,
                                              `create_time` datetime(6) NOT NULL,
                                              `update_time` datetime(6) NOT NULL,
                                              `level` int(11) NULL DEFAULT NULL,
                                              `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                                              PRIMARY KEY (`Id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_micropage_visit
-- ----------------------------
DROP TABLE IF EXISTS `hishop_micropage_visit`;
CREATE TABLE `hishop_micropage_visit`  (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `micropage_id` int(11) NOT NULL COMMENT '微页面id',
                                           `user_id` bigint(20) NOT NULL COMMENT '访问用户id',
                                           `create_time` datetime(0) NOT NULL COMMENT '首次访问时间',
                                           `update_time` datetime(0) NOT NULL COMMENT '最后访问时间',
                                           PRIMARY KEY (`id`) USING BTREE,
                                           UNIQUE INDEX `un_idx_micropage_id_user_id`(`micropage_id`, `user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4090 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微页面访客记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_mini_app
-- ----------------------------
DROP TABLE IF EXISTS `hishop_mini_app`;
CREATE TABLE `hishop_mini_app`  (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                    `app_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'app_id',
                                    `original_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '小程序原始id',
                                    `app_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '小程序名称',
                                    `app_secret` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'app_secret',
                                    `token` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '微信小程序消息服务器配置的token',
                                    `mch_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '商户号id(弃用)',
                                    `aes_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '微信小程序消息服务器配置的EncodingAESKey',
                                    `msg_data_format` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'JSON' COMMENT '消息格式，XML或者JSON',
                                    `env_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'release' COMMENT '要打开的小程序版本。正式版为 \"release\"，体验版为 \"trial\"，开发版为 \"develop\"。默认是正式版。',
                                    `setting_module_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '小程序和底部导航采用的模编码',
                                    `create_by` bigint(20) NOT NULL COMMENT '创建者ID',
                                    `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                    `update_by` bigint(20) NOT NULL COMMENT '更新者ID',
                                    `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                    PRIMARY KEY (`id`) USING BTREE,
                                    UNIQUE INDEX `un_idx_app_id`(`app_id`) USING BTREE,
                                    INDEX `idx_mch_id`(`mch_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小程序表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_mini_app_payment
-- ----------------------------
DROP TABLE IF EXISTS `hishop_mini_app_payment`;
CREATE TABLE `hishop_mini_app_payment`  (
                                            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                            `app_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '小程序id',
                                            `payment_id` bigint(20) NOT NULL COMMENT '支付设置id',
                                            PRIMARY KEY (`id`) USING BTREE,
                                            UNIQUE INDEX `un_idx_app_id_payment_id`(`app_id`, `payment_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小程序关联支付设置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_mini_user
-- ----------------------------
DROP TABLE IF EXISTS `hishop_mini_user`;
CREATE TABLE `hishop_mini_user`  (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '小程序用户id',
                                     `user_id` bigint(20) NOT NULL COMMENT '用户id',
                                     `nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '昵称',
                                     `avatar_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '微信头像',
                                     `app_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '小程序app_id',
                                     `register_module_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '注册模块编码',
                                     `union_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'unionid',
                                     `open_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'openid',
                                     `session_key` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '最后一次登录的sessionKey',
                                     `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                     `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                     PRIMARY KEY (`id`) USING BTREE,
                                     UNIQUE INDEX `un_idx_user_id_app_id`(`user_id`, `app_id`) USING BTREE,
                                     UNIQUE INDEX `un_idx_open_id_app_id`(`open_id`, `app_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 110 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '小程序用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_module
-- ----------------------------
DROP TABLE IF EXISTS `hishop_module`;
CREATE TABLE `hishop_module`  (
                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                  `module_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模块编码',
                                  `module_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模块名称',
                                  `app_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '小程序app_id, 为空则表示没有关联小程序',
                                  `create_by` bigint(20) NOT NULL COMMENT '创建者ID',
                                  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                  `update_by` bigint(20) NOT NULL COMMENT '更新者ID',
                                  `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                  PRIMARY KEY (`id`) USING BTREE,
                                  UNIQUE INDEX `un_idx_module_code`(`module_code`) USING BTREE,
                                  INDEX `idx_app_id`(`app_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '模块表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_pages
-- ----------------------------
DROP TABLE IF EXISTS `hishop_pages`;
CREATE TABLE `hishop_pages`  (
                                 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '页面id',
                                 `module_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模块编码',
                                 `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
                                 `path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '请求地址',
                                 `link_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '链接类型 为空时支持所有类型',
                                 `create_by` bigint(20) NULL DEFAULT NULL COMMENT '创建者ID',
                                 `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                 `update_by` bigint(20) NULL DEFAULT NULL COMMENT '更新者ID',
                                 `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                 PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 33 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '页面配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_payment_certificate
-- ----------------------------
DROP TABLE IF EXISTS `hishop_payment_certificate`;
CREATE TABLE `hishop_payment_certificate`  (
                                               `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                               `content` mediumblob NOT NULL COMMENT '商户证书byte',
                                               `create_by` bigint(20) NULL DEFAULT 0 COMMENT '创建者',
                                               `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                               `update_by` bigint(20) NULL DEFAULT NULL COMMENT '更新者',
                                               `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付证书' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_payment_setting
-- ----------------------------
DROP TABLE IF EXISTS `hishop_payment_setting`;
CREATE TABLE `hishop_payment_setting`  (
                                           `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `merchant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户id(各种支付方式的商户id)',
                                           `payment_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付方式名称',
                                           `payment_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '支付类型 WX_PAY-微信支付 ALI_PAY-支付宝支付 OFFLINE-线下支付',
                                           `scene_value` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '场景值',
                                           `setting_value` json NOT NULL COMMENT '配置值（json格式）',
                                           `create_by` bigint(20) NULL DEFAULT 0 COMMENT '创建者',
                                           `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                           `update_by` bigint(20) NULL DEFAULT NULL COMMENT '更新者',
                                           `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                           PRIMARY KEY (`id`) USING BTREE,
                                           UNIQUE INDEX `un_idx_merchant_id`(`merchant_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付设置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_product
-- ----------------------------
DROP TABLE IF EXISTS `hishop_product`;
CREATE TABLE `hishop_product`  (
                                   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '产品id',
                                   `product_type` tinyint(1) NOT NULL COMMENT '产品类型 1-商品 2-礼品',
                                   `product_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '产品名称',
                                   `product_img` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '产品首图',
                                   `product_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '产品编码',
                                   `product_unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '产品单位',
                                   `product_category_id` bigint(20) NOT NULL COMMENT '产品分类id',
                                   `market_price` decimal(20, 2) NULL DEFAULT NULL COMMENT '市场价格',
                                   `status` bit(1) NOT NULL DEFAULT b'1' COMMENT '状态 0：禁用  1：正常 (暂未使用)',
                                   `iz_delete` bigint(20) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0-未删除 其余为已删除',
                                   `create_by` bigint(20) NOT NULL COMMENT '创建者ID',
                                   `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                   `update_by` bigint(20) NOT NULL COMMENT '更新者ID',
                                   `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                   PRIMARY KEY (`id`) USING BTREE,
                                   UNIQUE INDEX `un_idx_product_code`(`product_code`, `iz_delete`) USING BTREE,
                                   INDEX `idx_product_category_id`(`product_category_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 70 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_product_category
-- ----------------------------
DROP TABLE IF EXISTS `hishop_product_category`;
CREATE TABLE `hishop_product_category`  (
                                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '产品分类id',
                                            `category_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
                                            `order_num` int(11) NULL DEFAULT 0 COMMENT '排序',
                                            `status` bit(1) NOT NULL DEFAULT b'1' COMMENT '状态 0：禁用  1：正常',
                                            `iz_delete` bigint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0-未删除 其余为已删除',
                                            `create_by` bigint(20) NOT NULL COMMENT '创建者ID',
                                            `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                            `update_by` bigint(20) NOT NULL COMMENT '更新者ID',
                                            `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                            PRIMARY KEY (`id`) USING BTREE,
                                            UNIQUE INDEX `idx_category_name`(`category_name`, `iz_delete`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_product_media
-- ----------------------------
DROP TABLE IF EXISTS `hishop_product_media`;
CREATE TABLE `hishop_product_media`  (
                                         `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '产品媒体id',
                                         `product_id` bigint(20) NOT NULL COMMENT '产品id',
                                         `media_type` tinyint(1) NOT NULL COMMENT '媒体类型 1-图片 2-主图视频 3-详情图',
                                         `url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '产品名称',
                                         `cover` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '视频封面图',
                                         PRIMARY KEY (`id`) USING BTREE,
                                         INDEX `idx_product_id_media_type`(`product_id`, `media_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 660 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '产品媒体资源表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_rank
-- ----------------------------
DROP TABLE IF EXISTS `hishop_rank`;
CREATE TABLE `hishop_rank`  (
                                `id` bigint(20) NOT NULL COMMENT '主键 雪花算法',
                                `rank_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '头衔名称',
                                `module_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模块编码',
                                `status` bit(1) NOT NULL DEFAULT b'1' COMMENT '状态 0：禁用  1：正常',
                                `iz_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除 0:未删除 1:删除',
                                `create_by` bigint(20) NULL DEFAULT NULL COMMENT '创建者ID',
                                `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                `update_by` bigint(20) NULL DEFAULT NULL COMMENT '更新者ID',
                                `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '头衔表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_refund_reason_config
-- ----------------------------
DROP TABLE IF EXISTS `hishop_refund_reason_config`;
CREATE TABLE `hishop_refund_reason_config`  (
                                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                `refund_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '退款原因',
                                                `refund_types` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '退款类型（1：仅退款，2：退货退款），多个用逗号隔开',
                                                `create_by` bigint(20) NOT NULL COMMENT '创建者',
                                                `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                                `update_by` bigint(20) NULL DEFAULT NULL COMMENT '更新者',
                                                `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                                PRIMARY KEY (`id`) USING BTREE,
                                                UNIQUE INDEX `uk_type_reason`(`refund_reason`, `refund_types`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '退款原因配置表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_resource
-- ----------------------------
DROP TABLE IF EXISTS `hishop_resource`;
CREATE TABLE `hishop_resource`  (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '资源id',
                                    `module_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模块编码',
                                    `parent_id` bigint(20) NOT NULL COMMENT '上级资源id, 一级资源为0',
                                    `parent_ids` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '上级id的集合',
                                    `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源名称',
                                    `path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求地址(用于后端拦截)',
                                    `privilege` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '资源编码(用于前端展示)',
                                    `type` tinyint(2) NOT NULL COMMENT '类型   0: 模块 1：目录  2：菜单 3：按钮',
                                    `icon` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '图标',
                                    `order_num` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
                                    `status` bit(1) NOT NULL DEFAULT b'1' COMMENT '状态 0：禁用  1：正常',
                                    `iz_show` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否展示',
                                    `iz_tab` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否是tab',
                                    `iz_group` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否分组',
                                    `menu_layout` tinyint(1) NOT NULL DEFAULT 0 COMMENT '菜单布局 1-左侧菜单 2-顶部菜单(只有type=0时才有值)',
                                    `default_resource_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '默认资源的id (只有type=0时才有值)',
                                    `default_resource_path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '默认资源的path (只有type=0时才有值)',
                                    `iz_delete` bigint(20) NOT NULL DEFAULT 0 COMMENT '逻辑删除 0-未删除 其余为已删除',
                                    `create_by` bigint(20) NOT NULL COMMENT '创建者ID',
                                    `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                    `update_by` bigint(20) NOT NULL COMMENT '更新者ID',
                                    `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4404010205 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '资源表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_role
-- ----------------------------
DROP TABLE IF EXISTS `hishop_role`;
CREATE TABLE `hishop_role`  (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色id',
                                `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
                                `default_url` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '默认登录页面',
                                `status` bit(1) NOT NULL DEFAULT b'1' COMMENT '状态 0：禁用  1：正常',
                                `iz_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '逻辑删除 0-未删除 1-已删除',
                                `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
                                `create_by` bigint(20) NOT NULL COMMENT '创建者ID',
                                `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                `update_by` bigint(20) NOT NULL COMMENT '更新者ID',
                                `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_role_resource
-- ----------------------------
DROP TABLE IF EXISTS `hishop_role_resource`;
CREATE TABLE `hishop_role_resource`  (
                                         `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                         `role_id` bigint(20) NOT NULL COMMENT '角色id',
                                         `resource_id` bigint(20) NOT NULL COMMENT '资源id',
                                         PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 308 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色资源关联表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_sms_record
-- ----------------------------
DROP TABLE IF EXISTS `hishop_sms_record`;
CREATE TABLE `hishop_sms_record`  (
                                      `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '短信id',
                                      `channel` tinyint(1) NOT NULL COMMENT '短信类型 0-通知短信 1-营销短信 2-验证码短信',
                                      `content` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '短信内容',
                                      `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '发送状态 0-未发送 1-发送成功 2-发送失败',
                                      `result` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '发送结果',
                                      `send_time` datetime(0) NULL DEFAULT NULL COMMENT '发送时间(现在就是创建时间, 暂无延迟推送)',
                                      `create_by` bigint(20) NULL DEFAULT 0 COMMENT '创建者',
                                      `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                      `update_by` bigint(20) NULL DEFAULT NULL COMMENT '更新者',
                                      `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
                                      PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 214 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '短信发送记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_sms_record_mobile
-- ----------------------------
DROP TABLE IF EXISTS `hishop_sms_record_mobile`;
CREATE TABLE `hishop_sms_record_mobile`  (
                                             `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
                                             `sms_record_id` bigint(20) NOT NULL COMMENT '短信记录id',
                                             `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号',
                                             PRIMARY KEY (`id`) USING BTREE,
                                             UNIQUE INDEX `idx_sms_record_id_mobile`(`sms_record_id`, `mobile`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 214 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '短信发送手机号表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_storebar
-- ----------------------------
DROP TABLE IF EXISTS `hishop_storebar`;
CREATE TABLE `hishop_storebar`  (
                                    `id` bigint(20) NOT NULL,
                                    `update_time` datetime(6) NOT NULL,
                                    `create_time` datetime(6) NOT NULL,
                                    `create_by` bigint(20) NULL DEFAULT NULL,
                                    `update_by` bigint(20) NULL DEFAULT NULL,
                                    `bar_json` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
                                    `bg_color` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                    `font_color` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                    `select_color` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                    `module_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                    `app_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_system_log
-- ----------------------------
DROP TABLE IF EXISTS `hishop_system_log`;
CREATE TABLE `hishop_system_log`  (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志Id',
                                      `module_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '模块编码',
                                      `business_sector` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务模块',
                                      `business_desc` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '业务描述',
                                      `operation_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作类型 CREATE/UPDATE/DELETE',
                                      `operation_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作名称',
                                      `business_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '业务键值',
                                      `operator_username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作人账号',
                                      `operator_mobile` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作人手机号',
                                      `request_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求路径',
                                      `request_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求参数',
                                      `response` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '返回值',
                                      `cost_time` bigint(20) NULL DEFAULT 0 COMMENT '耗时(毫秒)',
                                      `trace_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '跟踪号',
                                      `create_by` bigint(20) NULL DEFAULT NULL COMMENT '创建者ID',
                                      `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                      PRIMARY KEY (`id`) USING BTREE,
                                      UNIQUE INDEX `un_idx_trace_no`(`trace_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '操作日志表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_system_log_details
-- ----------------------------
DROP TABLE IF EXISTS `hishop_system_log_details`;
CREATE TABLE `hishop_system_log_details`  (
                                              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志明细id',
                                              `log_id` bigint(20) NOT NULL COMMENT '日志id',
                                              `primary_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '主键id的集合',
                                              `old_data_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '调用前的值',
                                              `new_data_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '调用后的值',
                                              `change_data_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '变更情况',
                                              PRIMARY KEY (`id`) USING BTREE,
                                              UNIQUE INDEX `un_idx_log_id`(`log_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '操作日志明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_tags
-- ----------------------------
DROP TABLE IF EXISTS `hishop_tags`;
CREATE TABLE `hishop_tags`  (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                `tag_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签名称',
                                `create_by` bigint(20) NULL DEFAULT NULL COMMENT '创建者ID',
                                `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                `update_by` bigint(20) NULL DEFAULT NULL COMMENT '更新者ID',
                                `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '标签表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_transaction
-- ----------------------------
DROP TABLE IF EXISTS `hishop_transaction`;
CREATE TABLE `hishop_transaction`  (
                                       `id` bigint(20) UNSIGNED NOT NULL COMMENT '交易id',
                                       `transaction_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '交易流水',
                                       `module_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模块编码',
                                       `transaction_method` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '交易方式 WX_PAY-微信支付',
                                       `transaction_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '交易类型 PAY-付款 REFUND-退款',
                                       `mch_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '商户id',
                                       `app_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'app_id',
                                       `user_id` bigint(20) NOT NULL COMMENT '用户id',
                                       `open_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '支付用户 openId',
                                       `biz_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务类型',
                                       `biz_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务编码(用来存放订单号)',
                                       `amount` decimal(10, 2) NOT NULL COMMENT '交易金额',
                                       `description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '描述',
                                       `third_transaction_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '第三方支付流水号',
                                       `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态 1-待确认 2-交易成功 3-交易失败',
                                       `third_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '第三方状态',
                                       `finish_time` datetime(0) NULL DEFAULT NULL COMMENT '完成时间',
                                       `org_transaction_id` bigint(20) NULL DEFAULT NULL COMMENT '原交易id(如果是交易类型为退款才存在)',
                                       `refund_status` tinyint(255) NULL DEFAULT 0 COMMENT '退款状态 0-缺省值  1-未退款 2-部分退款 3-全部退款 (只有是交易为付款时该值才有效)',
                                       `refund_amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '退款金额 (只有是交易类型为付款时该值才有效)',
                                       `create_by` bigint(20) NOT NULL COMMENT '创建者ID',
                                       `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                       `update_by` bigint(20) NULL DEFAULT NULL COMMENT '更新者ID',
                                       `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                       PRIMARY KEY (`id`) USING BTREE,
                                       INDEX `idx_biz_type_biz_code_status`(`biz_type`, `biz_code`, `status`) USING BTREE,
                                       INDEX `idx_org_transaction_id`(`org_transaction_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '交易流水表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_transaction_notify
-- ----------------------------
DROP TABLE IF EXISTS `hishop_transaction_notify`;
CREATE TABLE `hishop_transaction_notify`  (
                                              `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '回调id',
                                              `transaction_id` bigint(20) NOT NULL COMMENT '交易id',
                                              `notify_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '回调信息',
                                              `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 67 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '交易回调消息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_user
-- ----------------------------
DROP TABLE IF EXISTS `hishop_user`;
CREATE TABLE `hishop_user`  (
                                `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户id',
                                `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户名',
                                `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '密码',
                                `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号',
                                `real_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '真实姓名',
                                `nick_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '微信昵称',
                                `icon` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '头像',
                                `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '邮箱',
                                `id_card` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '身份证',
                                `rank_id` bigint(20) NULL DEFAULT NULL COMMENT '用户头衔id',
                                `status` bit(1) NOT NULL DEFAULT b'1' COMMENT '状态 0：禁用  1：正常',
                                `create_by` bigint(20) NULL DEFAULT NULL COMMENT '创建者ID',
                                `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                `update_by` bigint(20) NULL DEFAULT NULL COMMENT '更新者ID',
                                `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                PRIMARY KEY (`id`) USING BTREE,
                                UNIQUE INDEX `un_idx_user_name`(`username`) USING BTREE,
                                INDEX `idx_email`(`email`) USING BTREE,
                                INDEX `idx_mobile`(`mobile`) USING BTREE,
                                INDEX `idx_rank_id`(`rank_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 120 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_user_tags
-- ----------------------------
DROP TABLE IF EXISTS `hishop_user_tags`;
CREATE TABLE `hishop_user_tags`  (
                                     `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                     `user_id` bigint(20) NOT NULL COMMENT '用户ID',
                                     `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
                                     `tag_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签名称',
                                     `create_by` bigint(20) NULL DEFAULT NULL COMMENT '创建者ID',
                                     `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                     `update_by` bigint(20) NULL DEFAULT NULL COMMENT '更新者ID',
                                     `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                     PRIMARY KEY (`id`) USING BTREE,
                                     UNIQUE INDEX `idx_user_id_tag_id`(`user_id`, `tag_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户标签表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_wechat_code
-- ----------------------------
DROP TABLE IF EXISTS `hishop_wechat_code`;
CREATE TABLE `hishop_wechat_code`  (
                                       `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '微信二维码ID-主键',
                                       `code_type` tinyint(4) NOT NULL COMMENT '二维码类型。1：小程序码；2：公众号二维码',
                                       `code_from` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '二维码来源。BASIC_SYSTEM：基础库；SCAN_MARKETING：扫码营销；FANS_CLUB：粉丝俱乐部',
                                       `code_key` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '二维码唯一标识，全局唯一，通过该字段匹配获取',
                                       `code_desc` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '二维码描述',
                                       `code_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '二维码地址。云服务器地址，存相对路径',
                                       `page` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '完整的小程序链接(可带参数)',
                                       `iz_delete` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                       `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                       `create_by` bigint(20) NOT NULL COMMENT '创建人ID',
                                       `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
                                       `update_by` bigint(20) NULL DEFAULT NULL COMMENT '修改人ID',
                                       PRIMARY KEY (`id`) USING BTREE,
                                       UNIQUE INDEX `idx_code`(`code_key`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 290 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信二维码表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_wechat_merchant
-- ----------------------------
DROP TABLE IF EXISTS `hishop_wechat_merchant`;
CREATE TABLE `hishop_wechat_merchant`  (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                           `mch_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户号id',
                                           `mch_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户名称',
                                           `mch_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '商户密钥',
                                           `api_v3_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'APIv3密钥',
                                           `key_path` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'p12(apiclient_cert.p12)',
                                           `private_key_path` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '私钥地址(apiclient_key.pem)',
                                           `private_cert_path` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '证书地址(apiclient_cert.pem)',
                                           `create_by` bigint(20) NOT NULL COMMENT '创建者ID',
                                           `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                           `update_by` bigint(20) NOT NULL COMMENT '更新者ID',
                                           `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                           PRIMARY KEY (`id`) USING BTREE,
                                           UNIQUE INDEX `un_idx_mch_id`(`mch_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信商户表（废弃）' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for hishop_wechat_web_user
-- ----------------------------
DROP TABLE IF EXISTS `hishop_wechat_web_user`;
CREATE TABLE `hishop_wechat_web_user`  (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '微信网页应用用户id',
                                           `user_id` bigint(20) NOT NULL COMMENT '用户id',
                                           `nick_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '昵称',
                                           `avatar_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '微信头像',
                                           `app_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '网页应用app_id(冗余字段)',
                                           `register_module_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '注册模块编码(冗余字段)',
                                           `union_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'unionid',
                                           `open_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'openid',
                                           `create_time` datetime(0) NOT NULL COMMENT '创建时间',
                                           `update_time` datetime(0) NOT NULL COMMENT '更新时间',
                                           PRIMARY KEY (`id`) USING BTREE,
                                           UNIQUE INDEX `un_idx_user_id_app_id`(`user_id`, `app_id`) USING BTREE,
                                           UNIQUE INDEX `un_idx_open_id_app_id`(`open_id`, `app_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信网页应用用户表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;

