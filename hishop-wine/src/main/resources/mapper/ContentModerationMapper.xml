<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.ContentModerationMapper">

    <update id="refreshStatus">
        UPDATE hishop_content_moderation m
        SET m.`status` =
            (
                CASE
                    WHEN ( SELECT COUNT( 1 ) FROM hishop_content_moderation_info mi WHERE mi.moderation_id = m.id AND mi.`status` = 'FAIL' ) > 0 THEN 'FAIL'
                    WHEN ( SELECT COUNT( 1 ) FROM hishop_content_moderation_info mi WHERE mi.moderation_id = m.id AND mi.`status` = 'AUDITING' ) > 0 THEN 'AUDITING'
                    WHEN ( SELECT COUNT( 1 ) FROM hishop_content_moderation_info mi WHERE mi.moderation_id = m.id AND mi.`status` = 'WAITING' ) > 0 THEN'WAITING'
                    ELSE 'SUCCESS'
                END
            )
        WHERE m.id = #{id}
    </update>

</mapper>