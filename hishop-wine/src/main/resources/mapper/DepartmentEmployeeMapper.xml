<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.DepartmentEmployeeMapper">
    <insert id="add">
        INSERT INTO hishop_department_employee (`department_id`,
                                                `user_id`,
                                                `iz_head`,
                                                `create_by`)
        VALUES (#{departmentId}, #{userId}, #{izHead}, #{createBy})
    </insert>

    <update id="updateDepByUserId">
        UPDATE hishop_department_employee
        SET
        <if test="izHead != null">
            iz_head = #{izHead},
        </if>
        department_id = #{departmentId}
        WHERE user_id in
        <foreach collection="userIds" item= "id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>
</mapper>