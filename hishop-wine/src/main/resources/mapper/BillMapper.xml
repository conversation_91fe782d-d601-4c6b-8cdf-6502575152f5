<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.BillMapper">

    <resultMap type="com.hishop.wine.repository.entity.Bill" id="BillMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="moduleCode" column="module_code" jdbcType="VARCHAR"/>
        <result property="transactionType" column="transaction_type" jdbcType="VARCHAR"/>
        <result property="businessNo" column="business_no" jdbcType="VARCHAR"/>
        <result property="transactionId" column="transaction_id" jdbcType="INTEGER"/>
        <result property="thirdTransactionNo" column="third_transaction_no" jdbcType="VARCHAR"/>
        <result property="payMethod" column="pay_method" jdbcType="VARCHAR"/>
        <result property="amount" column="amount" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="createYear" column="create_year" jdbcType="VARCHAR"/>
        <result property="createMonth" column="create_month" jdbcType="VARCHAR"/>
        <result property="createDay" column="create_day" jdbcType="VARCHAR"/>
        <result property="izDelete" column="iz_delete" jdbcType="BOOLEAN"/>
        <result property="createBy" column="create_by" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="pageListCount" resultType="com.hishop.wine.model.dto.bill.BillCountDto">
        SELECT COUNT(1) as totalNum, IFNULL(SUM(t.amount), 0.00) as totalFee FROM hishop_bill t
        WHERE t.iz_delete = 0
        <if test="param.payMethod != null">
            AND t.pay_method = #{param.payMethod}
        </if>
        <if test="param.transactionType != null">
            AND t.transaction_type = #{param.transactionType}
        </if>
        <if test="param.no != null and param.no != ''">
            AND (t.business_no like CONCAT('%',#{param.no},'%') OR t.third_transaction_no like CONCAT('%',#{param.no},'%'))
        </if>
        <if test="param.startTime != null">
            AND t.create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            AND t.create_time &lt;= #{param.endTime}
        </if>
        <if test="param.minFee != null">
            AND t.amount >= #{param.minFee}
        </if>
        <if test="param.maxFee != null">
            AND t.amount &lt;= #{param.maxFee}
        </if>
    </select>

    <select id="monthPageList" resultType="com.hishop.wine.model.dto.bill.BillGroupDto">
        SELECT t.create_month as dateStr, t.pay_method, IFNULL(SUM(t.amount),0) as amount, COUNT(1) as num
        FROM hishop_bill t
        WHERE t.iz_delete = 0
        <if test="param.startTime != null">
            AND t.create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            AND t.create_time &lt;= #{param.endTime}
        </if>
        GROUP BY t.create_month, t.pay_method
    </select>

    <select id="monthPageListCount" resultType="com.hishop.wine.model.dto.bill.BillCountDto">
        SELECT COUNT(1) as totalNum, IFNULL(SUM(t.amount), 0.00) as totalFee
        FROM hishop_bill t
        WHERE t.iz_delete = 0
        <if test="param.startTime != null">
            AND t.create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            AND t.create_time &lt;= #{param.endTime}
        </if>
    </select>

    <select id="dayPageList" resultType="com.hishop.wine.model.dto.bill.BillGroupDto">
        SELECT t.create_day as dateStr, t.pay_method, IFNULL(SUM(t.amount),0) as amount, COUNT(1) as num
        FROM hishop_bill t
        WHERE t.iz_delete = 0
        <if test="param.startTime != null">
            AND t.create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            AND t.create_time &lt;= #{param.endTime}
        </if>
        GROUP BY t.create_day, t.pay_method
    </select>

    <select id="exportList" resultType="com.hishop.wine.model.vo.bill.BillVo">
        SELECT
            t.create_time, t.transaction_type, t.business_no, t.third_transaction_no, t.pay_method, t.amount, t.user_name
        FROM hishop_bill t
        WHERE t.iz_delete = 0
        <if test="param.startTime != null">
            AND t.create_time >= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            AND t.create_time &lt;= #{param.endTime}
        </if>
        <if test="param.payMethod != null">
            AND t.pay_method = #{param.payMethod}
        </if>
        ORDER BY t.create_time DESC
    </select>


</mapper>

