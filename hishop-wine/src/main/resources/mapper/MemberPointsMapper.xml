<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.MemberPointsMapper">

    <!-- identity_type=2 代表消费者，即会员。会员的积分没有区分业务系统，所以身份表分组处理了 -->
    <select id="qryMemberPoints" resultType="com.hishop.wine.repository.dto.points.MemberPointsDTO">
        select hi.user_id, hu.mobile as userPhone, hu.nick_name, hu.icon, hu.create_time as registerTime,
               hmp.total_points,hmp.available_points,hmp.consumed_points,hmp.expired_points,hi.identity_type
        from hishop_member_points hmp
        inner join hishop_user hu on hmp.user_id=hu.id
        inner join (select user_id,identity_type from hishop_identity group by user_id,identity_type) hi on hi.user_id=hmp.user_id and hi.identity_type=hmp.identity_type
        <where>
            <if test="param.userId != null">
                and hmp.user_id = #{param.userId}
            </if>
            <if test="param.identityType != null">
                and hi.identity_type=#{param.identityType}
            </if>
            <if test="param.userPhone != null and param.userPhone != ''">
                and hu.mobile like concat('%',#{param.userPhone},'%')
            </if>
            <if test="param.nickName != null and param.nickName != ''">
                and hu.nick_name like concat('%',#{param.nickName},'%')
            </if>
            <if test="param.searchKey != null and param.searchKey != ''">
                and (hu.mobile like concat('%',#{param.searchKey},'%') or hu.nick_name like concat('%',#{param.searchKey},'%'))
            </if>
        </where>
        <if test="param.sortSql != null and param.sortSql != ''">
            ${param.sortSql}
        </if>
    </select>

    <select id="summary" resultType="com.hishop.wine.repository.dto.points.MemberPointsSummaryDTO">
        select sum(hmp.total_points) as totalPoints, sum(hmp.available_points) as availablePoints,
               sum(hmp.consumed_points) as consumedPoints, sum(hmp.expired_points) as expiredPoints
        from hishop_member_points hmp
        inner join (select user_id,identity_type from hishop_identity group by user_id,identity_type) hi on hmp.user_id=hi.user_id and hi.identity_type=hmp.identity_type
        inner join hishop_user hu on hi.user_id=hu.id
        <where>
            <if test="param.userId != null">
                and hmp.user_id = #{param.userId}
            </if>
            <if test="param.identityType != null">
                and hi.identity_type=#{param.identityType}
            </if>
            <if test="param.userPhone != null and param.userPhone != ''">
                and hu.mobile like concat('%',#{param.userPhone},'%')
            </if>
            <if test="param.nickName != null and param.nickName != ''">
                and hu.nick_name like concat('%',#{param.nickName},'%')
            </if>
            <if test="param.searchKey != null and param.searchKey != ''">
                and (hu.mobile like concat('%',#{param.searchKey},'%') or hu.nick_name like concat('%',#{param.searchKey},'%'))
            </if>
        </where>
    </select>

</mapper>