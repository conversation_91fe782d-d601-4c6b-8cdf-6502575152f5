<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.LoginErrorMapper">

    <resultMap type="com.hishop.wine.repository.entity.LoginError" id="LoginErrorMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="openId" column="open_id" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="INTEGER"/>
        <result property="updateBy" column="update_by" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="izDelete" column="iz_delete" jdbcType="VARCHAR"/>
    </resultMap>

</mapper>

