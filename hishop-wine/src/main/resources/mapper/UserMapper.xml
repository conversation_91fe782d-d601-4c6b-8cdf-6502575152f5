<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.UserMapper">

    <select id="pageListAdmin" resultType="com.hishop.wine.repository.entity.User">
        SELECT
            i.id,
            u.username,
            u.mobile,
            u.`password`,
            u.real_name,
            u.icon,
            u.email,
            u.id_card,
            i.`status`,
            i.create_by,
            i.create_time,
            i.update_by,
            i.update_time,
            i.role_id,
            d.department_id,
            i.user_id
        FROM
            hishop_user u
        INNER JOIN hishop_identity i ON u.id = i.user_id
        LEFT JOIN hishop_department_employee d ON u.id = d.user_id
        WHERE
            i.iz_delete = 0
        <if test="param.status != null">
            AND i.status = #{param.status}
        </if>
        <if test="param.identityType != null">
            AND i.identity_type = #{param.identityType}
        </if>
        <if test="param.identityTypeList != null and param.identityTypeList.size > 0 ">
            AND i.identity_type in
            <foreach collection="param.identityTypeList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="param.roleId != null and param.roleId != 0 ">
            AND i.role_id = #{param.roleId}
        </if>

        <if test="param.searchValue != null and param.searchValue != ''">
            AND (
                u.username LIKE CONCAT('%', #{param.searchValue}, '%')
                OR u.real_name LIKE CONCAT('%', #{param.searchValue}, '%')
                OR u.mobile LIKE CONCAT('%', #{param.searchValue}, '%')
                OR u.email LIKE CONCAT('%', #{param.searchValue}, '%')
            )
        </if>
        <if test="param.name != null and param.name != ''">
            AND u.real_name LIKE CONCAT('%', #{param.name}, '%')
        </if>
        <if test="param.mobile != null and param.mobile != ''">
            AND u.mobile LIKE CONCAT('%', #{param.mobile}, '%')
        </if>
    </select>


    <select id="pageListAdminDep" resultType="com.hishop.wine.model.vo.basic.AdminVO">
        SELECT i.id,
        u.id as userId,
        u.username,
        u.mobile,
        u.`password`,
        u.real_name,
        u.icon,
        u.email,
        u.id_card,
        i.`status`,
        i.create_by,
        i.create_time,
        i.update_by,
        i.update_time,
        i.role_id,
        d.department_id,
        IFNULL(d.iz_head,0) izHead,
        r.name as roleName
        FROM hishop_user u
        INNER JOIN hishop_identity i ON u.id = i.user_id AND i.iz_delete = 0
        <if test="param.identityType != null">
            AND i.identity_type = #{param.identityType}
        </if>
        <if test="param.identityType == null">
            AND i.identity_type = 1
        </if>
        LEFT JOIN hishop_department_employee d ON u.id = d.user_id
        LEFT JOIN hishop_role r ON r.id = i.role_id AND r.iz_delete = 0
        WHERE u.iz_delete = 0
        <if test="param.roleId != null and param.roleId != 0">
            AND i.role_id = #{param.roleId}
        </if>

        <if test="param.departmentId != null">
            and d.department_id =#{param.departmentId}
        </if>

        <if test="param.searchValue != null and param.searchValue != ''">
            AND (
                    u.username LIKE CONCAT('%', #{param.searchValue}, '%')
                    OR u.real_name LIKE CONCAT('%', #{param.searchValue}, '%')
                    OR u.mobile LIKE CONCAT('%', #{param.searchValue}, '%')
                )
        </if>


        <if test="param.headFlag">
            ORDER BY izHead desc,u.create_time desc
        </if>
    </select>


    <select id="qryUserId" resultType="java.lang.Long">
        select distinct hu.id
        from hishop_user hu
        inner join hishop_mini_user hmu on hu.id=hmu.user_id
        <where>
            <if test="param.nickNameLike != null and param.nickNameLike != ''">
                and hmu.nick_name like CONCAT('%', #{param.nickNameLike}, '%')
            </if>
            <if test="param.mobileLike != null and param.mobileLike != ''">
                and hu.mobile like CONCAT('%', #{param.mobileLike}, '%')
            </if>
            <if test="param.searchKey != null and param.searchKey != ''">
                and (
                    hu.mobile like CONCAT('%', #{param.searchKey}, '%')
                    or
                    hmu.nick_name like CONCAT('%', #{param.searchKey}, '%')
                    or
                    hu.username like CONCAT('%', #{param.searchKey}, '%')
                )
            </if>
        </where>
    </select>

    <select id="pageListMember" resultType="com.hishop.wine.repository.dto.member.MemberDTO">
        SELECT
            u.id,
            u.mobile,
            u.nick_name,
            u.icon,
            i.create_time `register_time`,
            IFNULL(r.rank_name, '-') `rank_name`,
            r.id `rank_id`,
            r.`status` `rank_status`
        FROM
            hishop_user u
        INNER JOIN hishop_identity i ON u.id = i.user_id
        LEFT JOIN hishop_rank r ON u.rank_id = r.id AND r.iz_delete = 0
        <where>
            <if test="param.memberSearchKey != null and param.memberSearchKey  != ''">
                AND
                (
                    u.mobile LIKE CONCAT('%', #{param.memberSearchKey}, '%')
                    OR
                    u.nick_name LIKE CONCAT('%', #{param.memberSearchKey}, '%')
                )
            </if>
            <if test="param.rankId != null and param.rankId > 0">
                AND u.rank_id = #{param.rankId}
            </if>
            <if test="param.startTime != null">
                AND i.create_time &gt;= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                AND i.create_time &lt;= #{param.endTime}
            </if>
            <if test="param.userId != null">
                AND u.id = #{param.userId}
            </if>
            AND i.iz_delete = 0
            AND i.identity_type = 2
        </where>
        GROUP BY u.id
        <if test="param.sortField != null and param.sortField.registerTime != null">
            <if test="param.sortField.registerTime == true">
                ORDER BY i.create_time ASC
            </if>
            <if test="param.sortField.registerTime == false">
                ORDER BY i.create_time DESC
            </if>
        </if>
        <if test="param.sortField == null || param.sortField.registerTime == null">
            ORDER BY i.create_time DESC
        </if>
    </select>

    <select id="listMemberSelect" resultType="com.hishop.wine.repository.dto.member.MemberSelectDTO">
        SELECT
            u.id,
            u.nick_name,
            u.mobile
        FROM
            hishop_user u
        INNER JOIN hishop_identity i ON u.id = i.user_id
        WHERE i.iz_delete = 0 AND i.identity_type = 2
        <if test="param.memberSearchKey != null and param.memberSearchKey  != ''">
            AND
            (
                u.mobile LIKE CONCAT('%', #{param.memberSearchKey}, '%')
                OR
                u.nick_name LIKE CONCAT('%', #{param.memberSearchKey}, '%')
            )
        </if>
        <if test="param.userId != null">
            AND u.id = #{param.userId}
        </if>
        GROUP BY u.id
    </select>

</mapper>