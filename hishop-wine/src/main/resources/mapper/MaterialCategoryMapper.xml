<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.MaterialCategoryMapper">

    <update id="batchMove">
        UPDATE hishop_material_category SET `parent_id` = #{targetId}
        WHERE id IN
        <foreach collection="ids" item= "id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <select id="listAllCategories" resultType="com.hishop.wine.repository.entity.MaterialCategory">
        SELECT
            c.*,
            (
                SELECT
                    COUNT(*)
                FROM
                    hishop_material m
                WHERE
                    m.material_category_id IN ( SELECT c2.id FROM hishop_material_category c2 WHERE FIND_IN_SET( c.id, c2.parent_ids ) )
                AND m.status != 0
            ) `totalMaterial`
        FROM
            hishop_material_category c
        WHERE
            c.type = #{materialType}
        GROUP BY
            c.id
    </select>

    <select id="listChildCategoryIds" resultType="long">
        SELECT id FROM hishop_material_category WHERE FIND_IN_SET(#{materialCategoryId}, parent_ids)
    </select>

</mapper>