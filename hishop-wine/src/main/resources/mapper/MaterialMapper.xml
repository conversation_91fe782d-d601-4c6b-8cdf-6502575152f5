<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.MaterialMapper">

    <update id="updateMaterialCategoryIdByIds">
        UPDATE `hishop_material` SET `material_category_id` = #{newMaterialCategoryId}
        WHERE id IN
        <foreach collection="ids" item= "id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <select id="getMaterialTypesByIds" resultType="java.lang.Integer" >
        SELECT `type` from `hishop_material` WHERE id IN
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>


    <select id="getCountByMaterialCategoryId" resultType="java.lang.Integer" >
        SELECT COUNT(1) FROM `hishop_material` WHERE `material_category_id` = #{materialCategoryId} AND `status` = 1
    </select>

</mapper>