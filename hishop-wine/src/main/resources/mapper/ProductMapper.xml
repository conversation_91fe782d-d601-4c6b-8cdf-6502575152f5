<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.ProductMapper">

    <select id="queryProductPage" resultType="com.hishop.wine.repository.dto.ProductPageDTO">
        SELECT
            p.id,
            p.product_code,
            p.product_name,
            p.product_img,
            p.market_price,
            c.id `product_category_id`,
            c.category_name,
            p.product_type,
            p.create_time
        FROM
            hishop_product p
        INNER JOIN hishop_product_category c ON p.product_category_id = c.id
        <where>
            <if test="param.filterDelete == null || param.filterDelete">
                p.iz_delete = 0
            </if>
            <if test="param.searchValue != null and param.searchValue != ''">
                AND
                (
                p.product_name LIKE CONCAT('%', #{param.searchValue}, '%')
                OR c.category_name LIKE CONCAT('%', #{param.searchValue}, '%')
                OR p.product_code LIKE CONCAT('%', #{param.searchValue}, '%')
                )
            </if>
            <if test="param.productType != null and param.productType != 0">
                AND p.product_type = #{param.productType}
            </if>
            <if test="param.productCategoryId != null and param.productCategoryId != 0">
                AND p.product_category_id = #{param.productCategoryId}
            </if>
        </where>
        ORDER BY p.id DESC
    </select>

    <update id="logicDeleteByIds">
        UPDATE hishop_product
        SET iz_delete = id, update_time = now(), update_by = #{userId}
        WHERE id IN
        <foreach collection="ids" item= "id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>
</mapper>