<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.MicropageCategoryMapper">

    <select id="listAllCategorys" resultType="com.hishop.wine.repository.entity.MicropageCategory">
        SELECT c.*,
               (
                   SELECT
                          count(*)
                   FROM hishop_micropage m
                   WHERE
                         m.category_id IN
                   (
                       SELECT
                              c1.id
                       FROM hishop_micropage_category c1
                       WHERE
                             FIND_IN_SET(c.id, c1.path) or c.id = c1.id
                   )
                   AND m.iz_delete = 0
               ) as file_count
        FROM hishop_micropage_category c
        <where>
            <if test="name != null and name != ''">
                AND c.name LIKE CONCAT('%', #{name}, '%')
            </if>
        </where>
    </select>


    <select id="recursionChildIds" resultType="long">
        SELECT id FROM hishop_micropage_category c WHERE FIND_IN_SET(#{pid}, c.path)
    </select>

</mapper>