<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.LogisticsCodeMapper">

    <select id="queryPage" resultType="com.hishop.wine.model.vo.logisticsCode.LogisticsCodeVo">
        SELECT t.id, t.code_first, t.code_secondary, t.code_bottle, t.product_code,
               t.code_type, t.status, t.code_category, t.file_import_id, t.iz_delete,
               t.create_by, t.create_time, t.update_by, t.update_time, t1.name, t.product_id, t.num, t2.product_name as productName
        FROM hishop_logistics_code t
            left join hishop_file_import_record t1 ON t.file_import_id = t1.id
            left join hishop_product t2 ON t.product_id = t2.id
        <where>
            t.iz_delete = 0
            <if test="param.logisticsCode != null and param.logisticsCode != ''">
                AND (t.code_first LIKE CONCAT('%', #{param.logisticsCode}, '%')
                    or t.code_secondary LIKE CONCAT('%', #{param.logisticsCode}, '%'))
            </if>
            <if test="param.status != null">
                AND t.`status` = #{param.status}
            </if>
            <if test="param.startTime != null and param.startTime != ''">
                AND t.create_time >= #{param.startTime}
            </if>
            <if test="param.endTime != null and param.endTime != ''">
                AND t.create_time &lt;= #{param.endTime}
            </if>
            <if test="param.name != null and param.name != ''">
                AND t1.name LIKE CONCAT('%', #{param.name}, '%')
            </if>
            <if test="param.codeCategory != null">
                AND t.code_category LIKE CONCAT('%', #{param.codeCategory}, '%')
            </if>
            order by t.id desc
        </where>
    </select>

</mapper>