<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.SmsRecordMapper">

    <select id="qryPage" resultType="com.hishop.wine.repository.entity.SmsRecord">
        SELECT
            m.id,
            r.channel,
            r.content,
            r.send_time,
            r.status,
            (SELECT GROUP_CONCAT(m2.mobile) FROM hishop_sms_record_mobile m2 WHERE r.id = m2.sms_record_id) mobiles
        FROM
            hishop_sms_record r
        INNER JOIN hishop_sms_record_mobile m ON r.id = m.sms_record_id
        <where>
            <if test="param.mobile != null and param.mobile != ''">
                AND m.mobile = #{param.mobile}
            </if>
            <if test="param.startTime != null">
                AND r.send_time &gt;= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                AND r.send_time &lt;= #{param.endTime}
            </if>
        </where>
        ORDER BY r.id DESC
    </select>

</mapper>