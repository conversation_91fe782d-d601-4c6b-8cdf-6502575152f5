<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.DealerMapper">

    <select id="queryDealerPageList" resultType="com.hishop.wine.model.vo.dealer.DealerVo">
        SELECT t.id, t.dealer_code, t.dealer_name, t.province_id, t.province_name
             , t.city_id, t.city_name, t.district_name, t.district_id, t.address
             , t.iz_enable, t1.real_name as business_name,t1.mobile as business_phone,t.phone, t.duty_name
        FROM hishop_dealer t
        LEFT JOIN hishop_user t1 ON t.business_user_id = t1.id
        WHERE t.iz_delete = 0
        <if test="param.dutyNameOrPhone != null and param.dutyNameOrPhone != ''">
            AND (t.phone like CONCAT('%', #{param.dutyNameOrPhone}, '%') OR t.duty_name like CONCAT('%', #{param.dutyNameOrPhone}, '%'))
        </if>
        <if test="param.dealerCodeOrName != null and param.dealerCodeOrName != ''">
            AND (t.dealer_code like CONCAT('%', #{param.dealerCodeOrName}, '%') OR t.dealer_name like CONCAT('%', #{param.dealerCodeOrName}, '%'))
        </if>
        <if test="param.izEnable != null ">
            AND t.iz_enable = #{param.izEnable}
        </if>
        <if test="param.provinceId != null ">
            AND t.province_id = #{param.provinceId}
        </if>
        <if test="param.cityId != null ">
            AND t.city_id = #{param.cityId}
        </if>
        <if test="param.districtId != null ">
            AND t.district_id = #{param.districtId}
        </if>
        <if test="param.businessUserId != null ">
            AND t.business_user_id = #{param.businessUserId}
        </if>
        <if test="param.saleAreaCode != null and param.saleAreaCode != ''">
            AND EXISTS (
                SELECT 1 FROM hishop_dealer_area t3, hishop_sale_area t4
                WHERE t3.dealer_id = t.id AND t3.sale_area_id = t4.id
                AND t4.full_code like CONCAT(#{param.saleAreaCode}, '%')
                AND t4.iz_delete = 0 and t3.iz_delete = 0
            )
        </if>
        ORDER BY t.create_time DESC
    </select>


</mapper>

