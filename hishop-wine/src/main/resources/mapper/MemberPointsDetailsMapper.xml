<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.MemberPointsDetailsMapper">

    <select id="qryDetail" resultType="com.hishop.wine.repository.dto.points.MemberPointsDetailDTO">
        select hi.user_id, hmpd.user_phone, hmu.nick_name, hmpd.modified_type, hmpd.modified_points,
               hmpd.biz_type, hmpd.create_time, hmpd.modified_remark,hmpd.module_code,hi.identity_type
        from hishop_member_points_details hmpd
        inner join hishop_identity hi on hmpd.user_id=hi.user_id
        inner join hishop_mini_user hmu on hmpd.user_id=hmu.user_id
        inner join hishop_user hu on hmpd.user_id=hu.id
        left join hishop_member_points hmp on hi.user_id=hmp.user_id
        <where>
            <if test="param.identityType != null">
                and hi.identity_type=#{param.identityType}
            </if>
            <if test="param.moduleCode != null and param.moduleCode != ''">
                and hmpd.module_code=#{param.moduleCode}
            </if>
            <if test="param.userPhone != null and param.userPhone != ''">
                and hmpd.user_phone like concat('%',#{param.userPhone},'%')
            </if>
            <if test="param.nickName != null and param.nickName != ''">
                and hmu.nick_name like concat('%',#{param.nickName},'%')
            </if>
            <if test="param.userId != null">
                and hmpd.user_id = #{param.userId}
            </if>
            <if test="param.modifiedType != null">
                and hmpd.modified_type = #{param.modifiedType}
            </if>
            <if test="param.bizType != null">
                and hmpd.biz_type = #{param.bizType}
            </if>
            <if test="param.startTime != null">
                and hmpd.create_time &gt;= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                and hmpd.create_time &lt;= #{param.endTime}
            </if>
            <if test="param.searchKey != null and param.searchKey != ''">
                and (hu.mobile like concat('%',#{param.searchKey},'%') or hmu.nick_name like concat('%',#{param.searchKey},'%'))
            </if>
        </where>
        order by hmpd.create_time desc
    </select>

    <select id="getUserIncreasePointsSummary" resultType="java.lang.Integer">
        select sum(modified_points) as totalPoints
        from hishop_member_points_details
        where user_id=#{userId} and identity_type=#{identityType} and modified_type=1
          and id>=#{minId} and create_time <![CDATA[ <= ]]> #{maxCreateTime}
    </select>

    <select id="summaryPointsModified" resultType="com.hishop.wine.repository.dto.points.PointsModifiedSummaryDTO">
        SELECT
            d.modified_type `modifiedType`,
            sum( d.modified_points ) `points`
        FROM
            hishop_member_points_details d
        WHERE
            d.user_id = #{userId}
        AND d.identity_type = #{identityType}
        AND d.create_time <![CDATA[ <= ]]> #{endTime}
        AND d.create_time <![CDATA[ >= ]]> #{startTime}
        GROUP BY
            d.modified_type
    </select>

</mapper>