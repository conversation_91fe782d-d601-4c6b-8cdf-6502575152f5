server:
  # 开启压缩
  compression:
    enabled: true
    min-response-size: 2048
  #上下文路径
  servlet:
    context-path: /wine
  port: 80
spring:
  application:
    name: wine
  main:
    allow-bean-definition-overriding: true
  datasource:
    url: **************************************************************************************************************************************************************************************************************************************************************************************************
    username: wine-user
    password: ZcHi8qkIV8tCgEvkTk
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 0
      maximum-pool-size: 20
      idle-timeout: 10000
      auto-commit: true
      connection-test-query: SELECT 1
  redis:
    host: **************
    port: 6379
    database: 13
  #文件上传设置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
# mybaits-plus配置
mybatis-plus:
  configuration:
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
swagger:
  knife4j:
    enable: true
    apiInfo:
      title: 酒客多-基础系统接口文档
      description: 酒客多-基础系统接口文档
      version: v1.0
      apiUrl: http://127.0.0.1:8080/wine/doc.html
      baseApiPkg: com.hishop.wine.controller
      groupName: hishopBasic
      contactEmail: <EMAIL>
      contactName: hishop
rocketmq:
  name-server: **************:9876
  producer:
    group: DEFAULT_TRANSACTION_PRODUCER_GROUP
remote:
  api:
    wine: http://127.0.0.1:9080/wine
#测试打开健康检查端口
management:
  endpoints:
    web:
      exposure:
        include: health,info
hishop:
  wx:
    notify:
      pay:
        url: http://63476q.natappfree.cc/wine/transaction/notify/pay
      refund:
        url: http://63476q.natappfree.cc/wine/transaction/notify/refund
  email:
    host: smtp.163.com
    port: 25
    username: <EMAIL>
    password: FJKRTYMAUTOMSGGJ
  auth:
    domain: admin-dev.jkd.35hiw.com
  h5:
    domain: https://admin-dev.jkd.35hiw.com
  default:
    path: https://admin-dev.jkd.35hiw.com/#/login
  setting:
    mode: cluster
  jiukeduo:
    website: https://www.jiukeduo.cn
nfs:
  obs:
    accessKeyId: UFZKPSFWLGLVK7LR24HA
    accessKeySecret: X4R2PK6tnhEs91di1hUWvT4UtZoGlQUbIsUo084g
    bucketName: hishop-wine
    endPoint: obs.cn-south-1.myhuaweicloud.com
    domain: hishop-wine.obs.cn-south-1.myhuaweicloud.com
    region: cn-south-1
  mpc:
    productId: 6b5e628ef4f4473aa80b2e9195137687
    endpoint: https://mpc.cn-south-1.myhuaweicloud.com
    templateId: 7000524
    audioTemplateId: 7001053
wx:
  mini:
    config: 2d960bec124890b2cff66d0c36518822
    tokenUrl: https://api.weixin.qq.com/cgi-bin/token
    uploadUrl: https://api.weixin.qq.com/wxa/sec/order/upload_shipping_info
    envVersion: trial