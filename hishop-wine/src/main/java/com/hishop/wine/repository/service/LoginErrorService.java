package com.hishop.wine.repository.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.wine.model.po.LoginErrorQueryPO;
import com.hishop.wine.model.po.bill.BillQueryPo;
import com.hishop.wine.repository.entity.Bill;
import com.hishop.wine.repository.entity.District;
import com.hishop.wine.repository.entity.LoginError;
import org.springframework.data.domain.PageRequest;

/**
 * 登录异常表(LoginError)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-13 10:40:54
 */
public interface LoginErrorService extends IService<LoginError> {

    Page<LoginError> queryPage(Page<LoginError> pageInfo, LoginErrorQueryPO loginErrorQueryPo);

}
