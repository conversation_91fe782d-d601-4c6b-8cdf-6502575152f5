package com.hishop.wine.repository.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;
import com.hishop.common.pojo.entity.BaseEntity;

/**
 * 经销商表(Dealer)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-04 15:53:16
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("hishop_dealer")
public class Dealer extends BaseEntity {

    /**
     * 经销商ID=主键
     **/
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 经销商编码
     **/
    private String dealerCode;
    /**
     * 经销商名称
     **/
    private String dealerName;
    /**
     * 省份ID
     **/
    private Integer provinceId;
    /**
     * 省份名称
     **/
    private String provinceName;
    /**
     * 城市ID
     **/
    private Integer cityId;
    /**
     * 城市名称
     **/
    private String cityName;
    /**
     * 区县ID
     **/
    private Integer districtId;
    /**
     * 区县名称
     **/
    private String districtName;
    /**
     * 详细地址
     **/
    private String address;
    /**
     * 备注
     **/
    private String remark;
    /**
     * 业务员(用户表id)
     **/
    private Long businessUserId;
    /**
     * 负责人姓名
     **/
    private String dutyName;
    /**
     * 手机号
     **/
    private String phone;
    /**
     * 电子邮箱
     **/
    private String mail;
    /**
     * 是否启用
     **/
    private Boolean izEnable;
}

