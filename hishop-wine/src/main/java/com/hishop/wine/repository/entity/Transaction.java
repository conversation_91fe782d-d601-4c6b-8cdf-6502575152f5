package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**   
 * 交易流水表 实体类
 * @author: HuBiao
 * @date: 2023-08-04
 */

@Data
@NoArgsConstructor
@TableName("hishop_transaction")
public class Transaction implements Serializable {

	private static final long serialVersionUID = 1687264348979617792L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Long id;

    /**
     * 交易流水
     */
    private String transactionNo;
    
    /**
    * 模块编码
    */
	private String moduleCode;
    
    /**
    * 交易方式 WX_PAY-微信支付 WX_SEC_PAY-微信服务商支付 {@link com.hishop.wine.enums.TransactionEnum.MethodEnum}
    */
	private String transactionMethod;
    
    /**
    * 交易类型 PAY-付款 REFUND-退款 {@link com.hishop.wine.enums.TransactionEnum.Type}
    */
	private String transactionType;
    
    /**
    * 商户id
    */
	private String mchId;
    
    /**
    * app_id
    */
	private String appId;
    
    /**
    * 用户id
    */
	private Long userId;

    /**
     * 支付用户 openId
     */
	private String openId;
    
    /**
    * 业务类型 {@link com.hishop.wine.enums.TransactionEnum.BizTypeEnum}
    */
	private String bizType;
    
    /**
    * 业务编码(用来存放订单号)
    */
	private String bizCode;
    
    /**
    * 交易金额
    */
	private BigDecimal amount;
    
    /**
    * 描述
    */
	private String description;
    
    /**
    * 第三方支付流水号
    */
	private String thirdTransactionNo;
    
    /**
    * 状态 1-待确认 2-交易成功 3-交易失败
    */
	private Integer status;
    
    /**
    * 第三方状态
    */
	private String thirdStatus;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 原交易id(如果是交易类型为退款才存在)
     */
    private Long orgTransactionId;

    /**
     * 退款状态 1-未退款 2-部分退款 3-全部退款 (只有是交易为付款时该值才有效)
     */
    private Integer refundStatus;

    /**
     * 退款金额 (只有是交易为付款时该值才有效)
     */
    private BigDecimal refundAmount;

    /**
     * 完成时间
     */
	private Date finishTime;
    
    /**
    * 创建者ID
    */
    @TableField(fill = FieldFill.INSERT)
	private Long createBy;
    
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
	private Date createTime;
    
    /**
    * 更新者ID
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updateBy;
    
    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

//    /**
//     * 服务商id
//     */
//    private String spMchId;
//
//    /**
//     * 服务商小程序id
//     */
//    private String spAppId;
//
//    /**
//     * 支付类型
//     */
//    private String paymentType;
    

}
