package com.hishop.wine.repository.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;
import com.hishop.common.pojo.entity.BaseEntity;

/**
 * 瓶内码管理表(LogisticsCodeBottle)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-09 11:39:10
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("hishop_logistics_code_bottle")
public class LogisticsCodeBottle extends BaseEntity {

    /**
     * 主键
     **/
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 瓶内码
     **/
    private String codeBottle;
    /**
     * 物流码id
     **/
    private Long logisticsCodeId;

    /**
     * 批次id
     */
    private Long fileImportId;

    /**
     * 物流码类型 0盒码 1箱码
     */
    private Integer codeType;

    /**
     * 一级物流编码
     */
    private String codeFirst;

    /**
     * 二级物流编码
     */
    private String codeSecondary;

    /**
     * 物流码类别 0物流码 1扫码营销物流码
     */
    private Integer codeCategory;




}

