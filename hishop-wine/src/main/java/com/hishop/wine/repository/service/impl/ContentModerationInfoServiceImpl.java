package com.hishop.wine.repository.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hishop.common.util.MysqlPlusUtil;
import com.hishop.wine.enums.ModerationEnum;
import com.hishop.wine.repository.entity.ContentModerationInfo;
import com.hishop.wine.repository.dao.ContentModerationInfoMapper;
import com.hishop.wine.repository.param.moderation.ContentModerationInfoParam;
import com.hishop.wine.repository.service.ContentModerationInfoService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * 内容审核明细表 服务实现类
 *
 * @author: HuBiao
 * @date: 2023-09-11
 */

@Service
public class ContentModerationInfoServiceImpl extends ServiceImpl<ContentModerationInfoMapper, ContentModerationInfo> implements ContentModerationInfoService {

    /**
     * 统计数量
     *
     * @param param 查询参数
     * @return 数量
     */
    @Override
    public Long count(ContentModerationInfoParam param) {
        return count(buildCommonWrapper(param));
    }

    /**
     * 查询明细列表
     *
     * @param param
     * @return
     */
    @Override
    public List<ContentModerationInfo> list(ContentModerationInfoParam param) {
        return list(buildCommonWrapper(param));
    }

    /**
     * 构建查询条件
     *
     * @param param
     */
    private LambdaQueryWrapper<ContentModerationInfo> buildCommonWrapper(ContentModerationInfoParam param) {
        LambdaQueryWrapper<ContentModerationInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjectUtil.isNotNull(param.getModerationId()), ContentModerationInfo::getModerationId, param.getModerationId())
                .eq(StrUtil.isNotEmpty(param.getStatus()), ContentModerationInfo::getStatus, param.getStatus());

        MysqlPlusUtil.setOrder(wrapper, param.getSortField());
        if (ObjectUtil.isNotNull(param.getMaxLimit())) {
            wrapper.last("limit " + param.getMaxLimit());
        }
        return wrapper;
    }
}