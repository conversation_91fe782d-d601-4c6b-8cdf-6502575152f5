package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.hishop.wine.model.po.LoginErrorQueryPO;
import com.hishop.wine.repository.entity.LoginError;
import com.hishop.wine.repository.dao.LoginErrorMapper;
import com.hishop.wine.repository.service.LoginErrorService;

/**
 * 登录异常表(LoginError)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-13 10:40:54
 */
@Service
public class LoginErrorServiceImpl extends ServiceImpl<LoginErrorMapper, LoginError> implements LoginErrorService {

    @Override
    public Page<LoginError> queryPage(Page<LoginError> pageInfo, LoginErrorQueryPO loginErrorQueryPo) {
        LambdaQueryWrapper<LoginError> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LoginError::getIzDelete, false);
        if(StringUtils.isNotBlank(loginErrorQueryPo.getMobile())) {
            queryWrapper.eq(LoginError::getMobile, loginErrorQueryPo.getMobile());
        }
        queryWrapper.orderByDesc(LoginError::getCreateTime);
        return this.page(pageInfo, queryWrapper);
    }
}
