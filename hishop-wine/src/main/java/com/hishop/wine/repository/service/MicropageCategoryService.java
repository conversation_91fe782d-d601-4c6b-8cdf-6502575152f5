package com.hishop.wine.repository.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;

import com.hishop.wine.repository.entity.MicropageCategory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MicropageCategoryService extends IService<MicropageCategory> {

    List<MicropageCategory> listAllCategorys(@Param("name") String name);

    List<Long> recursionChildIds(@Param("pid") Long pid);

    void deleteBatchIds(List<Long> ids);

    List<MicropageCategory> selectList(QueryWrapper<MicropageCategory> queryWrapper);
}
