package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**   
 * 物流公司表 实体类
 * @author: chenpeng
 * @date: 2023-07-08
 */

@Data
@NoArgsConstructor
@TableName("hishop_logistics_company")
public class LogisticsCompany implements Serializable {

	private static final long serialVersionUID = 1677507382023532544L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Integer id;
    
    /**
    * 物流公司名称
    */
	private String companyName;

	/**
     * 物流公司图片
     */
    private String companyUrl;

    /**
     * 物流公司编码
     */
    private String shipperCode;

    /**
     * 是否选中
     */
    private Boolean izSelected;

    /**
    * 创建者ID
    */
    @TableField(fill = FieldFill.INSERT)
	private Long createBy;
    
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
	private Date createTime;
    
    /**
    * 更新者ID
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updateBy;
    
    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
    

}
