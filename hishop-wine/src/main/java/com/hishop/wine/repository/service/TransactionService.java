package com.hishop.wine.repository.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.wine.repository.dto.transaction.TransactionTotalDto;
import com.hishop.wine.repository.entity.Transaction;
import com.hishop.wine.repository.param.transaction.TransactionParam;

import java.util.List;

/**
 * 交易流水表 数据层服务类
 *
 * @author: Hu<PERSON>iao
 * @date: 2023-06-28
 */
public interface TransactionService extends IService<Transaction> {

    /**
     * 判断交易是否成功
     *
     * @param bizType 业务类型
     * @param bizCode 业务编码
     * @return 是否成功
     */
    Boolean izTransactionSuccess(String bizType, String bizCode);

    /**
     * 判断交易待确认
     *
     * @param bizType 业务类型
     * @param bizCode 业务编码
     * @return 是否待确认
     */
    Boolean izTransactionWaiting(String bizType, String bizCode);

    /**
     * 根据状态统计交易记录
     *
     * @param bizType 业务类型
     * @param bizCode 业务编码
     * @param status  状态
     * @return 交易数量
     */
    Long countByStatus(String bizType, String bizCode, Integer status);

    /**
     * 重置退款信息
     *
     * @param transactionId
     */
    void resetRefundData(Long transactionId);

    /**
     * 查询交易流水
     *
     * @param page  分页参数
     * @param param 查询参数
     * @return 交易流水
     */
    Page<Transaction> queryTransaction(Page<Transaction> page, TransactionParam param);

    /**
     * 获取交易统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 交易统计
     */
    TransactionTotalDto getTransactionTotal(String startTime, String endTime);

    /**
     * 获取每日交易统计
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 交易统计
     */
    List<TransactionTotalDto> queryTransactionTotalList(String startTime, String endTime);
}