package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@TableName("hishop_storebar")
public class StoreBar implements Serializable {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private Date updateTime;
    private Date createTime;
    private Long createBy;
    private Long updateBy;
    private String barJson;
    private String bgColor;
    private String fontColor;
    private String selectColor;
    private String moduleCode;
    private String appId;
}
