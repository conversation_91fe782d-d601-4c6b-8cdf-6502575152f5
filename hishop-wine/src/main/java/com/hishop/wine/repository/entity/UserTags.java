package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**   
 * 用户标签表 实体类
 * @author: chenpeng
 * @date: 2023-07-17
 */

@Data
@NoArgsConstructor
@TableName("hishop_user_tags")
public class UserTags implements Serializable {

	private static final long serialVersionUID = 1680845990000267264L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Long id;
    
    /**
    * 用户ID
    */
	private Long userId;
    
    /**
    * 标签ID
    */
	private Long tagId;
    
    /**
    * 标签名称
    */
	private String tagName;
    
    /**
    * 创建者ID
    */
    @TableField(fill = FieldFill.INSERT)
	private Long createBy;
    
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
	private Date createTime;
    
    /**
    * 更新者ID
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updateBy;
    
    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
    

}
