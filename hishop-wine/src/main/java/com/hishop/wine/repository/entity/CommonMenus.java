package com.hishop.wine.repository.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;
import com.hishop.common.pojo.entity.BaseEntity;

/**
 * 用户常用功能 (CommonMenus)表实体类
 *
 * <AUTHOR>
 * @since 2024-06-25 14:33:32
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("hishop_common_menus")
public class CommonMenus extends BaseEntity {

    /**
     * 主键
     **/
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 菜单id
     **/
    private Long menuId;
    /**
     * 用户id
     **/
    private Long userId;
}

