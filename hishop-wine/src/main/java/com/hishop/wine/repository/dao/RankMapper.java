package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.repository.entity.Rank;
import com.hishop.wine.repository.param.rank.RankParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**   
 * 头衔表 Mapper 接口
 * @author: HuBiao
 * @date: 2023-07-25
 */

public interface RankMapper extends BaseMapper<Rank> {

    /**
     * 分页获取 头衔表
     *
     * @param pageInfo 分页参数
     * @param param    筛选参数
     * @return 头衔表列表
     */
    Page<Rank> qryPage(Page<Rank> pageInfo, @Param("param") RankParam param);

    /**
     * 获取 头衔表
     *
     * @param param    筛选参数
     * @return 头衔表列表
     */
    List<Rank> qryList(@Param("param") RankParam param);
}
