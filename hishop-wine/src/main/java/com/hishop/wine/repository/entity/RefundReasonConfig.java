package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**   
 * 退款原因配置表 实体类
 * @author: chenpeng
 * @date: 2023-07-08
 */

@Data
@NoArgsConstructor
@TableName("hishop_refund_reason_config")
public class RefundReasonConfig implements Serializable {

	private static final long serialVersionUID = 1677528019416813568L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Long id;

    /**
     * 退款类型，多个用逗号隔开
     */
    private String refundTypes;

    /**
    * 退款原因
    */
	private String refundReason;
    
    /**
    * 创建者
    */
    @TableField(fill = FieldFill.INSERT)
	private Long createBy;
    
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
	private Date createTime;
    
    /**
    * 更新者
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updateBy;
    
    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
    

}
