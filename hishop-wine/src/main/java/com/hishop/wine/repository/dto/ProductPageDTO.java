package com.hishop.wine.repository.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品分页dto
 *
 * <AUTHOR>
 * @date : 2023/6/19
 */
@Data
public class ProductPageDTO {

    /**
     * 产品id
     */
    private Long id;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品首图
     */
    private String productImg;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品价格
     */
    private BigDecimal marketPrice;

    /**
     * 分类id
     */
    private Long productCategoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 产品分类
     */
    private Integer productType;

    /**
     * 产品类型名称
     */
    private String productTypeName;

    /**
     * 创建时间
     */
    private Date createTime;

}
