package com.hishop.wine.repository.dto;

import lombok.Data;

/**
 * 微信登录数据
 *
 * <AUTHOR>
 * @date : 2023/6/29
 */
@Data
public class WxLoginDataDTO {

    private String sessionKey;

    private String openId;

    private String unionId;

    public static WxLoginDataDTO of(String sessionKey, String openId, String unionId) {
        WxLoginDataDTO wxLoginDataDTO = new WxLoginDataDTO();
        wxLoginDataDTO.setSessionKey(sessionKey);
        wxLoginDataDTO.setOpenId(openId);
        wxLoginDataDTO.setUnionId(unionId);
        return wxLoginDataDTO;
    }
}
