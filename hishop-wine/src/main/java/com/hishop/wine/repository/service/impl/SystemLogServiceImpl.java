package com.hishop.wine.repository.service.impl;

import com.hishop.wine.repository.entity.SystemLog;
import com.hishop.wine.repository.dao.SystemLogMapper;
import com.hishop.wine.repository.param.log.SystemLogParam;
import com.hishop.wine.repository.service.SystemLogService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;

/**   
 * 操作日志表 服务实现类
 * @author: HuBiao
 * @date: 2023-08-01
 */

@Service
public class SystemLogServiceImpl extends ServiceImpl<SystemLogMapper, SystemLog> implements SystemLogService  {

    /**
     * 分页查询日志记录
     *
     * @param pageInfo 分页参数
     * @param param    筛选参数
     * @return 日志记录
     */
    @Override
    public Page<SystemLog> qryPage(Page<SystemLog> pageInfo, SystemLogParam param) {
        return baseMapper.qryPage(pageInfo, param);
    }

    public List<SystemLog> qryList(SystemLogParam param) {
        LambdaQueryWrapper<SystemLog> wrapper = new LambdaQueryWrapper<>();
        return super.list(wrapper);
    }
}