package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;

/**   
 * 短信发送手机号表 实体类
 * @author: HuBiao
 * @date: 2023-07-12
 */

@Data
@NoArgsConstructor
@TableName("hishop_sms_record_mobile")
public class SmsRecordMobile implements Serializable {

	private static final long serialVersionUID = 1678971123667886080L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Long id;
    
    /**
    * 短信记录id
    */
	private Long smsRecordId;
    
    /**
    * 手机号
    */
	private String mobile;
    

}
