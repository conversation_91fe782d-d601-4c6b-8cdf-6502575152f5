package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.wine.repository.dao.LogisticsCodeBottleMapper;
import com.hishop.wine.repository.entity.LogisticsCodeBottle;
import com.hishop.wine.repository.service.LogisticsCodeBottleService;
import org.springframework.stereotype.Service;

/**
 * 瓶内码管理表(LogisticsCodeBottle)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-09 11:39:11
 */
@Service
public class LogisticsCodeBottleServiceImpl extends ServiceImpl<LogisticsCodeBottleMapper, LogisticsCodeBottle> implements LogisticsCodeBottleService {

}

