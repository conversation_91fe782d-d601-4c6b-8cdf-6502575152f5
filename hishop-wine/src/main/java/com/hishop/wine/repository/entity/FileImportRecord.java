package com.hishop.wine.repository.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;
import com.hishop.common.pojo.entity.BaseEntity;

/**
 * 导入记录表(ImportRecord)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-05 16:16:32
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("hishop_file_import_record")
public class FileImportRecord extends BaseEntity {

    /**
     * 导入记录ID=主键
     **/
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 导入名称
     */
    private String name;
    /**
     * 导入类型 {@link com.hishop.wine.common.enums.FileImportType}
     **/
    private String importType;
    /**
     * 导入状态。1：导入中；2：导入成功；3：导入失败；4：部分失败；5：导入异常
     **/
    private Integer importStatus;
    /**
     * 业务key
     **/
    private String bizCode;
    /**
     * 导入成功数量
     **/
    private Integer successCount;
    /**
     * 导入失败数量
     **/
    private Integer failCount;
    /**
     * 异常提示
     **/
    private String errMsg;
    /**
     * 导入文件名称
     **/
    private String fileName;
    /**
     * 导入耗时
     **/
    private Integer costTime;

    /**
     * 错误文件路径
     **/
    private String errFilePath;
}

