package com.hishop.wine.repository.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 资源分组树
 * @author: tomliu
 * @create: 2022/06/23 16:23
 **/
@Data
@ApiModel("资源分组树")
public class MaterialCategoryTreeDTO extends MaterialCategoryDTO {

    /**
     * 子树
     */
    @ApiModelProperty("子树")
    private  List<MaterialCategoryTreeDTO> children = new ArrayList<>();
}
