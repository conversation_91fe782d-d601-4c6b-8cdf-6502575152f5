package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.model.po.TagsQueryPO;
import com.hishop.wine.model.vo.tags.TagsVO;
import com.hishop.wine.repository.entity.Tags;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**   
 * 标签表 Mapper 接口
 * @author: chenpeng
 * @date: 2023-07-17
 */

public interface TagsMapper extends BaseMapper<Tags> {

    Page<Tags> pageList(Page<Tags> page, @Param("param") TagsQueryPO pagePO);

    List<TagsVO> getMyList(Long userId);

    List<TagsVO> getTopList(@Param("num")Integer num, @Param("tagIds")List<Long> tagIds, @Param("tagName")String tagName);
}
