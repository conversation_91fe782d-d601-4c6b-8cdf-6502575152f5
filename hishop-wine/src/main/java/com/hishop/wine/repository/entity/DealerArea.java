package com.hishop.wine.repository.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;
import com.hishop.common.pojo.entity.BaseEntity;

/**
 * 经销商销售区域关联表(DealerArea)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-04 15:53:35
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("hishop_dealer_area")
public class DealerArea extends BaseEntity {

    /**
     * 主键
     **/
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 经销商id
     **/
    private Long dealerId;
    /**
     * 销售区域id
     **/
    private Long saleAreaId;
}

