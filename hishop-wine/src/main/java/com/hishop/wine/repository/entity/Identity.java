package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hishop.common.pojo.entity.BaseEntity;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户身份表 实体类
 *
 * @author: HuBia<PERSON>
 * @date: 2023-06-21
 */
@Data
@NoArgsConstructor
@TableName("hishop_identity")
public class Identity extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1671412750474821632L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 身份类型 1-管理员 2-消费者 3-经销商 4-终端 5-业务员
     */
    private Integer identityType;

    /**
     * 模块编码
     */
    private String moduleCode;

    /**
     * 角色id null表示没有角色
     */
    private Long roleId;

    /**
     * 状态 0：禁用  1：正常
     */
    private Boolean status;

    /**
     * 邀请用户id 为0表示无邀请人
     */
    private Long inviterUserId;

    /**
     * 注册渠道
     */
    private String registerChannel;

    /**
     * 注册业务码
     */
    private String registerBizCode;

    /**
     * 注册后处理是否完成(用来做幂等)
     */
    private Boolean registerHandle;
}
