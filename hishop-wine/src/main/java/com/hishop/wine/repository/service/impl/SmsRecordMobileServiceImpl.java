package com.hishop.wine.repository.service.impl;

import com.hishop.wine.repository.entity.SmsRecordMobile;
import com.hishop.wine.repository.dao.SmsRecordMobileMapper;
import com.hishop.wine.repository.service.SmsRecordMobileService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**   
 * 短信发送手机号表 服务实现类
 * @author: HuBiao
 * @date: 2023-07-12
 */

@Service
public class SmsRecordMobileServiceImpl extends ServiceImpl<SmsRecordMobileMapper, SmsRecordMobile> implements SmsRecordMobileService  {

    /**
     * 关联短信手机号
     *
     * @param smsRecordMobileList 短信关联手机号集合
     */
    @Override
    public void insertSmsRecordMobileBatch(List<SmsRecordMobile> smsRecordMobileList) {
        if (CollectionUtils.isEmpty(smsRecordMobileList)) {
            return;
        }
        baseMapper.insertBatchSomeColumn(smsRecordMobileList);
    }
}