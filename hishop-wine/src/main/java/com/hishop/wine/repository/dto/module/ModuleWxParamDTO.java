package com.hishop.wine.repository.dto.module;

import lombok.Data;

/**
 * 模块微信参数映射
 *
 * <AUTHOR>
 * @date : 2023/7/3
 */
@Data
public class ModuleWxParamDTO {

    /**
     * 模块编码
     */
    private String moduleCode;

    /**
     * 小程序id
     */
    private String appId;

    /**
     * 要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版。
     */
    private String envVersion;

    /**
     * 微信商户id
     */
    private String mchId;

    /**
     * 是否支持服务商模式
     */
    private Boolean isSupportSec;

    /**
     * 服务商id
     */
    private String spMchId;

    /**
     * 服务商小程序id
     */
    private String spAppId;

    /**
     * 支付类型
     */
    private String paymentType;

}
