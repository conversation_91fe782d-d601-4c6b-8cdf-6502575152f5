package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.repository.dto.points.MemberPointsDTO;
import com.hishop.wine.repository.dto.points.MemberPointsSummaryDTO;
import com.hishop.wine.repository.entity.MemberPoints;
import com.hishop.wine.repository.param.MemberPointsParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**   
 * 会员积分表 Mapper 接口
 * @author: LiGuoQiang
 * @date: 2023-06-25
 */

public interface MemberPointsMapper extends BaseMapper<MemberPoints> {

    /**
     * 分页获取 会员积分
     * <AUTHOR>
     * @date 2023/6/25
     */
    Page<MemberPointsDTO> qryMemberPoints(Page<MemberPoints> pageInfo, @Param("param") MemberPointsParam param);

    /**
     * 统计会员积分汇总信息
     * <AUTHOR>
     * @date 2023/6/25
     */
    MemberPointsSummaryDTO summary(@Param("param") MemberPointsParam param);
}
