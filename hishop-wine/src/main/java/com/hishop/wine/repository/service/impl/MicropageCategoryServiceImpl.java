package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.wine.repository.dao.MicropageCategoryMapper;


import com.hishop.wine.repository.entity.MicropageCategory;
import com.hishop.wine.repository.service.MicropageCategoryService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MicropageCategoryServiceImpl extends ServiceImpl<MicropageCategoryMapper, MicropageCategory> implements MicropageCategoryService {
    @Override
    public List<MicropageCategory> listAllCategorys(String name) {
        return getBaseMapper().listAllCategorys(name);
    }

    @Override
    public List<Long> recursionChildIds(Long pid) {
        return getBaseMapper().recursionChildIds(pid);
    }

    @Override
    public void deleteBatchIds(List<Long> ids) {
        getBaseMapper().deleteBatchIds(ids);
    }

    @Override
    public List<MicropageCategory> selectList(QueryWrapper<MicropageCategory> queryWrapper) {
        return getBaseMapper().selectList(queryWrapper);
    }
}
