package com.hishop.wine.repository.service;

import com.hishop.wine.repository.dto.ProductPageDTO;
import com.hishop.wine.repository.entity.Product;
import com.hishop.wine.repository.param.ProductParam;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 产品表 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
public interface ProductService extends IService<Product> {

    /**
     * 分页查询产品列表
     *
     * @param pageInfo 分页信息
     * @param param    筛选参数
     * @return 产品列表
     */
    Page<ProductPageDTO> queryProductPage(Page<Product> pageInfo, ProductParam param);

    /**
     * 根据产品id的集合逻辑删除
     *
     * @param ids    id的集合
     * @param userId 操作人id
     */
    void logicDeleteByIds(List<Long> ids, Long userId);

    /**
     * 批量添加产品
     *
     * @param productList 产品集合
     */
    void insertProductBatch(List<Product> productList);

    List<Product> qryList(ProductParam param);
}