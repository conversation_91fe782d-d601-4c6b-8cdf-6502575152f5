package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

/**   
 * 会员积分表 实体类
 * @author: LiGuoQiang
 * @date: 2023-06-25
 */

@Data
@NoArgsConstructor
@TableName("hishop_member_points")
public class MemberPoints implements Serializable {

	private static final long serialVersionUID = 1672869949996929024L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Long id;
    
    /**
    * 用户ID
    */
	private Long userId;
    /**
     * 身份类型 1-管理员 2-消费者 3-经销商 4-终端
     */
    private Integer identityType;
    
    /**
    * 用户手机号码
    */
	private String userPhone;
    
    /**
    * 用户历史累计总积分
    */
	private Integer totalPoints;
    
    /**
    * 可用积分=当前剩余总积分
    */
	private Integer availablePoints;
    
    /**
    * 已消耗积分=用户正常使用掉的
    */
	private Integer consumedPoints;
    
    /**
    * 过期清零的积分
    */
	private Integer expiredPoints;
    
    /**
    * 已冻结的积分
    */
	private Integer frozenPoints;
    
    /**
    * 积分消耗对应的明细指针
    */
	private Long consumedLastDetailId;
    
    /**
    * 明细指针剩余积分
    */
	private Integer lastDetailRemainPoint;
    

}
