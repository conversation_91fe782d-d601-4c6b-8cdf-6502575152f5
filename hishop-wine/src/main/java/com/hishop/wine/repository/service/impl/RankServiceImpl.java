package com.hishop.wine.repository.service.impl;

import com.hishop.wine.repository.entity.Rank;
import com.hishop.wine.repository.dao.RankMapper;
import com.hishop.wine.repository.param.rank.RankParam;
import com.hishop.wine.repository.service.RankService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;

/**   
 * 头衔表 服务实现类
 * @author: HuBiao
 * @date: 2023-07-25
 */

@Service
public class RankServiceImpl extends ServiceImpl<RankMapper, Rank> implements RankService  {

    /**
     * 分页获取 头衔表
     *
     * @param pageInfo 分页参数
     * @param param    筛选参数
     * @return 头衔表列表
     */
    @Override
    public Page<Rank> qryPage(Page<Rank> pageInfo, RankParam param) {
        return baseMapper.qryPage(pageInfo, param);
    }

    /**
     * 获取 头衔表
     *
     * @param param    筛选参数
     * @return 头衔表列表
     */
    @Override
    public List<Rank> qryList(RankParam param) {
        return baseMapper.qryList(param);
    }
}