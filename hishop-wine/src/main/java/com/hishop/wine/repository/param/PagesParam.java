package com.hishop.wine.repository.param;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Builder;
import java.util.Date;

/**   
 * 页面配置表 数据库查询类
 * @author: HuBia<PERSON>
 * @date: 2023-07-07
 */

@Data
@Builder
public class PagesParam {


	private Long id;
    

	private String moduleCode;
    

	private String name;
    

	private String path;
    

	private Long createBy;
    

	private Date createTime;
    

	private Long updateBy;
    

	private Date updateTime;
    

}
