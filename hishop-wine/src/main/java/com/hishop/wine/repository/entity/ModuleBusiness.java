package com.hishop.wine.repository.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;
import com.hishop.common.pojo.entity.BaseEntity;

/**
 * 模块业务表(ModuleBusiness)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-22 16:31:35
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("hishop_module_business")
public class ModuleBusiness extends BaseEntity {

    /**
     * 主键
     **/
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 模块编码
     **/
    private String moduleBusinessCode;
    /**
     * 模块名称
     **/
    private String moduleName;
    /**
     * 业务名称
     **/
    private String businessName;
    /**
     * 小程序app_id, 为空则表示没有关联小程序
     **/
    private String appId;
}

