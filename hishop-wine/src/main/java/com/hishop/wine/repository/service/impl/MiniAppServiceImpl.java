package com.hishop.wine.repository.service.impl;

import com.hishop.wine.repository.dto.miniApp.MiniAppDTO;
import com.hishop.wine.repository.dto.miniApp.MiniAppModuleDTO;
import com.hishop.wine.repository.entity.MiniApp;
import com.hishop.wine.repository.dao.MiniAppMapper;
import com.hishop.wine.repository.service.MiniAppService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * 小程序表 服务实现类
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
@Service
public class MiniAppServiceImpl extends ServiceImpl<MiniAppMapper, MiniApp> implements MiniAppService {

    /**
     * 查询小程序列表
     *
     * @return 小程序列表
     */
    @Override
    public List<MiniAppDTO> listMiniApp() {
        return baseMapper.listMiniApp();
    }

    /**
     * 根据appId 获取小程序信息
     *
     * @param appId 小程序appId
     * @return 小程序信息
     */
    @Override
    public MiniApp getByAppId(String appId) {
        return getOne(lambdaQuery().eq(MiniApp::getAppId, appId));
    }

    /**
     * 查询绑定的模块名称
     *
     * @param appId 小程序appId
     * @return 绑定的模块名称
     */
    @Override
    public String getBindModuleNames(String appId) {
        return baseMapper.getBindModuleNames(appId);
    }

    /**
     * 查询小程序模块绑定关系
     *
     * @return 绑定关系
     */
    @Override
    public List<MiniAppModuleDTO> listMiniAppModule() {
        return baseMapper.listMiniAppModule();
    }
}