package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.wine.repository.dto.TagsCountDTO;
import com.hishop.wine.repository.entity.UserTags;
import org.apache.ibatis.annotations.Param;

import java.util.Set;
import java.util.List;

/**   
 * 用户标签表 Mapper 接口
 * @author: chenpeng
 * @date: 2023-07-17
 */

public interface UserTagsMapper extends BaseMapper<UserTags> {

    List<TagsCountDTO> userTagsCount(@Param("userSet") Set<Long> userSet);
	
}
