package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.wine.common.enums.MaterialType;
import com.hishop.wine.repository.entity.Identity;
import com.hishop.wine.repository.entity.Material;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 资源库表 Mapper 接口
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
public interface MaterialMapper extends BaseMapper<Material> {

    void updateMaterialCategoryIdByIds(@Param("newMaterialCategoryId") Long newMaterialCategoryId, @Param("ids") Collection<Long> ids);

    List<Integer> getMaterialTypesByIds(@Param("ids") Collection<Long> ids);

    Integer getCountByMaterialCategoryId(@Param("materialCategoryId") Long materialCategoryId);

}
