package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.wine.repository.entity.LoginError;
import com.hishop.wine.repository.entity.LogisticsCode;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;

/**
 * 登录异常表(LoginError)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-13 10:40:44
 */
public interface LoginErrorMapper extends BaseMapper<LoginError>  {

}

