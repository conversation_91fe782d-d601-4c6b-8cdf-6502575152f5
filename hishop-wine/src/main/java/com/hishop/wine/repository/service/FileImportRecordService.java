package com.hishop.wine.repository.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.wine.model.po.fileImport.FileImportRecordQueryPo;
import com.hishop.wine.model.vo.fileImport.FileImportRecordVo;
import com.hishop.wine.repository.entity.FileImportRecord;

/**
 * 导入记录表(ImportRecord)表服务接口
 *
 * <AUTHOR>
 * @since 2024-07-05 16:16:32
 */
public interface FileImportRecordService extends IService<FileImportRecord> {

    /**
     * 更新导入记录
     * @param record    导入记录
     * @param cost     耗时
     * @param status   状态
     * @param errMsg   错误信息
     * @param successCount 成功数量
     * @param failCount 失败数量
     * @param filePath 文件路径
     */
    void updateFileImportRecord(FileImportRecord record, long cost, Integer status, String errMsg, Integer successCount, Integer failCount, String filePath);

    /**
     * 分页查询导入记录
     * @param page 分页对象
     * @param fileImportRecordQueryPo 查询条件
     * @return Page<FileImportRecordVo>
     */
    Page<FileImportRecordVo> queryFileImportCodePageList(Page<FileImportRecordVo> page,  FileImportRecordQueryPo fileImportRecordQueryPo);
}

