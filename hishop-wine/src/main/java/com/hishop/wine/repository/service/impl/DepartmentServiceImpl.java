package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hishop.wine.repository.entity.Department;
import com.hishop.wine.repository.dao.DepartmentMapper;
import com.hishop.wine.repository.service.DepartmentService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**   
 * @Description: 部门表 服务实现类
 * @Author: chenpeng
 * @since: 2023-04-25 10:50:00
 */

@Service
public class DepartmentServiceImpl extends ServiceImpl<DepartmentMapper, Department> implements DepartmentService  {

    @Override
    public List<Department> listAll() {
        LambdaQueryWrapper<Department> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Department::getIzDelete, false);
        return this.list(queryWrapper);
    }

    @Override
    public Department getByNameAndParent(Long parentId, String departmentName) {
        LambdaQueryWrapper<Department> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Department::getParentId, parentId)
                .eq(Department::getDepartmentName, departmentName)
                .eq(Department::getIzDelete, false);
        return this.getOne(queryWrapper);
    }

    @Override
    public void logicalDeleteById(Long id) {
        Department department = new Department();
        department.setId(id);
        department.setIzDelete(true);
        this.updateById(department);
    }

    @Override
    public Integer getMaxFormIdByParentId(Long parentId) {
        Department department =  this.getMaxByParentId(parentId);
        return department != null ? department.getFormId() : null;
    }

    @Override
    public Department getMaxByParentId(Long parentId) {
        LambdaQueryWrapper<Department> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Department::getParentId, parentId)
                .eq(Department::getIzDelete, false)
                .orderByDesc(Department::getFormId)
                .last("limit 1");
        Department department = this.getOne(queryWrapper);
        return department;
    }

    @Override
    public List<String> getDepartmentNameByParentId(Long parentId) {
        LambdaQueryWrapper<Department> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Department::getParentId, parentId)
                .eq(Department::getIzDelete, false);
        List<Department> list=this.list(queryWrapper);
        return list.stream().map(Department::getDepartmentName).collect(Collectors.toList());
    }
}