package com.hishop.wine.repository.param.moderation;

import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.LinkedHashMap;

/**   
 * 内容审核明细表 数据库查询类
 * @author: HuBiao
 * @date: 2023-09-11
 */

@Data
@Builder
public class ContentModerationInfoParam {

	private Long moderationId;

	private String status;
    
	private Integer maxLimit;

	private LinkedHashMap<String, Boolean> sortField;

}
