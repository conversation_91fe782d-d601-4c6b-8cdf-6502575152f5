package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hishop.common.pojo.entity.BaseEntity;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户表 实体类
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Data
@NoArgsConstructor
@TableName("hishop_user")
public class User extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1686984251864L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 微信昵称
     */
    private String nickName;

    /**
     * 头像
     */
    private String icon;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 身份证
     */
    private String idCard;

    /**
     * 头衔id
     */
    private Long rankId;

    /**
     * 状态 0：禁用  1：正常
     */
    private Boolean status;

    /**
     * 当前查询的身份
     */
    @TableField(exist = false)
    private Identity identity;

    /**
     * 角色id
     */
    @TableField(exist = false)
    private Long roleId;

    /**
     * 部门id
     */
    @TableField(exist = false)
    private String departmentId;

    /**
     * 用户id
     */
    @TableField(exist = false)
    private Long userId;

    /**
     * 用户等级
     */
    private Long gradeId;

    /**
     * 会员等级容量
     */
    private BigDecimal gradeCapacity;

}
