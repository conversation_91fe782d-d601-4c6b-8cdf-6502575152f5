package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.wine.repository.entity.RoleResourceRelate;
import com.hishop.wine.repository.dao.RoleResourceRelateMapper;
import com.hishop.wine.repository.service.RoleResourceRelateService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 角色资源关联表 服务实现类
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Service
public class RoleResourceRelateServiceImpl extends ServiceImpl<RoleResourceRelateMapper, RoleResourceRelate> implements RoleResourceRelateService {

    /**
     * 通过角色id 删除关联关系
     *
     * @param roleId 角色id
     */
    @Override
    public void removeByRoleId(Long roleId) {
        remove(new LambdaQueryWrapper<RoleResourceRelate>().eq(RoleResourceRelate::getRoleId, roleId));
    }

    /**
     * 关联角色资源
     *
     * @param roleResourceRelateList 角色资源关系集合
     */
    @Override
    public void insertRoleResourceRelateBatch(List<RoleResourceRelate> roleResourceRelateList) {
        if (CollectionUtils.isEmpty(roleResourceRelateList)) {
            return;
        }

        baseMapper.insertBatchSomeColumn(roleResourceRelateList);
    }

    /**
     * 根据角色id 查询权限id
     *
     * @param roleId 角色id
     * @return 权限id集合
     */
    @Override
    public List<Long> listResourceIdsByRoleId(Long roleId) {
        return baseMapper.listResourceIdsByRoleId(roleId);
    }

    /**
     * 判断角色是否有权限
     *
     * @param roleId     角色id
     * @param resourceId 资源id
     * @return 是否有权限
     */
    @Override
    public Integer checkResourceAuth(Long roleId, Long resourceId) {
        return baseMapper.checkResourceAuth(roleId, resourceId);
    }
}