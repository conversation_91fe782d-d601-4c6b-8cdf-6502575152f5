package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.wine.model.po.terminate.TerminateQueryPo;
import com.hishop.wine.model.vo.terminate.TerminateVo;
import com.hishop.wine.repository.dao.TerminateMapper;
import com.hishop.wine.repository.entity.Terminate;
import com.hishop.wine.repository.service.TerminateService;
import org.springframework.stereotype.Service;

/**
 * 门店表(Terminate)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-06 14:47:43
 */
@Service
public class TerminateServiceImpl extends ServiceImpl<TerminateMapper, Terminate> implements TerminateService {

    @Override
    public Page<TerminateVo> queryTerminatePageList(Page<TerminateVo> page, TerminateQueryPo terminateQueryPo) {
        return baseMapper.queryTerminatePageList(page, terminateQueryPo);
    }
}

