package com.hishop.wine.repository.service.impl;

import com.hishop.wine.repository.entity.DeliveryAddress;
import com.hishop.wine.repository.dao.DeliveryAddressMapper;
import com.hishop.wine.repository.service.DeliveryAddressService;
import com.hishop.wine.repository.param.DeliveryAddressParam;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.List;

/**
 * 收货地址表 服务实现类
 *
 * @author: HuBiao
 * @date: 2023-06-29
 */
@Service
public class DeliveryAddressServiceImpl extends ServiceImpl<DeliveryAddressMapper, DeliveryAddress> implements DeliveryAddressService {

    @Override
    public DeliveryAddress getExist(DeliveryAddress deliveryAddress) {
        DeliveryAddress dbAddress = this.getOne(new LambdaQueryWrapper<DeliveryAddress>()
                .eq(DeliveryAddress::getProvinceId, deliveryAddress.getProvinceId())
                .eq(DeliveryAddress::getCityId, deliveryAddress.getCityId())
                .eq(DeliveryAddress::getAreaId, deliveryAddress.getAreaId())
                .eq(DeliveryAddress::getStreetId, deliveryAddress.getStreetId())
                .eq(DeliveryAddress::getUserId, deliveryAddress.getUserId())
                .eq(DeliveryAddress::getAddress, deliveryAddress.getAddress())
                .eq(DeliveryAddress::getConsignee, deliveryAddress.getConsignee())
                .eq(DeliveryAddress::getConsigneePhone, deliveryAddress.getConsigneePhone())
                .last("limit 1"));
        return dbAddress;
    }
}