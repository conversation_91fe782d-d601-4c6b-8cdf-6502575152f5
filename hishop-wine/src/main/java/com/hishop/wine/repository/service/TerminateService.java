package com.hishop.wine.repository.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.wine.model.po.terminate.TerminateQueryPo;
import com.hishop.wine.model.vo.terminate.TerminateVo;
import com.hishop.wine.repository.entity.Terminate;

/**
 * 门店表(Terminate)表服务接口
 *
 * <AUTHOR>
 * @since 2024-07-06 14:47:43
 */
public interface TerminateService extends IService<Terminate> {

    /**
     * 分页查询
     * @param page 分页对象
     * @param terminateQueryPo 查询实体
     * @return 分页对象
     */
    Page<TerminateVo> queryTerminatePageList(Page<TerminateVo> page, TerminateQueryPo terminateQueryPo);
}

