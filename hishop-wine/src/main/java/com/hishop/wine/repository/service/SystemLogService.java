package com.hishop.wine.repository.service;

import com.hishop.wine.repository.entity.SystemLog;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.repository.param.log.SystemLogParam;

import java.util.List;

/**
 * 操作日志表 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-08-01
 */

public interface SystemLogService extends IService<SystemLog> {

    /**
     * 分页查询日志记录
     *
     * @param pageInfo 分页参数
     * @param param    筛选参数
     * @return 日志记录
     */
    Page<SystemLog> qryPage(Page<SystemLog> pageInfo, SystemLogParam param);

    /**
     * 获取 操作日志表 列表
     *
     * @author: HuBiao
     * @date: 2023-08-01
     */
    List<SystemLog> qryList(SystemLogParam param);
}