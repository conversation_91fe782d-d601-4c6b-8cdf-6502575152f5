package com.hishop.wine.repository.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Builder;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 产品表 数据库查询类
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
@Data
@Builder
public class ProductParam {

    /**
     * 筛选值
     */
    private String searchValue;

    /**
     * 产品类型 1-商品 2-礼品
     */
    private Integer productType;

    /**
     * 产品分类id
     */
    private Long productCategoryId;
    private List<Long> idList;
    private List<String> productCodeList;

    /**
     * 是否需要被标记删除的数据
     */
    private Boolean filterDelete;

}
