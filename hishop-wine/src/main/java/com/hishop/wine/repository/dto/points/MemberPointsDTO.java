package com.hishop.wine.repository.dto.points;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/6/25
 */
@Data
public class MemberPointsDTO {

    /**
     *用户ID
     */
    private Long userId;
    /**
     * 身份类型 1-管理员 2-消费者 3-经销商 4-终端
     */
    private Integer identityType;
    /**
     *用户昵称
     */
    private String nickName;
    /**
     *用户手机号码
     */
    private String userPhone;
    /**
     *用户注册时间
     */
    private Date registerTime;
    /**
     *累计总积分
     */
    private Integer totalPoints;
    /**
     *可用积分
     */
    private Integer availablePoints;
    /**
     *已消耗积分
     */
    private Integer consumedPoints;
    /**
     *过期清零的积分
     */
    private Integer expiredPoints;
    /**
     * 已冻结的积分
     */
    private Integer frozenPoints;

    /**
     * 用户头衔
     */
    private String icon;

}
