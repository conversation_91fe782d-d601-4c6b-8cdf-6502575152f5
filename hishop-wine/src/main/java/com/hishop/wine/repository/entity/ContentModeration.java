package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**   
 * 内容审核表 实体类
 * @author: Hu<PERSON>iao
 * @date: 2023-09-11
 */

@Data
@NoArgsConstructor
@TableName("hishop_content_moderation")
public class ContentModeration implements Serializable {

	private static final long serialVersionUID = 1701124854668681216L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.INPUT)
	private Long id;
    
    /**
    * 模块编码
    */
	private String moduleCode;
    
    /**
    * 业务类型
    */
	private String bizType;
    
    /**
    * 业务编码
    */
	private String bizCode;
    
    /**
    * 业务描述
    */
	private String bizDesc;
    
    /**
    * 待审核-WAITING 审核中-AUDITING SUCCESS-审核通过 FAIL-审核失败
    */
	private String status;
    
    /**
    * 创建者ID
    */
    @TableField(fill = FieldFill.INSERT)
	private Long createBy;
    
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
	private Date createTime;
    
    /**
    * 更新者ID
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updateBy;
    
    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
    

}
