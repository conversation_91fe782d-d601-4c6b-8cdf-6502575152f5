package com.hishop.wine.repository.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 产品导入检测结果
 *
 * <AUTHOR>
 * @date : 2023/6/20
 */
@Data
public class ProductImportCheckDTO {

    /**
     * 成功列表
     */
    private List<ProductImportDTO> successList;

    /**
     * 失败列表
     */
    private List<ProductImportDTO> failList;

    public static ProductImportCheckDTO ofEmpty() {
        ProductImportCheckDTO result = new ProductImportCheckDTO();
        result.setSuccessList(new ArrayList<>());
        result.setFailList(new ArrayList<>());
        return result;
    }

}
