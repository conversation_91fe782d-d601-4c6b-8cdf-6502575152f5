package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**   
 * 会员积分明细表 实体类
 * @author: LiGuoQiang
 * @date: 2023-06-25
 */

@Data
@NoArgsConstructor
@TableName("hishop_member_points_details")
public class MemberPointsDetails implements Serializable {

	private static final long serialVersionUID = 1672869952815501312L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;
    
    /**
    * 用户ID
    */
	private Long userId;
    /**
     * 身份类型 1-管理员 2-消费者 3-经销商 4-终端
     */
    private Integer identityType;
    
    /**
    * 用户手机号码
    */
	private String userPhone;
    
    /**
    * 变更类型。-1：减积分；0：积分清零；1：加积分。方便区分
    */
	private Integer modifiedType;
    
    /**
    * 变更的积分。通过正负值表示增减，方便统计
    */
	private Integer modifiedPoints;
    
    /**
    * 模块编码。BASIC_SYSTEM：基础库；SCAN_MARKETING：扫码营销；FANS_CLUB：粉丝俱乐部
    */
	private String moduleCode;
    
    /**
    * 具体的业务类型，枚举与来源系统保持一致。积分清零固定为0
    */
	private Integer bizType;
    /**
     * 修改说明
     */
    private String modifiedRemark;
    
    /**
    * 关联的业务编码，方便回溯
    */
	private String bizCode;
    
    /**
    * 跟踪号，比如消费消息的唯一编码
    */
	private String traceNo;
    
    /**
    * 创建时间，即积分变更时间
    */
    @TableField(fill = FieldFill.INSERT)
	private Date createTime;
    
    /**
    * 积分过期时间
    */
	private Date expireTime;
    
    /**
    * 创建人。如果是手工变更的，记录变更人。0代表系统变更
    */
    @TableField(fill = FieldFill.INSERT)
	private Long createBy;
    

}
