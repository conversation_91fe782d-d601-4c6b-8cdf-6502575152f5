package com.hishop.wine.repository.dto.points;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/6/25
 */
@Data
public class MemberPointsDetailDTO {

    /**
     *用户ID
     */
    private Long userId;
    /**
     *用户昵称
     */
    private String nickName;
    /**
     *用户手机号码
     */
    private String userPhone;
    /**
     * 用户身份类型。积分根据身份类型隔离
     */
    private Integer identityType;
    /**
     *变更类型。-1：减积分/积分消耗；0：积分清零；1：加积分(积分发放)。方便区分
     */
    private Integer modifiedType;
    /**
     *变更的积分。通过正负值表示增减，方便统计
     */
    private Integer modifiedPoints;
    /**
     * 模块编码。BASIC_SYSTEM：基础库；SCAN_MARKETING：扫码营销；FANS_CLUB：粉丝俱乐部
     */
    private String moduleCode;
    /**
     *具体的业务类型，枚举与来源系统保持一致。积分清零固定为0
     */
    private Integer bizType;
    /**
     *修改说明
     */
    private String modifiedRemark;
    /**
     *创建时间，即积分变更时间
     */
    private Date createTime;

}
