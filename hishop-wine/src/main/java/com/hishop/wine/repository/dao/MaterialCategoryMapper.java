package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.wine.repository.entity.MaterialCategory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资源分组表 Mapper 接口
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
public interface MaterialCategoryMapper extends BaseMapper<MaterialCategory> {

    void batchMove(@Param("targetId") Long targetId, @Param("ids") List<Long> ids);

    List<MaterialCategory> listAllCategories(@Param("materialType") Integer materialType);

    List<Long> listChildCategoryIds(@Param("materialCategoryId") Long materialCategoryId);
}
