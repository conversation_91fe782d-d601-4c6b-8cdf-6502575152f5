package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.wine.repository.dto.payment.PaymentSettingDTO;
import com.hishop.wine.repository.dto.payment.PaymentSettingOfflineDTO;
import com.hishop.wine.repository.entity.PaymentSetting;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**   
 * 支付设置 Mapper 接口
 * @author: HuBia<PERSON>
 * @date: 2023-07-18
 */

public interface PaymentSettingMapper extends BaseMapper<PaymentSetting> {

    /**
     * 获取支付设置列表
     *
     * @return 获取支付设置列表
     */
    List<PaymentSettingDTO> listAll();


    /**
     * 获取线下支付设置列表
     * @return 获取线下支付设置列表
     */
    List<PaymentSettingOfflineDTO> listForOfflineSelect(@Param("moduleCode") String moduleCode);
}
