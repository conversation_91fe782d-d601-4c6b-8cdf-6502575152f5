package com.hishop.wine.repository.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.wine.repository.entity.Micropage;

import java.util.Map;


public interface MicropageService extends IService<Micropage> {

    /**
     * 更新访问次数
     *
     * @param visitMap 微页面id和访问次数
     */
    void updateVisitCount(Map<Integer, Long> visitMap);

    Long countFile(Integer status, Long categoryId);
}
