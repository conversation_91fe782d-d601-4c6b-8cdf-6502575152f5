package com.hishop.wine.repository.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.wine.model.po.logisticsCode.LogisticsCodeQueryPo;
import com.hishop.wine.model.vo.logisticsCode.LogisticsCodeVo;
import com.hishop.wine.repository.entity.LogisticsCode;

/**
 * 物流码管理表(LogisticsCode)表服务接口
 *
 * <AUTHOR>
 * @since 2024-07-08 11:51:56
 */
public interface LogisticsCodeService extends IService<LogisticsCode> {

    /**
     * 分页查询
     * @param pageInfo
     * @param param
     * @return
     */
    Page<LogisticsCodeVo> qryPage(Page<LogisticsCode> pageInfo, LogisticsCodeQueryPo param);

}

