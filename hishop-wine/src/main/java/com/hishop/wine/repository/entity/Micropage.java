package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**   
 * 微页面表 实体类
 * @author: HuBiao
 * @date: 2023-08-07
 */

@Data
@NoArgsConstructor
@TableName("hishop_micropage")
public class Micropage implements Serializable {

	private static final long serialVersionUID = 1688452988330090496L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Integer id;
    
    /**
    * 分组id, 如果为0表示未分组
    */
	private Integer categoryId;
    
    /**
    * 微页面名称
    */
	private String name;
    
    /**
    * 配置json
    */
	private String settingJson;
    
    /**
    * 访问量
    */
	private Integer pv;
    
    /**
    * 访问人数
    */
	private Integer uv;
    
    /**
    * 1-草稿 2-上架
    */
	private Integer status;

    /**
     * 是否删除 true-删除 false-未删除
     */
	private Boolean izDelete;
    
    /**
    * 创建者ID
    */
    @TableField(fill = FieldFill.INSERT)
	private Long createBy;
    
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
	private Date createTime;
    
    /**
    * 更新者ID
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updateBy;
    
    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
    

}
