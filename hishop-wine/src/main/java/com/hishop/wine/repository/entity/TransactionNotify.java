package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 交易回调消息 实体类
 *
 * @author: HuBiao
 * @date: 2023-06-28
 */
@Data
@NoArgsConstructor
@TableName("hishop_transaction_notify")
public class TransactionNotify implements Serializable {

    private static final long serialVersionUID = 1673964660515930112L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 交易id
     */
    private Long transactionId;

    /**
     * 回调信息
     */
    private String notifyJson;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;


}
