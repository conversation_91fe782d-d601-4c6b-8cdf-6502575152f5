package com.hishop.wine.repository.dto;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hishop.common.constants.RegexConstants;
import com.hishop.wine.enums.ProductTypeEnums;
import lombok.Data;

import java.io.File;

/**
 * 产品导入模板
 *
 * <AUTHOR>
 * @date : 2023/6/19
 */
@Data
public class ProductImportDTO {

    @ExcelProperty(value = "*产品名称")
    private String productName;

    @ExcelProperty(value = "*产品编码")
    private String productCode;

    @ExcelProperty(value = "*产品类型")
    private String productTypeName;

    @ExcelProperty(value = "*产品分类")
    private String productCategoryName;

    @ExcelProperty(value = "*产品价格")
    private String marketPrice;

    @ExcelProperty(value = "*产品主图")
    private String productImg;

    @ExcelProperty(value = "失败原因")
    private String result;

    /**
     * 产品id
     */
    private transient Long id;

    /**
     * 产品类型
     */
    private transient Integer productType;

    /**
     * 产品分类id
     */
    private transient Long productCategoryId;

    /**
     * 图片文件
     */
    private transient File productImgFile;

    /**
     * 校验基本信息
     *
     * @return 基本信息校验结果
     */
    public StringBuilder checkBaseParam() {
        StringBuilder errorResult = new StringBuilder();
        if (StrUtil.isEmpty(productName)) {
            errorResult.append("产品名称不能为空;");
        }
        if (StrUtil.isEmpty(productCode)) {
            errorResult.append("产品编码不能为空;");
        }
        if (StrUtil.isEmpty(productTypeName)) {
            errorResult.append("产品类型不能为空;");
        }
        Integer productType = ProductTypeEnums.getTypeByName(productTypeName);
        if (ObjectUtil.isNull(productType)) {
            errorResult.append("产品类型错误;");
        } else {
            this.productType = productType;
        }
        if (StrUtil.isEmpty(productCategoryName)) {
            errorResult.append("产品分类不能为空;");
        }
        // 使用正则校验金额, 必须大于0 并且最多保留两位小数
        if (!StrUtil.isEmpty(marketPrice) && !marketPrice.matches(RegexConstants.PRICE_REGEX)) {
            errorResult.append("价格格式不对;");
        }
        if (StrUtil.isEmpty(productImg)) {
            errorResult.append("产品主图不能为空;");
        }
        return errorResult;
    }
}
