package com.hishop.wine.repository.param.member;

import lombok.Data;

import java.util.Date;
import java.util.LinkedHashMap;

/**
 * 会员查询参数
 *
 * <AUTHOR>
 * @date : 2023/7/25
 */
@Data
public class MemberParam {

    /**
     * 注册开始时间
     */
    private Date startTime;

    /**
     * 注册结束时间
     */
    private Date endTime;

    /**
     * 会员筛选
     */
    private String memberSearchKey;

    /**
     * 头衔id
     */
    private Long rankId;

    /**
     * 排序字段
     */
    private LinkedHashMap<String, Boolean> sortField;

    /**
     * 用户id
     */
    private Long userId;

}
