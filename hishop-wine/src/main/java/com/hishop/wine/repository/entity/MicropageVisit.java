package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**   
 * 微页面访客记录表 实体类
 * @author: HuBiao
 * @date: 2023-08-28
 */

@Data
@NoArgsConstructor
@TableName("hishop_micropage_visit")
public class MicropageVisit implements Serializable {

	private static final long serialVersionUID = 1695988546770083840L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Long id;
    
    /**
    * 微页面id
    */
	private Integer micropageId;

    /**
     * 访问量
     */
	private Integer visitCount;
    
    /**
    * 访问用户id
    */
	private Long userId;
    
    /**
    * 首次访问时间
    */
    @TableField(fill = FieldFill.INSERT)
	private Date createTime;
    
    /**
    * 最后访问时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
    

}
