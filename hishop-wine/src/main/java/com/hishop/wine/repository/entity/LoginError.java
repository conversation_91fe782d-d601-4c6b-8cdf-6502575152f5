package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.io.Serializable;

/**
 * 登录异常表(LoginError)实体类
 *
 * <AUTHOR>
 * @since 2025-06-13 10:40:52
 */
@Data
@NoArgsConstructor
@TableName("hishop_login_error")
public class LoginError implements Serializable {

    private static final long serialVersionUID = 211686348586620995L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 小程序app_id
     */
    private String appId;
    /**
     * openid
     */
    private String openId;
    /**
     * 异常类型 0微信号绑定其他手机 1手机号绑定其他微信
     */
    private Integer type;
    /**
     * 重置状态 0未重置 1已重置
     */
    private Boolean status;
    /**
     * 创建者ID
     */
    private Long createBy;
    /**
     * 更新者ID
     */
    private Long updateBy;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否删除 0否 1是
     */
    private Boolean izDelete;

}

