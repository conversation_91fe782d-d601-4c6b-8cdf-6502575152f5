package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**   
 * 地址库表 实体类
 * @author: HuBiao
 * @date: 2023-07-17
 */

@Data
@NoArgsConstructor
@TableName("hishop_base_address")
public class BaseAddress implements Serializable {

	private static final long serialVersionUID = 1680758105398132736L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Long id;
    
    /**
    * 联系人
    */
	private String contacts;
    
    /**
    * 联系人手机号
    */
	private String contactsPhone;
    
    /**
    * 省ID
    */
	private Integer provinceId;
    
    /**
    * 省
    */
	private String province;
    
    /**
    * 城市ID
    */
	private Integer cityId;
    
    /**
    * 城市
    */
	private String city;
    
    /**
    * 区ID
    */
	private Integer areaId;
    
    /**
    * 区
    */
	private String area;
    
    /**
    * 街道id
    */
	private Integer streetId;
    
    /**
    * 街道名称
    */
	private String street;
    
    /**
    * 详细地址
    */
	private String address;
    
    /**
    * 邮政编码
    */
	private String postalCode;

    /**
     * 是否逻辑删除
     */
	private Boolean izDelete;
    
    /**
    * 是否默认发货地址
    */
	private Boolean izDefaultSendAddress;
    
    /**
    * 是否默认收货地址
    */
	private Boolean izDefaultReceiveAddress;
    
    /**
    * 创建者ID
    */
    @TableField(fill = FieldFill.INSERT)
	private Long createBy;
    
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
	private Date createTime;
    
    /**
    * 更新者ID
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updateBy;
    
    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
    

}
