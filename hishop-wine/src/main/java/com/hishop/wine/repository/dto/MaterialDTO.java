package com.hishop.wine.repository.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hishop.wine.common.enums.MaterialStatus;
import com.hishop.wine.common.enums.MaterialType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description: 资源DTO
 * @author: tomliu
 * @create: 2022/06/24 16:45
 **/
@Data
@ApiModel("资源")
public class MaterialDTO  extends BaseDTO<Integer> {

    /**
     * 所属资源分组
     */
    @ApiModelProperty("所属资源分组")
    private Long materialCategoryId;

    /**
     * 资源标题
     */
    @ApiModelProperty("标题")
    private String title;

    /**
     * 资源路径
     */
    @ApiModelProperty("路径")
    private String path;

    /**
     * 封面路径
     */
    @ApiModelProperty("封面路径")
    private String bannerPath;

    /**
     * 资源类型
     */
    @ApiModelProperty("类型")
    private MaterialType type;

    /**
     * 资源大小(Byte)
     */
    @ApiModelProperty("资源大小(Byte)")
    private Long size;

    /**
     * 资源状态
     */
    @ApiModelProperty("状态")
    private MaterialStatus status;

    /**
     * 使用次数
     */
    @ApiModelProperty("使用次数")
    private Integer usedTimes = 0;


    /**
     * 视频长度(秒)
     */
    @ApiModelProperty("视频长度(秒)")
    private Integer duration;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
