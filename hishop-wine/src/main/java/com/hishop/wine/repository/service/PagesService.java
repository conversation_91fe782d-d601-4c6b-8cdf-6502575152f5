package com.hishop.wine.repository.service;

import com.hishop.wine.repository.entity.Pages;
import com.hishop.wine.repository.param.PagesParam;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

/**   
 * 页面配置表 数据层服务类
 * @author: HuBiao
 * @date: 2023-07-07
 */

public interface PagesService extends IService<Pages> {

    /**
     * 分页获取 页面配置表
     * @author: HuBiao
     * @date: 2023-07-07
    */
    Page<Pages> qryPage(Page<Pages> pageInfo, PagesParam param);

    /**
     * 获取 页面配置表 列表
     * @author: HuBiao
     * @date: 2023-07-07
    */
    List<Pages> qryList(PagesParam param);
}