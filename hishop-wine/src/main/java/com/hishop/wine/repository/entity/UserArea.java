package com.hishop.wine.repository.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;
import com.hishop.common.pojo.entity.BaseEntity;

/**
 * 业务员销售区域关联表(HishopUserArea)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-06 11:23:54
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("hishop_user_area")
public class UserArea extends BaseEntity {

    /**
     * 主键
     **/
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 经销商id
     **/
    private Long userId;
    /**
     * 销售区域id
     **/
    private Long saleAreaId;
}

