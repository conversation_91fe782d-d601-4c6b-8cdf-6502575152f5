package com.hishop.wine.repository.service.impl;

import com.hishop.wine.repository.entity.MicropageVisit;
import com.hishop.wine.repository.dao.MicropageVisitMapper;
import com.hishop.wine.repository.service.MicropageVisitService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.Date;
import java.util.List;

/**   
 * 微页面访客记录表 服务实现类
 * @author: HuBiao
 * @date: 2023-08-28
 */

@Service
public class MicropageVisitServiceImpl extends ServiceImpl<MicropageVisitMapper, MicropageVisit> implements MicropageVisitService  {

    /**
     * 添加访问明细
     *
     * @param visitList 访问记录
     */
    @Override
    public void saveVisitLog(List<MicropageVisit> visitList) {
        baseMapper.saveVisitLog(visitList);
    }
}