package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**   
 * 内容审核明细表 实体类
 * @author: HuBiao
 * @date: 2023-09-11
 */

@Data
@NoArgsConstructor
@TableName("hishop_content_moderation_info")
public class ContentModerationInfo implements Serializable {

	private static final long serialVersionUID = 1701124857231400960L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Long id;
    
    /**
    * 审核id
    */
	private Long moderationId;

    /**
     * 审核内容 如果审核类型为TEXT, 则是文本信息, 其余为远程地址
     */
	private String moderationData;
    
    /**
    * 审核类型 TEXT-文本 IMAGE-图片 VIDEO-视频 AUDIO-音频
    */
	private String moderationType;
    
    /**
    * 审核建议 block：包含敏感信息，不通过。pass：不包含敏感信息，通过。review：需要人工复查。（华为云返回）
    */
	private String suggestion;
    
    /**
    * 提示信息，失败和不通过时会有（华为云返回）
    */
	private String message;
    
    /**
    * 如果审核不通过，则会有具体的提示，每种类型的都不一样(华为云返回)
    */
	private String moderationResult;
    
    /**
    * 异步审核时，与审核服务的跟踪号，用于后续查询异步结果(华为云返回)
    */
	private String traceNo;
    
    /**
    * 待审核-WAITING 审核中-AUDITING SUCCESS-审核通过 FAIL-审核失败
    */
	private String status;
    
    /**
    * 备注信息
    */
	private String remark;
    
    /**
    * 创建者ID
    */
    @TableField(fill = FieldFill.INSERT)
	private Long createBy;
    
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
	private Date createTime;
    
    /**
    * 更新者ID
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updateBy;
    
    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
    

}
