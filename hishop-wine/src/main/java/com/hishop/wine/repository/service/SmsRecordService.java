package com.hishop.wine.repository.service;

import com.hishop.wine.repository.entity.SmsRecord;
import com.hishop.wine.repository.param.SmsRecordParam;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 短信发送记录表 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-07-12
 */

public interface SmsRecordService extends IService<SmsRecord> {

    /**
     * 分页获取短信发送记录
     *
     * @param pageInfo 分页参数
     * @param param 筛选参数
     * @return 短信发送记录列表
     */
    Page<SmsRecord> qryPage(Page<SmsRecord> pageInfo, SmsRecordParam param);

}