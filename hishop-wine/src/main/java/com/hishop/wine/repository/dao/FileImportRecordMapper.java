package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.model.po.fileImport.FileImportRecordQueryPo;
import com.hishop.wine.model.vo.fileImport.FileImportRecordVo;
import com.hishop.wine.repository.entity.FileImportRecord;
import org.apache.ibatis.annotations.Param;

/**
 * 导入记录表(ImportRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-05 16:16:32
 */
public interface FileImportRecordMapper extends BaseMapper<FileImportRecord> {

    /**
     * 分页查询导入记录
     * @param page 分页对象
     * @param fileImportRecordQueryPo 查询条件
     * @return Page<FileImportRecordVo>
     */
    Page<FileImportRecordVo> queryFileImportCodePageList(Page<FileImportRecordVo> page, @Param("param") FileImportRecordQueryPo fileImportRecordQueryPo);
}

