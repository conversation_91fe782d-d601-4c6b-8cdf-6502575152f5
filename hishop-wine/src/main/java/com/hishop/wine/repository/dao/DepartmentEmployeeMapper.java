package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.wine.model.po.DepartmentUpdateIdPO;
import com.hishop.wine.repository.entity.DepartmentEmployee;

/**   
 * 部门员工表 Mapper 接口
 * @Author: chenpeng
 * @since: 2023-04-25 10:50:00
 */

public interface DepartmentEmployeeMapper extends BaseMapper<DepartmentEmployee> {


    /**
     * 添加部门用户
     * @param entity
     * @return
     */
    int add(DepartmentEmployee entity);


    /**
     * 修改部门用户
     * @param entity
     * @return
     */
    int updateDepByUserId(DepartmentUpdateIdPO entity);
}
