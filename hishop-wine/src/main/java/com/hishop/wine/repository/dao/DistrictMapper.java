package com.hishop.wine.repository.dao;

import com.hishop.common.handler.EasyBaseMapper;
import com.hishop.wine.repository.entity.District;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 地区表 Mapper 接口
 *
 * @author: HuBiao
 * @date: 2023-06-26
 */
public interface DistrictMapper extends EasyBaseMapper<District> {

    /**
     * 批量更新(拼接sql)
     *
     * @param entityList 更新集合
     * @return 影响行数
     */
    Integer updateBatchSomeColumn(@Param("districtList") List<District> entityList);

    /**
     * 更新下级名称
     *
     * @param id          组级id
     * @param oldFullName 旧的下级名称
     * @param newFullName 新的下级名称
     */
    void updateChildName(@Param("id") Integer id, @Param("oldFullName") String oldFullName, @Param("newFullName") String newFullName);
}
