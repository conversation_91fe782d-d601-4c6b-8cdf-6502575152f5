package com.hishop.wine.repository.service;

import com.hishop.wine.repository.entity.RoleResourceRelate;
import com.hishop.wine.repository.entity.SmsRecordMobile;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 短信发送手机号表 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-07-12
 */

public interface SmsRecordMobileService extends IService<SmsRecordMobile> {

    /**
     * 关联短信手机号
     *
     * @param smsRecordMobileList 短信关联手机号集合
     */
    void insertSmsRecordMobileBatch(List<SmsRecordMobile> smsRecordMobileList);

}