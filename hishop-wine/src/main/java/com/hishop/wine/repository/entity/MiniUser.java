package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hishop.common.pojo.entity.BaseEntity;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 小程序用户表 实体类
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
@Data
@NoArgsConstructor
@TableName("hishop_mini_user")
public class MiniUser extends BaseEntity {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户头像
     */
    private String avatarUrl;

    /**
     * 小程序app_id
     */
    private String appId;

    /**
     * 注册模块编码
     */
    private String registerModuleCode;

    /**
     * unionid
     */
    private String unionId;

    /**
     * openid
     */
    private String openId;

    /**
     * 会话密钥
     */
    private String sessionKey;

    /**
     * 是否黑名单 0：否 1：是
     */
    private Boolean izBlacklist;

    /**
     * 拉黑时间
     */
    private Date blacklistDate;

    /**
     * 拉黑操作人
     */
    private Long blacklistOperateId;

    /**
     * 拉黑操作人姓名
     */
    private String blacklistOperateName;

    /**
     * 拉黑操作人电话
     */
    private String blacklistOperatePhone;

    /**
     * 客户编号
     */
    private String userNo;

    /**
     * 备注名
     */
    private String remarkName;

}
