package com.hishop.wine.repository.dto.payment;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 微信支付配置参数
 *
 * <AUTHOR>
 * @date : 2023/7/21
 */
@Data
public class PaymentSettingWxDTO {

    /**
     * 商户号
     */
    private String mchId;

    /**
     * 支付密钥
     */
    private String apiV3Key;

    /**
     * apiclient_cert.p12 证书(apiV3 key 不能使用该证书)
     */
    private Long certificateId;

    /**
     * apiclient_key.pem 证书
     */
    private Long privateKeyId;

    /**
     * apiclient_cert.pem 证书
     */
    private Long privateCertId;


}
