package com.hishop.wine.repository.dto.points;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/25
 */
@Data
public class MemberPointsSummaryDTO {

    private Long userId;
    private Integer identityType;
    /**
     * 所有用户总的可用积分
     */
    private Integer availablePoints;
    /**
     * 所有用户总的累计发放积分
     */
    private Integer totalPoints;
    /**
     * 所有用户总的已消耗积分
     */
    private Integer consumedPoints;
    /**
     * 所有用户总的过期清零的积分
     */
    private Integer expiredPoints;

}
