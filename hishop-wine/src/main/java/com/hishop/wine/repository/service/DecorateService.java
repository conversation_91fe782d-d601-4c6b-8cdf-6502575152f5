package com.hishop.wine.repository.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.wine.repository.entity.Decorate;

/**   
 * 装修表 数据层服务类
 * @author: chenpeng
 * @date: 2023-06-26
 */

public interface DecorateService extends IService<Decorate> {

    /**
     * 根据类型获取商城装修，类型是前端自定义的关键字
     * <AUTHOR>
     * @date 2023/6/29
     */
    Decorate getByType(String appId, String modelCode, String decorateType);

    /**
     * 修改其他首页装修为非默认状态
     * @param appId
     * @param modelCode
     * @param decorateType
     */
    void updateOtherDefault(String appId, String modelCode, String decorateType);

}