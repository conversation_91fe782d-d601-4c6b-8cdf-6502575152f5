package com.hishop.wine.repository.service.impl;

import com.hishop.wine.repository.entity.MaterialCategory;
import com.hishop.wine.repository.dao.MaterialCategoryMapper;
import com.hishop.wine.repository.service.MaterialCategoryService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * 资源分组表 服务实现类
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Service
public class MaterialCategoryServiceImpl extends ServiceImpl<MaterialCategoryMapper, MaterialCategory> implements MaterialCategoryService {

    @Override
    public void batchMove(Long targetId, List<Long> ids) {
        baseMapper.batchMove(targetId, ids);
    }

    @Override
    public List<MaterialCategory> listAllCategories(Integer materialType) {
        return baseMapper.listAllCategories(materialType);
    }

    @Override
    public List<Long> listChildCategoryIds(Long materialCategoryId) {
        return baseMapper.listChildCategoryIds(materialCategoryId);
    }
}