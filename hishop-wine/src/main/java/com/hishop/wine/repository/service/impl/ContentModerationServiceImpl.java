package com.hishop.wine.repository.service.impl;

import cn.hutool.core.util.StrUtil;
import com.hishop.wine.repository.entity.ContentModeration;
import com.hishop.wine.repository.dao.ContentModerationMapper;
import com.hishop.wine.repository.param.moderation.ContentModerationParam;
import com.hishop.wine.repository.service.ContentModerationService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 内容审核表 服务实现类
 *
 * @author: HuBiao
 * @date: 2023-09-11
 */

@Service
public class ContentModerationServiceImpl extends ServiceImpl<ContentModerationMapper, ContentModeration> implements ContentModerationService {

    public Page<ContentModeration> qryPage(Page<ContentModeration> pageInfo, ContentModerationParam param) {
        LambdaQueryWrapper<ContentModeration> wrapper = new LambdaQueryWrapper<>();
        return this.page(pageInfo, wrapper);
    }

    public List<ContentModeration> qryList(ContentModerationParam param) {
        LambdaQueryWrapper<ContentModeration> wrapper = new LambdaQueryWrapper<>();
        return super.list(wrapper);
    }

    /**
     * 更新审核状态
     *
     * @param id     内容审核id
     * @param status 审核状态
     */
    @Override
    public void updateStatus(Long id, String status) {
        ContentModeration entity = new ContentModeration();
        entity.setId(id);
        entity.setStatus(status);
        super.updateById(entity);
    }

    /**
     * 统计数量
     *
     * @param param 筛选参数
     * @return 数量
     */
    @Override
    public Long count(ContentModerationParam param) {
        return count(buildCommonWrapper(param));
    }

    /**
     * 刷新审核状态
     *
     * @param id 内容审核id
     */
    @Override
    public void refreshStatus(Long id) {
        baseMapper.refreshStatus(id);
    }

    /**
     * 构建通用查询条件
     *
     * @param param
     * @return
     */
    private LambdaQueryWrapper buildCommonWrapper(ContentModerationParam param) {
        LambdaQueryWrapper<ContentModeration> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotEmpty(param.getBizType()), ContentModeration::getBizType, param.getBizType())
                .eq(StrUtil.isNotEmpty(param.getBizCode()), ContentModeration::getBizCode, param.getBizCode())
                .in(!CollectionUtils.isEmpty(param.getStatusList()), ContentModeration::getStatus, param.getStatusList());
        return wrapper;
    }
}