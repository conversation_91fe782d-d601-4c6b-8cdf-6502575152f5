package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.wine.repository.dto.module.ModuleWxParamDTO;
import com.hishop.wine.repository.entity.ModuleInfo;

import java.util.List;

/**
 * 模块表 Mapper 接口
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
public interface ModuleMapper extends BaseMapper<ModuleInfo> {

    /**
     * 查询模块关联微信参数
     *
     * @return 模块关联微信参数
     */
    List<ModuleWxParamDTO> listModuleWxParam();
}
