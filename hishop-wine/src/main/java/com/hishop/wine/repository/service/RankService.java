package com.hishop.wine.repository.service;

import com.hishop.wine.repository.entity.Rank;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.repository.param.rank.RankParam;

import java.util.List;

/**
 * 头衔表 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-07-25
 */

public interface RankService extends IService<Rank> {

    /**
     * 分页获取 头衔表
     *
     * @param pageInfo 分页参数
     * @param param    筛选参数
     * @return 头衔表列表
     */
    Page<Rank> qryPage(Page<Rank> pageInfo, RankParam param);

    /**
     * 获取 头衔表
     *
     * @param param    筛选参数
     * @return 头衔表列表
     */
    List<Rank> qryList(RankParam param);
}