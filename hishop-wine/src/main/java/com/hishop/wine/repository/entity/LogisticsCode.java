package com.hishop.wine.repository.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;
import com.hishop.common.pojo.entity.BaseEntity;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 物流码管理表(LogisticsCode)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-08 11:51:55
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("hishop_logistics_code")
public class LogisticsCode extends BaseEntity {

    /**
     * 主键
     **/
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 一级物流编码
     **/
    private String codeFirst;
    /**
     * 二级物流编码
     **/
    private String codeSecondary;
    /**
     * 瓶内码
     **/
    private String codeBottle;
    /**
     * 产品编码
     **/
    private String productCode;
    /**
     * 物流码类型 0盒码 1箱码
     **/
    private Integer codeType;
    /**
     * 状态 0未使用 1已使用
     **/
    private Integer status;
    /**
     * 物流码类别 0普通物流码 1扫码营销物流码
     **/
    private Integer codeCategory;
    /**
     *  批次id
     */
    private Long fileImportId;
    /**
     * 盒码数量
     */
    private Integer num;
    /**
     * 商品id
     */
    private Long productId;
}

