package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.wine.model.po.TagsQueryPO;
import com.hishop.wine.model.vo.tags.TagsVO;
import com.hishop.wine.repository.dao.TagsMapper;
import com.hishop.wine.repository.entity.Tags;
import com.hishop.wine.repository.service.TagsService;
import org.springframework.stereotype.Service;

import java.util.List;

/**   
 * 标签表 服务实现类
 * @author: chenpeng
 * @date: 2023-07-17
 */

@Service
public class TagsServiceImpl extends ServiceImpl<TagsMapper, Tags> implements TagsService  {

    @Override
    public Page<Tags> pageList(Page<Tags> page, TagsQueryPO pagePO) {
        return baseMapper.pageList(page, pagePO);
    }

    @Override
    public List<TagsVO> getMyList(Long userId) {
        return baseMapper.getMyList(userId);
    }

    @Override
    public List<TagsVO> getTopList(Integer num, List<Long> tagIds, String tagName) {
        return baseMapper.getTopList(num, tagIds, tagName);
    }
}