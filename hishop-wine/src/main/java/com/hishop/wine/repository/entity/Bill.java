package com.hishop.wine.repository.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hishop.common.pojo.entity.BaseEntity;
import com.hishop.wine.enums.bill.TransactionType;
import com.hishop.wine.enums.order.PayMethod;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 收入账单表(Bill)表实体类
 *
 * <AUTHOR>
 * @since 2024-01-29 15:17:19
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("hishop_bill")
public class Bill extends BaseEntity {

    /**
     * 主键
     **/
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 模块编码
     */
    private String moduleCode;
    /**
     * 交易类型
     **/
    private String transactionType;
    /**
     * 业务单号
     **/
    private String businessNo;
    /**
     * 交易流水号,对应交易表id
     **/
    private Long transactionId;
    /**
     * 第三方交易流水号
     **/
    private String thirdTransactionNo;
    /**
     * 支付方式 ONLINE_PAY:在线支付 OFFLINE_PAY:线下支付
     **/
    private String payMethod;
    /**
     * 金额
     **/
    private BigDecimal amount;
    /**
     * 用户id
     **/
    private Long userId;
    /**
     * 用户名称
     **/
    private String userName;
    /**
     * 年 yyyy
     */
    private String createYear;
    /**
     * 月 yyyy-MM
     */
    private String createMonth;
    /**
     * 日 yyyy-MM-dd
     */
    private String createDay;
}

