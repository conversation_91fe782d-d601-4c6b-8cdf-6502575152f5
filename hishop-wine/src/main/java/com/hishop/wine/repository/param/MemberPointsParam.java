package com.hishop.wine.repository.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.LinkedHashMap;

/**   
 * 会员积分表 数据库查询类
 * @author: LiGuoQiang
 * @date: 2023-06-25
 */

@Data
@Builder
public class MemberPointsParam {

	/**
	 * 搜索关键字。目前支持用户昵称和手机号码
	 */
	private String searchKey;
	/**
	 * 用户ID
	 */
	private Long userId;
	/**
	 * 身份类型 1-管理员 2-消费者 3-经销商 4-终端
	 */
	private Integer identityType;
	/**
	 * 模块编码。BASIC_SYSTEM：基础库；SCAN_MARKETING：扫码营销；FANS_CLUB：粉丝俱乐部
	 */
	private String moduleCode;
	/**
	 * 用户昵称
	 */
	private String nickName;
	/**
	 * 用户手机号码
	 */
	private String userPhone;
	/**
	 * 变更类型。-1：减积分；0：积分清零；1：加积分。方便区分
	 */
	private Integer modifiedType;
	/**
	 * 具体的业务类型，枚举与来源系统保持一致。积分清零固定为0
	 */
	private Integer bizType;
	/**
	 * 开始时间
	 */
	private Date startTime;
	/**
	 * 结束时间
	 */
	private Date endTime;

	/**
	 * 排序sql
	 */
	private String sortSql;
}
