package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.wine.repository.dao.WechatCodeMapper;
import com.hishop.wine.repository.entity.WechatCode;
import com.hishop.wine.repository.service.WechatCodeService;
import org.springframework.stereotype.Service;

/**   
 * 微信二维码表 服务实现类
 * @author: LiGuoQiang
 * @date: 2023-06-20
 */

@Service
public class WechatCodeServiceImpl extends ServiceImpl<WechatCodeMapper, WechatCode> implements WechatCodeService {

    @Override
    public WechatCode qryByKey(String codeKey) {
        LambdaQueryWrapper<WechatCode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WechatCode::getCodeKey, codeKey)
                .eq(WechatCode::getIzDelete, false);
        return super.getOne(wrapper);
    }
}