package com.hishop.wine.repository.service;

import com.hishop.wine.repository.entity.DeliveryAddress;
import com.hishop.wine.repository.param.DeliveryAddressParam;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 收货地址表 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-06-29
 */
public interface DeliveryAddressService extends IService<DeliveryAddress> {

    /**
     * 根据省市区街道id以及userId查询是否存在地址
     */
    DeliveryAddress getExist(DeliveryAddress deliveryAddress);
}