package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.model.po.dealer.DealerQueryPo;
import com.hishop.wine.model.vo.dealer.DealerVo;
import com.hishop.wine.repository.entity.Dealer;
import org.apache.ibatis.annotations.Param;

/**
 * 经销商表(Dealer)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-04 15:53:16
 */
public interface DealerMapper extends BaseMapper<Dealer> {

    /**
     * 经销商分页列表
     * @param pageInfo 查询条件
     * @param dealerQueryPo 查询条件
     * @return PageResult<DealerVo>
     */
    Page<DealerVo> queryDealerPageList(Page<DealerVo> pageInfo, @Param("param") DealerQueryPo dealerQueryPo);
}

