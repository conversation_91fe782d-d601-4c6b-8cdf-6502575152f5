package com.hishop.wine.repository.service.impl;

import com.hishop.wine.repository.entity.BaseAddress;
import com.hishop.wine.repository.dao.BaseAddressMapper;
import com.hishop.wine.repository.service.BaseAddressService;
import com.hishop.wine.repository.param.BaseAddressParam;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.List;

/**
 * 地址库表 服务实现类
 *
 * @author: HuBiao
 * @date: 2023-07-17
 */
@Service
public class BaseAddressServiceImpl extends ServiceImpl<BaseAddressMapper, BaseAddress> implements BaseAddressService {

    /**
     * 分页获取 地址库表
     *
     * @param pageInfo 分页信息
     * @param param    查询参数
     * @return 地址库表分页数据
     */
    @Override
    public Page<BaseAddress> qryPage(Page<BaseAddress> pageInfo, BaseAddressParam param) {
        LambdaQueryWrapper<BaseAddress> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseAddress::getIzDelete, Boolean.FALSE);
        return this.page(pageInfo, wrapper);
    }

    /**
     * 获取 地址库表 列表
     *
     * @param param 查询参数
     * @return 地址库表列表
     */
    @Override
    public List<BaseAddress> qryList(BaseAddressParam param) {
        LambdaQueryWrapper<BaseAddress> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseAddress::getIzDelete, Boolean.FALSE);
        return super.list(wrapper);
    }

    /**
     * 逻辑删除地址
     *
     * @param id 地址id
     */
    @Override
    public void logicRemoveById(Long id) {
        BaseAddress updAddress = new BaseAddress();
        updAddress.setId(id);
        updAddress.setIzDelete(Boolean.TRUE);
        updateById(updAddress);
    }
}