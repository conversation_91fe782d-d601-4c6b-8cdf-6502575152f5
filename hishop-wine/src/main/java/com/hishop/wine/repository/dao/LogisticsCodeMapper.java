package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.model.po.logisticsCode.LogisticsCodeQueryPo;
import com.hishop.wine.model.vo.logisticsCode.LogisticsCodeVo;
import org.apache.ibatis.annotations.Param;
import com.hishop.wine.repository.entity.LogisticsCode;

/**
 * 物流码管理表(LogisticsCode)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-08 11:51:55
 */
public interface LogisticsCodeMapper extends BaseMapper<LogisticsCode> {

    /**
     * 分页查询
     * @param pageInfo
     * @param param
     * @return
     */
    Page<LogisticsCodeVo> queryPage(Page<LogisticsCode> pageInfo, @Param("param") LogisticsCodeQueryPo param);

}

