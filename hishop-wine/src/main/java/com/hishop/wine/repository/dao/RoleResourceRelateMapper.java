package com.hishop.wine.repository.dao;

import com.hishop.common.handler.EasyBaseMapper;
import com.hishop.wine.repository.entity.RoleResourceRelate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色资源关联表 Mapper 接口
 *
 * @author: Hu<PERSON>ia<PERSON>
 * @date: 2023-06-17
 */
public interface RoleResourceRelateMapper extends EasyBaseMapper<RoleResourceRelate> {

    /**
     * 根据角色id 查询权限id
     *
     * @param roleId 角色id
     * @return 权限id集合
     */
    List<Long> listResourceIdsByRoleId(@Param("roleId") Long roleId);

    /**
     * 判断角色是否有权限
     *
     * @param roleId     角色id
     * @param resourceId 资源id
     * @return 是否有权限
     */
    Integer checkResourceAuth(@Param("roleId") Long roleId, @Param("resourceId") Long resourceId);
}
