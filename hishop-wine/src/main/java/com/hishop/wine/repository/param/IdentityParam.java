package com.hishop.wine.repository.param;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Builder;
import java.util.Date;

/**   
 * 用户身份表 数据库查询类
 * @author: Hu<PERSON>ia<PERSON>
 * @date: 2023-06-21
 */

@Data
@Builder
public class IdentityParam {


	private Long id;
    

	private Long userId;
    

	private Integer identityType;
    

	private String moduleCode;
    

	private Long roleId;
    

	private Boolean status;
    

	private Long createBy;
    

	private Date createTime;
    

	private Long updateBy;
    

	private Date updateTime;
    

}
