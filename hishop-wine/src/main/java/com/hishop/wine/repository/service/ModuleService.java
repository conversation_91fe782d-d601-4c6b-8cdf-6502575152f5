package com.hishop.wine.repository.service;

import com.hishop.wine.repository.dto.module.ModuleWxParamDTO;
import com.hishop.wine.repository.entity.ModuleInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 模块表 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
public interface ModuleService extends IService<ModuleInfo> {

    /**
     * 查询模块关联微信参数
     *
     * @return 模块关联微信参数
     */
    List<ModuleWxParamDTO> listModuleWxParam();

    /**
     * 模块绑定小程序
     *
     * @param appId       小程序id
     * @param moduleCodes 模块编码的集合
     */
    void bindMiniApp(String appId, List<String> moduleCodes);

}