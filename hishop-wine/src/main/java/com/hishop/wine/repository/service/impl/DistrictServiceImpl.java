package com.hishop.wine.repository.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.wine.repository.entity.District;
import com.hishop.wine.repository.dao.DistrictMapper;
import com.hishop.wine.repository.service.DistrictService;

import java.util.List;

/**
 * 地区表 服务实现类
 *
 * @author: HuBiao
 * @date: 2023-06-26
 */
@Service
public class DistrictServiceImpl extends ServiceImpl<DistrictMapper, District> implements DistrictService {

    /**
     * 批量查询(拼接sql)
     *
     * @param entityList 插入集合
     * @return 影响行数
     */
    @Override
    public Integer insertBatchSomeColumn(List<District> entityList) {
        return baseMapper.insertBatchSomeColumn(entityList);
    }

    /**
     * 批量更新(拼接sql)
     *
     * @param entityList 更新集合
     * @return 影响行数
     */
    @Override
    public Integer updateBatchSomeColumn(List<District> entityList) {
        return baseMapper.updateBatchSomeColumn(entityList);
    }

    /**
     * 更新下级名称
     *
     * @param id          组级id
     * @param oldFullName 旧的下级名称
     * @param newFullName 新的下级名称
     */
    @Override
    public void updateChildName(Integer id, String oldFullName, String newFullName) {
        baseMapper.updateChildName(id, oldFullName, newFullName);
    }

    /**
     * 是否存在下级
     *
     * @param id id
     * @return 是否存在下级
     */
    @Override
    public Boolean hasChild(Integer id) {
        return count(new LambdaQueryWrapper<District>().eq(District::getParentId, id)) > 0;
    }


    @Override
    public District getByNameAndLeven(String name, Integer level, Integer parentId) {
        LambdaQueryWrapper<District> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(District::getName, name);
        wrapper.eq(District::getLevel, level);
        wrapper.eq(District::getParentId, parentId);
        List<District> list = list(wrapper);
        if(CollUtil.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }
}