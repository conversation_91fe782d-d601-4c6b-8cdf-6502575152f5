package com.hishop.wine.repository.service.impl;

import com.hishop.wine.repository.entity.TransactionNotify;
import com.hishop.wine.repository.dao.TransactionNotifyMapper;
import com.hishop.wine.repository.service.TransactionNotifyService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.List;

/**
 * 交易回调消息 服务实现类
 *
 * @author: Hu<PERSON>iao
 * @date: 2023-06-28
 */
@Service
public class TransactionNotifyServiceImpl extends ServiceImpl<TransactionNotifyMapper, TransactionNotify> implements TransactionNotifyService {

}