package com.hishop.wine.repository.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.wine.repository.entity.WechatCode;

/**   
 * 微信二维码表 数据层服务类
 * @author: LiGuoQiang
 * @date: 2023-06-20
 */

public interface WechatCodeService extends IService<WechatCode> {


    /**
     * 通过二维码唯一标识获取码信息
     * <AUTHOR>
     * @date 2023/6/20
     */
    WechatCode qryByKey(String codeKey);
}