package com.hishop.wine.repository.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.wine.repository.dto.TagsCountDTO;
import com.hishop.wine.repository.entity.UserTags;

import java.util.List;
import java.util.Set;

/**
 * 用户标签表 数据层服务类
 *
 * @author: chenpeng
 * @date: 2023-07-17
 */

public interface UserTagsService extends IService<UserTags> {

    /**
     * 获取用户标签id的集合
     *
     * @param userId 用户id
     * @return 用户标签id的集合
     */
    List<Long> getTagIdsByUserId(Long userId);

    /**
     * 统计选中客户已有的标签数量
     * @param userSet
     * @return
     */
    List<TagsCountDTO> userTagsCount(Set<Long> userSet);

}