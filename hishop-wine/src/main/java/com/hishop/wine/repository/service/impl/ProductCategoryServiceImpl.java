package com.hishop.wine.repository.service.impl;

import cn.hutool.core.util.StrUtil;
import com.hishop.common.enums.DeleteFlagEnums;
import com.hishop.wine.repository.entity.ProductCategory;
import com.hishop.wine.repository.dao.ProductCategoryMapper;
import com.hishop.wine.repository.service.ProductCategoryService;
import com.hishop.wine.repository.param.ProductCategoryParam;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.List;

/**
 * 产品分类表 服务实现类
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
@Service
public class ProductCategoryServiceImpl extends ServiceImpl<ProductCategoryMapper, ProductCategory> implements ProductCategoryService {

    /**
     * 分页获取产品分类
     *
     * @param pageInfo 分类参数
     * @param param    筛选参数
     * @return 分页分类列表
     */
    @Override
    public Page<ProductCategory> qryPage(Page<ProductCategory> pageInfo, ProductCategoryParam param) {
        LambdaQueryWrapper<ProductCategory> wrapper = new LambdaQueryWrapper<ProductCategory>()
                .eq(ProductCategory::getIzDelete, DeleteFlagEnums.NO.getCode());
        if (StrUtil.isNotEmpty(param.getCategoryName())) {
            wrapper.like(ProductCategory::getCategoryName, param.getCategoryName());
        }
        return this.page(pageInfo, wrapper);
    }

    /**
     * 逻辑删除产品分类
     *
     * @param id 产品分类id
     */
    @Override
    public void logicDeleteById(Long id) {
        ProductCategory category = new ProductCategory();
        category.setId(id);
        category.setIzDelete(id);
        updateById(category);
    }
}