package com.hishop.wine.repository.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.wine.model.dto.bill.BillCountDto;
import com.hishop.wine.model.dto.bill.BillGroupDto;
import com.hishop.wine.model.po.bill.BillExportPo;
import com.hishop.wine.model.po.bill.BillQueryPo;
import com.hishop.wine.model.vo.bill.BillCountVo;
import com.hishop.wine.model.vo.bill.BillVo;
import com.hishop.wine.repository.dao.BillMapper;
import com.hishop.wine.repository.entity.Bill;
import com.hishop.wine.repository.service.BillService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 收入账单表(Bill)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-29 15:17:19
 */
@Service
public class BillServiceImpl extends ServiceImpl<BillMapper, Bill> implements BillService {

    @Override
    public Page<Bill> queryPage(Page<Bill> pageInfo, BillQueryPo billQueryPo) {
        LambdaQueryWrapper<Bill> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Bill::getIzDelete, false);
        if(billQueryPo.getPayMethod() != null) {
            queryWrapper.eq(Bill::getPayMethod, billQueryPo.getPayMethod());
        }
        if(billQueryPo.getStartTime() != null) {
            Date beginOfDay = DateUtil.beginOfDay(billQueryPo.getStartTime());
            queryWrapper.ge(Bill::getCreateTime, beginOfDay);
        }
        if(billQueryPo.getEndTime() != null) {
            Date endOfDay = DateUtil.endOfDay(billQueryPo.getEndTime());
            queryWrapper.le(Bill::getCreateTime, endOfDay);
        }
        if(StringUtils.isNotBlank(billQueryPo.getNo())) {
            queryWrapper.like(Bill::getBusinessNo, billQueryPo.getNo()).or().like(Bill::getThirdTransactionNo, billQueryPo.getNo());
        }
        if(billQueryPo.getTransactionType() != null) {
            queryWrapper.eq(Bill::getTransactionType, billQueryPo.getTransactionType());
        }
        if(billQueryPo.getMinFee() != null) {
            queryWrapper.ge(Bill::getAmount, billQueryPo.getMinFee());
        }
        if(billQueryPo.getMaxFee() != null) {
            queryWrapper.le(Bill::getAmount, billQueryPo.getMaxFee());
        }
        if(StringUtils.isNotBlank(billQueryPo.getSortSql())) {
            queryWrapper.last(billQueryPo.getSortSql());
        }
        return this.page(pageInfo, queryWrapper);
    }

    @Override
    public BillCountVo pageListCount(BillQueryPo billQueryPo) {
        if(billQueryPo.getStartTime() != null) {
            Date beginOfDay = DateUtil.beginOfDay(billQueryPo.getStartTime());
            billQueryPo.setStartTime(beginOfDay);
        }
        if(billQueryPo.getEndTime() != null) {
            Date endOfDay = DateUtil.endOfDay(billQueryPo.getEndTime());
            billQueryPo.setEndTime(endOfDay);
        }
        BillCountDto billCountDto = baseMapper.pageListCount(billQueryPo);
        BillCountVo vo = BeanUtil.copyProperties(billCountDto, BillCountVo.class);
        return vo;
    }

    @Override
    public Page<BillGroupDto> monthPageList(Page<BillGroupDto> pageInfo, BillQueryPo billQueryPo) {
        Assert.isTrue(billQueryPo.getYear() != null, "年份不能为空");
        Date beginOfDay = DateUtil.beginOfYear(billQueryPo.getYear());
        Date endOfDay = DateUtil.endOfYear(billQueryPo.getYear());
        billQueryPo.setStartTime(beginOfDay);
        billQueryPo.setEndTime(endOfDay);
        return baseMapper.monthPageList(pageInfo, billQueryPo);
    }

    @Override
    public BillCountVo monthPageListCount(BillQueryPo billQueryPo) {
        Assert.isTrue(billQueryPo.getYear() != null, "年份不能为空");
        Date beginOfDay = DateUtil.beginOfYear(billQueryPo.getYear());
        Date endOfDay = DateUtil.endOfYear(billQueryPo.getYear());
        billQueryPo.setStartTime(beginOfDay);
        billQueryPo.setEndTime(endOfDay);
        BillCountDto billCountDto = baseMapper.monthPageListCount(billQueryPo);
        Date dateR = null;
        // 获取当前时间
        if(DateUtil.year(billQueryPo.getYear()) == DateUtil.year(new Date())) {
            dateR = new Date();
        } else {
            dateR = endOfDay;
        }
        String start = DateUtil.format(beginOfDay, "yyyy-MM");
        String end = DateUtil.format(dateR, "yyyy-MM");
        BillCountVo billCountVo = BeanUtil.copyProperties(billCountDto, BillCountVo.class);
        billCountVo.setTimeIntervalStart(start);
        billCountVo.setTimeIntervalEnd(end);
        return billCountVo;
    }

    @Override
    public Page<BillGroupDto> dayPageList(Page<BillGroupDto> pageInfo, BillQueryPo billQueryPo) {
        Assert.isTrue(billQueryPo.getMonth() != null, "月份不能为空");
        Date beginOfDay = DateUtil.beginOfMonth(billQueryPo.getMonth());
        Date endOfDay = DateUtil.endOfMonth(billQueryPo.getMonth());
        billQueryPo.setStartTime(beginOfDay);
        billQueryPo.setEndTime(endOfDay);
        return baseMapper.dayPageList(pageInfo, billQueryPo);
    }

    @Override
    public BillCountVo dayPageListCount(BillQueryPo billQueryPo) {
        Assert.isTrue(billQueryPo.getMonth() != null, "月份不能为空");
        Date beginOfDay = DateUtil.beginOfMonth(billQueryPo.getMonth());
        Date endOfDay = DateUtil.endOfMonth(billQueryPo.getMonth());
        billQueryPo.setStartTime(beginOfDay);
        billQueryPo.setEndTime(endOfDay);
        BillCountDto billCountDto = baseMapper.monthPageListCount(billQueryPo);
        Date dateR = null;
        // 获取当前时间
        if(DateUtil.month(billQueryPo.getMonth()) == DateUtil.month(new Date())) {
            dateR = new Date();
        } else {
            dateR = endOfDay;
        }
        String start = DateUtil.format(beginOfDay, "yyyy-MM-dd");
        String end = DateUtil.format(dateR, "yyyy-MM-dd");
        BillCountVo billCountVo = BeanUtil.copyProperties(billCountDto, BillCountVo.class);
        billCountVo.setTimeIntervalStart(start);
        billCountVo.setTimeIntervalEnd(end);
        return billCountVo;
    }

    @Override
    public List<BillVo> exportList(BillExportPo param) {
        BillQueryPo billQueryPo = new BillQueryPo();
        billQueryPo.setPayMethod(param.getPayMethod());
        if(param.getMonth() != null && param.getDay() == null) {
            Date beginOfDay = DateUtil.beginOfMonth(param.getMonth());
            Date endOfDay = DateUtil.endOfMonth(param.getMonth());
            billQueryPo.setStartTime(beginOfDay);
            billQueryPo.setEndTime(endOfDay);
        } else if(param.getMonth() != null && param.getDay() != null) {
            Date beginOfDay = DateUtil.beginOfDay(param.getDay());
            Date endOfDay = DateUtil.endOfDay(param.getDay());
            billQueryPo.setStartTime(beginOfDay);
            billQueryPo.setEndTime(endOfDay);
        }
        return baseMapper.exportList(billQueryPo);
    }
}

