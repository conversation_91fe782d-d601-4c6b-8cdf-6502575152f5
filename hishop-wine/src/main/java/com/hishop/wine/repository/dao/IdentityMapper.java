package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.wine.repository.dto.PullNewSummaryDTO;
import com.hishop.wine.repository.entity.Identity;
import com.hishop.wine.repository.param.PullNewSummaryParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户身份表 Mapper 接口
 *
 * @author: Hu<PERSON>ia<PERSON>
 * @date: 2023-06-21
 */
public interface IdentityMapper extends BaseMapper<Identity> {

    /**
     * 逻辑删除身份信息
     *
     * @param ids    身份id列表
     * @param userId 操作人id
     */
    void logicDeleteByIds(@Param("ids") List<Long> ids, @Param("userId") Long userId);

    /**
     * 获取拉新汇总
     *
     * @param param 查询参数
     * @return 拉新汇总
     */
    List<PullNewSummaryDTO> getPullNewSummary(@Param("param") PullNewSummaryParam param);
}
