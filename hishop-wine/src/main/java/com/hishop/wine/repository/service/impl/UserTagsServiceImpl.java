package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.wine.repository.dao.UserTagsMapper;
import com.hishop.wine.repository.dto.TagsCountDTO;
import com.hishop.wine.repository.entity.UserTags;
import com.hishop.wine.repository.service.UserTagsService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**   
 * 用户标签表 服务实现类
 * @author: chenpeng
 * @date: 2023-07-17
 */

@Service
public class UserTagsServiceImpl extends ServiceImpl<UserTagsMapper, UserTags> implements UserTagsService  {

    /**
     * 获取用户标签id的集合
     *
     * @param userId 用户id
     * @return 用户标签id的集合
     */
    @Override
    public List<Long> getTagIdsByUserId(Long userId) {
        return listObjs(new LambdaQueryWrapper<UserTags>().eq(UserTags::getUserId, userId).select(UserTags::getTagId), obj -> Long.parseLong(obj.toString()));
    }

    @Override
    public List<TagsCountDTO> userTagsCount(Set<Long> userSet) {
        return baseMapper.userTagsCount(userSet);
    }
}