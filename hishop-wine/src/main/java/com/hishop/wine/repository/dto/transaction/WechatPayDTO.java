package com.hishop.wine.repository.dto.transaction;

import lombok.Data;

/**
 * 微信支付dto
 *
 * <AUTHOR>
 * @date : 2023/6/28
 */
@Data
public class WechatPayDTO {

    /**
     * 商户id
     */
    private String mchId;

    /**
     * 应用id
     */
    private String appId;

    public static WechatPayDTO of(String mchId, String appId) {
        WechatPayDTO dto = new WechatPayDTO();
        dto.setMchId(mchId);
        dto.setAppId(appId);
        return dto;
    }
}
