package com.hishop.wine.repository.param;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Builder;
import java.math.BigDecimal;
import java.util.Date;

/**   
 * 产品媒体资源表 数据库查询类
 *
 * @author: HuBia<PERSON>
 * @date: 2023-06-19
 */
@Data
@Builder
public class ProductMediaParam {


	private Long id;
    

	private Integer productId;
    

	private Integer mediaType;
    

	private String url;
    

}
