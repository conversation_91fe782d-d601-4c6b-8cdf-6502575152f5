package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.hishop.common.pojo.entity.BaseEntity;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;

/**   
 * 部门表 实体类
 * @Author: chenpeng
 * @since: 2023-04-25 10:50:00
 */

@Data
@NoArgsConstructor
@TableName("hishop_department")
public class Department extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1715914116794L;
	
    /**
     * 主键id，上级ID+组成id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Long id;

    /**
     * 组成id，编码的后段组成部分
     */
    private Integer formId;
    
    /**
    * 部门编号，生成规则：上级部门编号+组成id
    */
	private String departmentCode;
    
    /**
    * 部门名称
    */
	private String departmentName;
    
    /**
    * 上级部门ID
    */
	private Long parentId;
    
    /**
     * 排序
     */
    private Integer sort;
    

}
