package com.hishop.wine.controller;

import com.hishop.common.annotation.AuthIgnore;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.vo.wechat.WebQrCodeInitParamVO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import com.hishop.wine.biz.WechatWebUserBiz;
import javax.annotation.Resource;

import io.swagger.annotations.Api;

/**
* 微信网页应用用户表 相关接口
* @author: HuBiao
* @date: 2023-07-10
*/

@Api(value = "WechatWebUserController",tags="【微信网页应用】" )
@RestController
@RequestMapping("/wechatWebUser")
@Slf4j
public class WechatWebUserController {

    @Resource
    private WechatWebUserBiz wechatWebUserBiz;

    @ApiOperation(value = "获取生成微信二维码参数", httpMethod = "GET")
    @GetMapping("/getQrCodeParam")
    @AuthIgnore
    public ResponseBean<WebQrCodeInitParamVO> getQrCodeParam(@ApiParam("二维码类型 1-绑定微信二维码 2-扫码登录") @RequestParam Integer type) {
        return ResponseBean.success(wechatWebUserBiz.getQrCodeParam(type));
    }

}