package com.hishop.wine.controller;

import com.hishop.common.annotation.AuthIgnore;
import com.hishop.common.annotation.RejectRepeat;
import com.hishop.common.pojo.IdBatchPO;
import com.hishop.common.pojo.IdPO;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.api.DeliveryAddressFeign;
import com.hishop.wine.biz.DeliveryAddressBiz;
import com.hishop.wine.model.po.delivery.DeliveryAddressCreatePO;
import com.hishop.wine.model.po.delivery.DeliveryAddressUpdatePO;
import com.hishop.wine.model.po.delivery.DeliveryWxCreatePO;
import com.hishop.wine.model.vo.delivery.DeliveryAddressSimpleVO;
import com.hishop.wine.model.vo.delivery.DeliveryAddressVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 收货地址表 相关接口
 *
 * @author: HuBiao
 * @date: 2023-06-29
 */
@Api(value = "DeliveryAddressController", tags = "【收货地址管理】")
@RestController
@RequestMapping("/deliveryAddress")
public class DeliveryAddressController implements DeliveryAddressFeign {

    @Resource
    private DeliveryAddressBiz deliveryAddressBiz;

    @ApiOperation(value = "mini-创建收货地址", httpMethod = "POST")
    @PostMapping("/mini/create")
    @RejectRepeat
    public ResponseBean<Long> create(@Valid @RequestBody DeliveryAddressCreatePO deliveryAddressCreatePO) {
        return ResponseBean.success(deliveryAddressBiz.create(deliveryAddressCreatePO));
    }

    @ApiOperation(value = "mini-编辑收货地址", httpMethod = "POST")
    @PostMapping("/mini/update")
    public ResponseBean<Void> update(@Valid @RequestBody DeliveryAddressUpdatePO deliveryAddressUpdatePO) {
        deliveryAddressBiz.update(deliveryAddressUpdatePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "mini-删除收货地址", httpMethod = "POST")
    @PostMapping("/mini/delete")
    public ResponseBean<Void> delete(@RequestBody IdPO<Long> idPO) {
        deliveryAddressBiz.deleteById(idPO.getId());
        return ResponseBean.success();
    }

    @ApiOperation(value = "mini-设置为默认地址", httpMethod = "POST")
    @PostMapping("/mini/setDefault")
    @RejectRepeat
    public ResponseBean<Void> setDefault(@RequestBody IdPO<Long> idPO) {
        deliveryAddressBiz.setDefault(idPO.getId());
        return ResponseBean.success();
    }

    @ApiOperation(value = "mini-查询收货地址列表", httpMethod = "POST")
    @PostMapping("/mini/list")
    public ResponseBean<List<DeliveryAddressSimpleVO>> list() {
        return ResponseBean.success(deliveryAddressBiz.list());
    }

    @ApiOperation(value = "mini-查询收货地址详情", httpMethod = "GET")
    @GetMapping("/mini/getById")
    @Override
    public ResponseBean<DeliveryAddressVO> getById(@RequestParam(name = "id") Long id) {
        DeliveryAddressVO detail = deliveryAddressBiz.getById(id);
        return ResponseBean.success(detail);
    }

    @ApiOperation(value = "feign-查询收货地址详情", httpMethod = "POST")
    @PostMapping("/inner/getByIds")
    @Override
    public ResponseBean<List<DeliveryAddressVO>> getByIds(@RequestBody IdBatchPO<Long> idBatchPO) {
        return ResponseBean.success(deliveryAddressBiz.getByIds(idBatchPO.getIds()));
    }

    @ApiOperation(value = "feign-获取默认收货地址", httpMethod = "GET")
    @GetMapping("/inner/getDefaultAddress")
    @Override
    public ResponseBean<DeliveryAddressVO> getDefaultAddress(Long userId) {
        return ResponseBean.success(deliveryAddressBiz.getDefaultAddress(userId));
    }

    @ApiOperation(value = "mini-保存微信收货地址", httpMethod = "POST")
    @PostMapping("/mini/createWxDeliveryAddress")
    public ResponseBean<Long> createWxDeliveryAddress(@RequestBody @Valid DeliveryWxCreatePO deliveryWxCreatePO) {
        return ResponseBean.success(deliveryAddressBiz.createWxDeliveryAddress(deliveryWxCreatePO));
    }

    //---------------------------------PC端接口---------------------------------
    @ApiOperation(value = "PC-获取商家收货地址列表", httpMethod = "GET")
    @GetMapping("/pc/listByMerchant")
    public ResponseBean<List<DeliveryAddressSimpleVO>> listByMerchant() {
        return ResponseBean.success(deliveryAddressBiz.listByMerchant());
    }

}