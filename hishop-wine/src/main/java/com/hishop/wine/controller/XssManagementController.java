package com.hishop.wine.controller;

import com.hishop.common.response.ResponseBean;
import com.hishop.wine.common.config.XssConfig;
import com.hishop.wine.common.service.XssCleanupService;
import com.hishop.wine.common.utils.XssUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * XSS 防护管理接口
 * 提供 XSS 防护的监控和管理功能
 *
 * @author: System
 * @date: 2025-01-05
 */
@Api(value = "XssManagementController", tags = "【PC-XSS防护管理】")
@RestController
@RequestMapping("/xss")
@Slf4j
public class XssManagementController {

    @Resource
    private XssConfig xssConfig;

    @Resource
    private XssCleanupService xssCleanupService;

    @ApiOperation(value = "获取XSS防护配置信息", httpMethod = "GET")
    @GetMapping("/config")
    public ResponseBean<Map<String, Object>> getXssConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("enabled", xssConfig.isEnabled());
        config.put("logEnabled", xssConfig.isLogEnabled());
        config.put("excludeUrls", xssConfig.getExcludeUrls());
        config.put("strictFields", xssConfig.getStrictFields());
        config.put("allowedHtmlTags", xssConfig.getAllowedHtmlTags());
        config.put("allowedHtmlAttributes", xssConfig.getAllowedHtmlAttributes());
        
        return ResponseBean.success(config);
    }

    @ApiOperation(value = "获取XSS清理统计信息", httpMethod = "GET")
    @GetMapping("/statistics")
    public ResponseBean<String> getCleanupStatistics() {
        String statistics = xssCleanupService.getCleanupStatistics();
        return ResponseBean.success(statistics);
    }

    @ApiOperation(value = "清理所有配置数据中的XSS内容", httpMethod = "POST")
    @PostMapping("/cleanup/all")
    public ResponseBean<String> cleanupAllSettings() {
        try {
            xssCleanupService.cleanupAllSettings();
            return ResponseBean.success("XSS 数据清理完成");
        } catch (Exception e) {
            log.error("XSS 数据清理失败", e);
            return ResponseBean.error("XSS 数据清理失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "清理协议设置中的XSS内容", httpMethod = "POST")
    @PostMapping("/cleanup/protocol")
    public ResponseBean<String> cleanupProtocolSettings() {
        try {
            xssCleanupService.cleanupProtocolSettings();
            return ResponseBean.success("协议设置 XSS 清理完成");
        } catch (Exception e) {
            log.error("协议设置 XSS 清理失败", e);
            return ResponseBean.error("协议设置 XSS 清理失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "清理系统设置中的XSS内容", httpMethod = "POST")
    @PostMapping("/cleanup/system")
    public ResponseBean<String> cleanupSystemSettings() {
        try {
            xssCleanupService.cleanupSystemSettings();
            return ResponseBean.success("系统设置 XSS 清理完成");
        } catch (Exception e) {
            log.error("系统设置 XSS 清理失败", e);
            return ResponseBean.error("系统设置 XSS 清理失败: " + e.getMessage());
        }
    }

    @ApiOperation(value = "验证文本内容是否安全", httpMethod = "POST")
    @PostMapping("/validate/text")
    public ResponseBean<Map<String, Object>> validateText(
            @ApiParam(value = "待验证的文本内容", required = true)
            @RequestBody String content) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean isSafe = XssUtils.isSafeContent(content);
            boolean containsDangerous = XssUtils.containsDangerousScript(content);
            String cleanedContent = XssUtils.cleanRichText(content);
            
            result.put("isSafe", isSafe);
            result.put("containsDangerousScript", containsDangerous);
            result.put("originalLength", content != null ? content.length() : 0);
            result.put("cleanedLength", cleanedContent != null ? cleanedContent.length() : 0);
            result.put("hasChanges", content != null && !content.equals(cleanedContent));
            
            if (xssConfig.isLogEnabled()) {
                log.info("XSS 验证 - 原始长度: {}, 清理后长度: {}, 是否安全: {}", 
                    content != null ? content.length() : 0, 
                    cleanedContent != null ? cleanedContent.length() : 0, 
                    isSafe);
            }
            
            return ResponseBean.success(result);
        } catch (Exception e) {
            log.error("XSS 验证过程中发生异常", e);
            result.put("error", e.getMessage());
            return ResponseBean.error("XSS 验证失败", result);
        }
    }

    @ApiOperation(value = "清理文本内容中的XSS代码", httpMethod = "POST")
    @PostMapping("/clean/text")
    public ResponseBean<Map<String, Object>> cleanText(
            @ApiParam(value = "待清理的文本内容", required = true)
            @RequestBody String content,
            @ApiParam(value = "清理模式: RICH_TEXT, TEXT_ONLY, JSON, BASE64", required = false)
            @RequestParam(defaultValue = "RICH_TEXT") String mode) {
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            String cleanedContent;
            
            switch (mode.toUpperCase()) {
                case "TEXT_ONLY":
                    cleanedContent = XssUtils.cleanText(content);
                    break;
                case "JSON":
                    cleanedContent = XssUtils.cleanJsonString(content);
                    break;
                case "BASE64":
                    cleanedContent = XssUtils.cleanBase64Content(content);
                    break;
                case "RICH_TEXT":
                default:
                    cleanedContent = XssUtils.cleanRichText(content);
                    break;
            }
            
            result.put("originalContent", content);
            result.put("cleanedContent", cleanedContent);
            result.put("originalLength", content != null ? content.length() : 0);
            result.put("cleanedLength", cleanedContent != null ? cleanedContent.length() : 0);
            result.put("hasChanges", content != null && !content.equals(cleanedContent));
            result.put("mode", mode);
            
            return ResponseBean.success(result);
        } catch (Exception e) {
            log.error("XSS 清理过程中发生异常", e);
            result.put("error", e.getMessage());
            return ResponseBean.error("XSS 清理失败", result);
        }
    }

    @ApiOperation(value = "检查字段是否需要严格验证", httpMethod = "GET")
    @GetMapping("/check/field/{fieldName}")
    public ResponseBean<Map<String, Object>> checkField(
            @ApiParam(value = "字段名称", required = true)
            @PathVariable String fieldName) {
        
        Map<String, Object> result = new HashMap<>();
        result.put("fieldName", fieldName);
        result.put("isStrictField", xssConfig.isStrictField(fieldName));
        result.put("strictFields", xssConfig.getStrictFields());
        
        return ResponseBean.success(result);
    }

    @ApiOperation(value = "检查URL是否需要跳过XSS检查", httpMethod = "GET")
    @GetMapping("/check/url")
    public ResponseBean<Map<String, Object>> checkUrl(
            @ApiParam(value = "URL路径", required = true)
            @RequestParam String url) {
        
        Map<String, Object> result = new HashMap<>();
        result.put("url", url);
        result.put("shouldExclude", xssConfig.shouldExcludeUrl(url));
        result.put("excludeUrls", xssConfig.getExcludeUrls());
        
        return ResponseBean.success(result);
    }
}
