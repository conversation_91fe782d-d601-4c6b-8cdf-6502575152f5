package com.hishop.wine.controller;

import com.hishop.common.pojo.IdBatchPO;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.biz.IndexBiz;
import com.hishop.wine.common.enums.IndexTrendEnum;
import com.hishop.wine.model.vo.index.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description: 首页
 * @author: chenzw
 * @date: 2024/6/25 11:18
 */
@Api(value = "IndexController", tags = "【首页】")
@RestController
@RequestMapping("/index")
@RequiredArgsConstructor
public class IndexController {

    private final IndexBiz indexBiz;

    @ApiOperation("首页-获取数据概览")
    @GetMapping("/getDataOverview")
    public ResponseBean<IndexDataOverviewVo> getDataOverview() {
        return ResponseBean.success(indexBiz.getDataOverview());
    }

    @ApiOperation("首页-常用功能")
    @GetMapping("/queryCommonMenuList")
    public ResponseBean<List<IndexCommonMenuVo>> queryCommonMenuList() {
        return ResponseBean.success(indexBiz.queryCommonMenuList());
    }

    @ApiOperation(value = "首页-常用功能" , httpMethod = "POST")
    @PostMapping("/addCommonMenu")
    public ResponseBean<List<IndexCommonMenuVo>> addCommonMenu(@RequestBody @Validated IdBatchPO<Long> idBatchPo) {
        indexBiz.addCommonMenu(idBatchPo);
        return ResponseBean.success();
    }

    @ApiOperation("首页-经营趋势")
    @GetMapping("/queryIndexBusinessTrendList")
    public ResponseBean<List<IndexBusinessTrendVo>> queryIndexBusinessTrendList(@RequestParam(value = "indexTrendEnum") String indexTrendEnum) {
        return ResponseBean.success(indexBiz.queryIndexBusinessTrendList(IndexTrendEnum.valueOf(indexTrendEnum)));
    }

    @ApiOperation("首页-应用推荐、小程序列表")
    @GetMapping("/getIndexAppWechatList")
    public ResponseBean<IndexAppWechatVo> getIndexAppWechatList(){
        return ResponseBean.success(indexBiz.getIndexAppWechatList());
    }

    @ApiOperation("首页-获取客户数据")
    @GetMapping("/getIndexCustomData")
    public ResponseBean<IndexCustomDataVo> getIndexCustomData(){
        return ResponseBean.success(indexBiz.getIndexCustomData());
    }
}
