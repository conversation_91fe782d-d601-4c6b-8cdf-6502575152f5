package com.hishop.wine.controller;

import com.hishop.common.response.ResponseBean;
import com.hishop.wine.biz.ModuleBiz;
import com.hishop.wine.model.vo.module.BindModuleVO;
import com.hishop.wine.model.vo.module.ModuleVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 模块管理相关接口
 *
 * @author: chenpeng
 * @date: 2023-07-14
 */

@Api(value = "ModuleController", tags = "【模块管理】")
@RestController
@RequestMapping("/module")
public class ModuleController  {

    @Resource
    private ModuleBiz moduleBiz;

    @ApiOperation(value = "PC-获取授权模块列表", httpMethod = "GET")
    @GetMapping({"/pc/listAuthModule"})
    public ResponseBean<List<ModuleVO>> listAuthModule() {
        return ResponseBean.success(moduleBiz.listAuthModule());
    }

    @ApiOperation(value = "PC-查询模块列表(包含基础模块)", httpMethod = "GET")
    @GetMapping({"/pc/listAuthModuleIncludeBasic"})
    public ResponseBean<List<ModuleVO>> listAuthModuleIncludeBasic() {
        return ResponseBean.success(moduleBiz.listAuthModuleIncludeBasic());
    }

    @ApiOperation(value = "PC-获取授权模块且未绑定小程序列表", httpMethod = "GET")
    @GetMapping({"/pc/listAuthModuleUnBind"})
    public ResponseBean<List<ModuleVO>> listAuthModuleUnBind() {
        return ResponseBean.success(moduleBiz.listAuthModuleUnBind());
    }

    @ApiOperation(value = "PC-获取小程序已绑定模块", httpMethod = "GET")
    @GetMapping({"/pc/listABindModule"})
    public ResponseBean<List<BindModuleVO>> listABindModule(@RequestParam("appId") String appId) {
        return ResponseBean.success(moduleBiz.listABindModule(appId));
    }

    @ApiOperation(value = "PC-查询是否有扫码营销模块", httpMethod = "GET")
    @GetMapping({"/pc/queryHasScanCodeModule"})
    public ResponseBean<Boolean> queryHasScanCodeModule() {
        return ResponseBean.success(moduleBiz.queryHasScanCodeModule());
    }

}