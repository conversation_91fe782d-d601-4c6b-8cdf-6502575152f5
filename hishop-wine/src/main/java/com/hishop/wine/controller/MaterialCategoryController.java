package com.hishop.wine.controller;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hishop.common.pojo.IdPO;
import com.hishop.log.annotation.OperationLog;
import com.hishop.log.enums.OperationType;
import com.hishop.wine.biz.MaterialBiz;
import com.hishop.wine.model.po.material.MaterialCategoryCreatePO;
import com.hishop.wine.model.po.material.MaterialCategoryDeletePO;
import com.hishop.wine.model.po.material.MaterialCategoryMovePO;
import com.hishop.wine.model.po.material.MaterialCategoryUpdatePO;
import com.hishop.wine.model.vo.micropage.MicropageCategoryVO;
import com.hishop.wine.repository.dto.MaterialCategoryDTO;
import com.hishop.wine.repository.dto.MaterialCategoryTreeDTO;
import com.hishop.wine.common.enums.MaterialType;
import io.swagger.annotations.ApiParam;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hishop.wine.biz.MaterialCategoryBiz;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.hishop.common.response.ResponseBean;

import javax.annotation.Resource;
import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 资源分组表 相关接口
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Api(value = "MaterialCategoryController", tags = "【素材库分组管理】")
@RestController
@RequestMapping("/materialCategory")
public class MaterialCategoryController {

    @Resource
    private MaterialCategoryBiz materialCategoryBiz;

    @Resource
    private MaterialBiz materialBiz;

    @ApiOperation("PC-新增分组")
    @PostMapping(path = "/pc/create")
    @OperationLog
    public ResponseBean createCategory(@RequestBody @Valid MaterialCategoryCreatePO materialCategoryCreatePO) {
        materialCategoryBiz.create(materialCategoryCreatePO);
        return ResponseBean.success();
    }

    @ApiOperation("PC-编辑分组")
    @PostMapping(path = "/pc/update")
    @OperationLog
    public ResponseBean updateCategory(@RequestBody @Valid MaterialCategoryUpdatePO materialCategoryUpdatePO) {
        materialCategoryBiz.update(materialCategoryUpdatePO.getId(), materialCategoryUpdatePO.getParentId(), materialCategoryUpdatePO.getName());
        return ResponseBean.success();
    }

    @ApiOperation("PC-获取所有分组")
    @GetMapping(path = "/pc/tree")
    public ResponseBean<List<MaterialCategoryTreeDTO>> getCategories(@RequestParam("materialType") Integer materialType) {
        List<MaterialCategoryTreeDTO> allMaterialCategories = materialCategoryBiz.getAllMaterialCategories(materialType);
        return ResponseBean.success(allMaterialCategories);
    }

    @ApiOperation("PC-获取可用的分组 tree类型-移动分组时")
    @GetMapping(path = "/pc/parent/list")
    public ResponseBean<List<MaterialCategoryTreeDTO>> getParentList(@RequestParam("materialType") Integer materialType,@RequestParam("ids") String ids) {
        //如果没有传id 直接返回 1 2 级
        if(StrUtil.isEmpty(ids)){
            List<MaterialCategoryTreeDTO> result= materialCategoryBiz.listParent(materialType);
            return ResponseBean.success(result);
        }

        List<Long> cateIds = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());

        //数组为空 直接返回 1 2 级
        if(CollectionUtils.isEmpty(cateIds)){
            List<MaterialCategoryTreeDTO> result= materialCategoryBiz.listParent(materialType);
            return ResponseBean.success(result);
        }

        List<MaterialCategoryTreeDTO> categorys =materialCategoryBiz.getListByIds(materialType,cateIds);
        int maxLevel= categorys.stream().mapToInt(MaterialCategoryTreeDTO::getLevel).max()
                .orElse(Integer.MIN_VALUE);

        //取出可选的相关分组
        List<MaterialCategoryTreeDTO> canSelectCategory =  materialCategoryBiz.getListLessThanLevel(materialType,maxLevel,cateIds);
        return ResponseBean.success(canSelectCategory);
    }

    @ApiOperation("PC-获取指定分组")
    @GetMapping(path = "/pc/detail")
    public ResponseBean<MaterialCategoryDTO> detail(@RequestParam @ApiParam("资源分组id") Long id) {
        MaterialCategoryDTO materialCategoryDTO = materialCategoryBiz.getById(id);
        return ResponseBean.success(materialCategoryDTO);
    }

    @ApiOperation("PC-删除分组及其下级分组")
    @PostMapping(path = "/pc/delete")
    @OperationLog(primaryKey = "topId")
    public ResponseBean deleteCategories(@RequestBody @Valid MaterialCategoryDeletePO materialCategoryDeletePO) {
        materialCategoryBiz.delete(materialCategoryDeletePO.getDeleteFiles(), Arrays.asList(materialCategoryDeletePO.getTopId()));
        return ResponseBean.success();
    }

    @ApiOperation("PC-更新资源分组的资源数量")
    @PostMapping(path = "/pc/materials/total")
    public ResponseBean updateMaterialsCount(@RequestBody @Valid IdPO<Long> idPO) {
        materialBiz.updateCountByMaterialCategoryId(idPO.getId());
        return ResponseBean.success();
    }

    @ApiOperation("PC-移动分组")
    @PostMapping(path = "/pc/move")
    @OperationLog(operationType = OperationType.UPDATE, primaryKey = "ids")
    public ResponseBean moveCategories(@RequestBody @Valid MaterialCategoryMovePO materialCategoryMovePO) {
        materialCategoryBiz.batchMove(materialCategoryMovePO.getTopId(), materialCategoryMovePO.getIds());
        return ResponseBean.success();
    }

    //-----------------------------------小程序接口-----------------------------------
    @ApiOperation("小程序-获取所有分组")
    @GetMapping(path = "/mini/tree")
    public ResponseBean<List<MaterialCategoryTreeDTO>> getCategoriesMini(@RequestParam("materialType") Integer materialType) {
        List<MaterialCategoryTreeDTO> allMaterialCategories = materialCategoryBiz.getAllMaterialCategories(materialType);
        return ResponseBean.success(allMaterialCategories);
    }


}