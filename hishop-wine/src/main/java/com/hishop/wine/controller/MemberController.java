package com.hishop.wine.controller;

import com.hishop.common.export.handler.ExportHelper;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.log.enums.OperationType;
import com.hishop.wine.biz.MemberBiz;
import com.hishop.wine.biz.export.enums.BizTypeEnum;
import com.hishop.wine.model.po.basic.AdminQueryPO;
import com.hishop.wine.model.po.member.MemberQueryPO;
import com.hishop.wine.model.po.member.MemberSetRankBatchPO;
import com.hishop.wine.model.po.member.MemberSetRankPO;
import com.hishop.wine.model.vo.basic.AdminVO;
import com.hishop.wine.model.vo.member.MemberSelectVO;
import com.hishop.wine.model.vo.member.MemberVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/7/25
 */
@Api(value = "MemberController", tags = "【会员管理】")
@RestController
@RequestMapping("/member")
public class MemberController {

    @Resource
    private MemberBiz memberBiz;
    @Resource
    private ExportHelper exportHelper;

    @ApiOperation(value = "PC-会员列表", httpMethod = "POST")
    @PostMapping("/pc/pageList")
    public ResponseBean<PageResult<MemberVO>> pageList(@Valid @RequestBody MemberQueryPO memberQueryPO) {
        return ResponseBean.success(memberBiz.pageList(memberQueryPO));
    }

    @ApiOperation(value = "PC-会员导出", httpMethod = "POST")
    @PostMapping("/pc/export")
    public void export(@Valid @RequestBody MemberQueryPO memberQueryPO) {
        exportHelper.export(BizTypeEnum.MEMBER, memberQueryPO);
    }

    @ApiOperation(value = "PC-会员下拉", httpMethod = "GET")
    @GetMapping("/pc/listForSelect")
    public ResponseBean<List<MemberSelectVO>> listForSelect(@RequestParam(required = false) String searchKey,
                                                            @RequestParam(required = false) Long userId) {
        return ResponseBean.success(memberBiz.listForSelect(userId, searchKey));
    }

    @ApiOperation(value = "PC-设置头衔", httpMethod = "POST")
    @PostMapping("/pc/setRank")
    @OperationLog(primaryKey = "userId", operationType = OperationType.UPDATE)
    public ResponseBean setRank(@RequestBody @Valid MemberSetRankPO memberSetRankPO) {
        memberBiz.setRank(memberSetRankPO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-批量设置头衔", httpMethod = "POST", hidden = true)
    @PostMapping("/pc/setRankBatch")
    public ResponseBean setRankBatch(@RequestBody @Valid MemberSetRankBatchPO memberSetRankBatchPO) {
        memberBiz.setRankBatch(memberSetRankBatchPO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-会员详情", httpMethod = "GET")
    @PostMapping("/pc/detail")
    public ResponseBean<MemberVO> detail(@RequestParam Long id) {
        return ResponseBean.success(memberBiz.detail(id));
    }

}
