package com.hishop.wine.controller;


import com.hishop.common.annotation.AuthIgnore;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.log.enums.OperationType;
import com.hishop.wine.biz.MicropageBiz;
import com.hishop.wine.model.po.micropage.*;
import com.hishop.wine.model.vo.micropage.MicropageDetailVO;
import com.hishop.wine.model.vo.micropage.MicropageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@Api(value = "MicropageController",tags="微页面相关接口" )
@RestController
@RequestMapping("/micropage")
public class MicropageController {
    @Resource
    private MicropageBiz micropageBiz;

    @ApiOperation("PC-创建微页面")
    @PostMapping(path = "/pc/create")
    @OperationLog
    public ResponseBean<Boolean> create(@RequestBody @Valid MicropageCreatePO micropageCreatePO) {
        boolean result= micropageBiz.create(micropageCreatePO);
        return ResponseBean.success(result);
    }

    @ApiOperation("PC-修改微页面")
    @PostMapping(path = "/pc/update")
    @OperationLog
    public ResponseBean<Boolean> update(@RequestBody @Valid MicropageUpdatePO micropageUpdatePO) {
        boolean result= micropageBiz.update(micropageUpdatePO);
        return ResponseBean.success(result);
    }

    @ApiOperation("PC-获取微页面详情")
    @GetMapping(path = "/pc/detail")
    public ResponseBean<MicropageDetailVO> detail(@RequestParam @ApiParam("微页面id") Long id) {
        MicropageDetailVO result = micropageBiz.detail(id);
        return ResponseBean.success(result);
    }

    @ApiOperation("PC-批量更改分组")
    @PostMapping(path = "/pc/change/category")
    @OperationLog(operationType = OperationType.UPDATE, primaryKey = "ids")
    public ResponseBean<Boolean> change(@RequestBody @Valid MicropageChangeCategoryPO micropageChangeCategoryPO) {
        boolean result= micropageBiz.changeCategory(micropageChangeCategoryPO);
        return ResponseBean.success(result);
    }

    @ApiOperation("PC-批量删除微页面")
    @PostMapping(path = "/pc/deletes")
    @OperationLog(primaryKey = "ids")
    public ResponseBean<Boolean> deletes(@RequestBody @Valid MicropageDeletePO micropageDeletePO) {
        boolean result= micropageBiz.deletes(micropageDeletePO);
        return ResponseBean.success(result);
    }

    @ApiOperation(value = "分页获取", httpMethod = "POST")
    @PostMapping("/pc/pageDetail")
    public ResponseBean<PageResult<MicropageVO>> pageDetail(@RequestBody MicropageQueryPO pagePo) {
        return ResponseBean.success(micropageBiz.pageList(pagePo));
    }

    @ApiOperation("mini-获取微页面详情")
    @GetMapping(path = "/mini/detail")
    @AuthIgnore
    public ResponseBean<MicropageDetailVO> miniDetail(@RequestParam @ApiParam("微页面id") Long id) {
        MicropageDetailVO result = micropageBiz.miniDetail(id);
        return ResponseBean.success(result);
    }
}
