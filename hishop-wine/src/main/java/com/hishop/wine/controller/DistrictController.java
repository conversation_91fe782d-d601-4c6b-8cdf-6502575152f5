package com.hishop.wine.controller;

import com.hishop.common.annotation.RejectRepeat;
import com.hishop.common.pojo.IdPO;
import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.wine.api.DistrictFeign;
import com.hishop.wine.biz.DistrictBiz;
import com.hishop.wine.common.utils.TencentMapUtils;
import com.hishop.wine.model.po.basic.AreaNamePO;
import com.hishop.wine.model.po.basic.DistrictCreatePO;
import com.hishop.wine.model.po.basic.DistrictUpdatePO;
import com.hishop.wine.model.vo.basic.DistrictTreeVO;
import com.hishop.wine.model.vo.basic.DistrictVO;
import com.hishop.wine.model.vo.basic.RegionTreeVO;
import com.hishop.wine.model.vo.basic.RegionVO;
import com.hishop.wine.model.vo.map.TecentAddressDetailDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;


/**
 * 地区表 相关接口
 *
 * @author: HuBiao
 * @date: 2023-06-26
 */
@Api(value = "DistrictController", tags = "【行政区域管理】")
@RestController
@RequestMapping("/district")
public class DistrictController implements DistrictFeign {

    @Resource
    private DistrictBiz districtBiz;

    @ApiOperation("获取行政区域树")
    @GetMapping("/tree")
    @Override
    public ResponseBean<List<DistrictTreeVO>> tree(@RequestParam(value = "maxLevel", defaultValue = "3") Integer maxLevel) {
        return ResponseBean.success(districtBiz.tree(maxLevel));
    }

    @ApiOperation("获取区域列表")
    @GetMapping("/list")
    @Override
    public ResponseBean<List<DistrictVO>> list(@RequestParam(value = "parentId", defaultValue = "0") Integer parentId) {
        return ResponseBean.success(districtBiz.list(parentId));
    }

    @ApiOperation("获取区域列表(包含全部)")
    @GetMapping("/listHasWhole")
    public ResponseBean<List<DistrictVO>> listHasWhole(@RequestParam(value = "parentId", defaultValue = "0") Integer parentId) {
        List<DistrictVO> districtList = districtBiz.list(parentId);
        if (!CollectionUtils.isEmpty(districtList)) {
            districtList.add(0, DistrictVO.ofWhole(districtList.get(0)));
        }
        return ResponseBean.success(districtList);
    }

    @ApiOperation("获取区域详情")
    @GetMapping("/detail")
    @Override
    public ResponseBean<DistrictVO> detail(@RequestParam Integer id) {
        return ResponseBean.success(districtBiz.detail(id));
    }

    @ApiOperation("获取大区集合")
    @GetMapping("/listRegions")
    @Override
    public ResponseBean<List<RegionVO>> listRegions() {
        return ResponseBean.success(districtBiz.listRegions());
    }

    @ApiOperation("查询等级内的区域信息")
    @GetMapping("/listByLevel")
    @Override
    public ResponseBean<List<DistrictVO>> listByLevel(@RequestParam(value = "maxLevel", defaultValue = "3") Integer maxLevel) {
        return ResponseBean.success(districtBiz.listByLevel(maxLevel));
    }

    @Override
    public ResponseBean<DistrictVO> getByLocation(String lng, String lat) {
        TecentAddressDetailDTO addressDTO = TencentMapUtils.getAddressByLocation(new BigDecimal(lat), new BigDecimal(lng));
        DistrictVO districtVO = new DistrictVO();
        districtVO.setId(Integer.parseInt(addressDTO.getAdInfo().getAdcode()));
        String fullName = addressDTO.getAdInfo().getName();
        districtVO.setFullName(fullName.substring(fullName.indexOf(",")));
        return ResponseBean.success(districtVO);
    }


    @Override
    public ResponseBean<TecentAddressDetailDTO> getMapByLocation(String lng, String lat) {
        return ResponseBean.success(TencentMapUtils.getAddressByLocation(new BigDecimal(lat), new BigDecimal(lng)));
    }

    @ApiOperation("获取大区树")
    @GetMapping("/pc/regionTree")
    public ResponseBean<List<RegionTreeVO>> regionTree(@RequestParam(value = "maxLevel", defaultValue = "3") Integer maxLevel) {
        return ResponseBean.success(districtBiz.regionTree(maxLevel));
    }

    @ApiOperation("同步地图数据")
    @GetMapping("/pc/sync")
    @RejectRepeat(tips = "当前同步任务执行中..., 请勿重复点击")
    public ResponseBean syncDistrict() {
        districtBiz.syncDistrict();
        return ResponseBean.success();
    }

    @ApiOperation("新增地区")
    @PostMapping("/pc/create")
    @OperationLog
    public ResponseBean create(@RequestBody @Valid DistrictCreatePO districtCreatePO) {
        districtBiz.createDistrict(districtCreatePO);
        return ResponseBean.success();
    }

    @ApiOperation("编辑地区")
    @PostMapping("/pc/update")
    @OperationLog
    public ResponseBean<List<RegionTreeVO>> updateDistrict(@RequestBody @Valid DistrictUpdatePO districtUpdatePO) {
        districtBiz.updateDistrict(districtUpdatePO);
        return ResponseBean.success();
    }

    @ApiOperation("删除地区")
    @PostMapping("/pc/delete")
    @OperationLog
    public ResponseBean deleteDistrict(@RequestBody @Valid IdPO<Integer> idPO) {
        districtBiz.deleteById(idPO.getId());
        return ResponseBean.success();
    }

    @ApiOperation("根据最后一级id获取区域列表")
    @GetMapping("/listByLastId")
    public ResponseBean<List<DistrictVO>> listByLastId(@RequestParam Integer lastId) {
        return ResponseBean.success(districtBiz.listByLastId(lastId));
    }


    @ApiOperation("根据名称，查询区级信息")
    @PostMapping("/getAreaByName")
    public ResponseBean<DistrictVO> getAreaByName(@RequestBody @Valid AreaNamePO areaNamePO){
        return ResponseBean.success(districtBiz.getAreaByName(areaNamePO));
    }

}