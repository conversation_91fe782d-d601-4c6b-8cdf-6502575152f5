package com.hishop.wine.controller;

import com.hishop.common.annotation.RejectRepeat;
import com.hishop.common.export.handler.ExportHelper;
import com.hishop.common.pojo.IdBatchPO;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.log.enums.OperationType;
import com.hishop.wine.api.ProductFeign;
import com.hishop.wine.biz.ProductBiz;
import com.hishop.wine.biz.export.enums.BizTypeEnum;
import com.hishop.wine.model.po.product.*;
import com.hishop.wine.model.vo.product.ProductInnerVO;
import com.hishop.wine.model.vo.product.ProductPageVO;
import com.hishop.wine.model.vo.product.ProductVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


/**
 * 产品表 相关接口
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
@Api(value = "ProductController", tags = "【产品管理】")
@RestController
@RequestMapping("/product")
public class ProductController implements ProductFeign {

    @Resource
    private ProductBiz productBiz;
    @Resource
    private ExportHelper exportHelper;

    @ApiOperation(value = "PC-新增产品", httpMethod = "POST")
    @PostMapping("/pc/create")
    @OperationLog
    public ResponseBean create(@Valid @RequestBody ProductCreatePO productCreatePO) {
        productBiz.create(productCreatePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-编辑产品", httpMethod = "POST")
    @PostMapping("/pc/update")
    @OperationLog
    public ResponseBean update(@Valid @RequestBody ProductUpdatePO productUpdatePO) {
        productBiz.update(productUpdatePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-删除产品", httpMethod = "POST")
    @PostMapping("/pc/deleteBatch")
    @OperationLog(primaryKey = "ids")
    public ResponseBean delete(@RequestBody @Valid IdBatchPO<Long> idBatchPO) {
        productBiz.deleteByIds(idBatchPO.getIds());
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-查询产品详情", httpMethod = "GET")
    @GetMapping("/pc/detail")
    public ResponseBean<ProductVO> detail(@RequestParam(name = "id") Long id) {
        ProductVO detail = productBiz.detail(id);
        return ResponseBean.success(detail);
    }

    @Override
    @ApiOperation(value = "Feign-查询产品详情", httpMethod = "GET", hidden = true)
    @GetMapping("/pc/inner/detail")
    public ResponseBean<ProductInnerVO> getDetailForInner(@RequestParam(name = "id") Long id) {
        ProductInnerVO detail = productBiz.getDetailForInner(id);
        return ResponseBean.success(detail);
    }

    @PostMapping("/pc/inner/listById")
    @ApiOperation(value = "Feign-根据ID批量查询", httpMethod = "POST", hidden = true)
    @Override
    public ResponseBean<List<ProductInnerVO>> listById(@Valid @RequestBody ProductQueryPO qryPO) {
        List<ProductInnerVO> list = productBiz.listById(qryPO);
        return ResponseBean.success(list);
    }

    @Override
    @ApiOperation(value = "PC-查询产品列表", httpMethod = "POST")
    @PostMapping("/pc/pageList")
    public ResponseBean<PageResult<ProductPageVO>> pageList(@RequestBody @Valid ProductQueryPO pagePO) {
        return ResponseBean.success(productBiz.pageList(pagePO));
    }

    @ApiOperation(value = "PC-产品导出", httpMethod = "POST")
    @PostMapping("/pc/export")
    public void exportProduct(@RequestBody @Valid ProductQueryPO pagePO) {
        exportHelper.export(BizTypeEnum.PRODUCT, pagePO);
        // return ResponseBean.success(result);
    }

    @ApiOperation(value = "PC-修改产品价格", httpMethod = "POST")
    @PostMapping("/pc/updatePriceBatch")
    @OperationLog(primaryKey = "ids")
    public ResponseBean updatePrice(@RequestBody @Valid ProductUpdatePricePO updatePricePO) {
        productBiz.updatePrice(updatePricePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-导入产品", httpMethod = "POST")
    @PostMapping("/pc/import")
    @RejectRepeat
    public ResponseBean<String> importProduct(@RequestParam("file") MultipartFile file) {
        return ResponseBean.success(productBiz.importProduct(file));
    }

    @Override
    @ApiOperation(value = "feign-查询产品id", httpMethod = "POST", hidden = true)
    @PostMapping("/inner/queryIds")
    public ResponseBean<List<Long>> queryIds(@RequestBody @Valid ProductIdQueryPO productIdQueryPO) {
        return ResponseBean.success(productBiz.queryIds(productIdQueryPO));
    }

    @ApiOperation(value = "mini-查询产品详情", httpMethod = "GET")
    @GetMapping("/mini/detail")
    public ResponseBean<ProductVO> getMiniDetail(@RequestParam(name = "id") Long id) {
        ProductVO detail = productBiz.detail(id);
        return ResponseBean.success(detail);
    }
}