package com.hishop.wine.controller;

import com.hishop.common.response.ResponseBean;
import com.hishop.moderation.model.*;
import com.hishop.wine.biz.ModerationBiz;
import com.hishop.wine.model.po.moderation.ContentModerationCreatePO;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2023/8/3
 */
@RestController
@RequestMapping("/moderation")
public class ModerationController {

    @Resource
    private ModerationBiz moderationBiz;

    /**
     * 发起审核
     *
     * @param contentModerationCreatePO 发起审核参数
     * @return 审核id
     */
    @ApiOperation(value = "发起审核", httpMethod = "POST")
    @PostMapping("/check")
    public ResponseBean<Long> check(@Valid @RequestBody ContentModerationCreatePO contentModerationCreatePO) {
        return ResponseBean.success(moderationBiz.check(contentModerationCreatePO));
    }

    /**
     * 文本内容审核
     *
     * <AUTHOR>
     * @date 2023/8/3
     */
    @PostMapping("/text")
    public ResponseBean<ModerationResult> checkText(@RequestBody TextData textData) {
        return ResponseBean.success(moderationBiz.checkText(textData));
    }

    /**
     * 图像内容审核
     *
     * <AUTHOR>
     * @date 2023/8/3
     */
    @PostMapping("/image")
    public ResponseBean<ModerationResult> checkImage(@RequestBody ImageData imageData) {
        return ResponseBean.success(moderationBiz.checkImage(imageData));
    }

    /**
     * 视频内容审核
     *
     * <AUTHOR>
     * @date 2023/8/3
     */
    @PostMapping("/video")
    public ResponseBean<ModerationResult> checkVideo(@RequestBody VideoData videoData) {
        return ResponseBean.success(moderationBiz.checkVideo(videoData));
    }

    /**
     * 音频内容审核
     *
     * <AUTHOR>
     * @date 2023/8/3
     */
    @PostMapping("/audio")
    public ResponseBean<ModerationResult> checkAudio(@RequestBody AudioData audioData) {
        return ResponseBean.success(moderationBiz.checkAudio(audioData));
    }

}
