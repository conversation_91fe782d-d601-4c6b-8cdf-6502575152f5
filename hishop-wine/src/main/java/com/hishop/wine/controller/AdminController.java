package com.hishop.wine.controller;

import com.hishop.common.pojo.IdBatchPO;
import com.hishop.common.pojo.IdStatusPo;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.log.enums.OperationType;
import com.hishop.wine.biz.AdminBiz;
import com.hishop.wine.biz.IdentityBiz;
import com.hishop.wine.model.po.admin.BusinessUserQueryPo;
import com.hishop.wine.model.po.basic.AdminCreatePO;
import com.hishop.wine.model.po.basic.AdminQueryPO;
import com.hishop.wine.model.po.basic.AdminUpdatePO;
import com.hishop.wine.model.vo.basic.AdminDetailVO;
import com.hishop.wine.model.vo.basic.AdminVO;
import com.hishop.wine.model.vo.user.BusinessUserVo;
import com.hishop.wine.model.vo.user.UserDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 用户身份表 相关接口
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
@Api(value = "AdminController", tags = "【管理员管理】")
@RestController
@RequestMapping("/admin")
public class AdminController {

    @Resource
    private AdminBiz adminBiz;
    @Resource
    private IdentityBiz identityBiz;

    @ApiOperation(value = "PC-新增管理员", httpMethod = "POST")
    @PostMapping("/pc/create")
    @OperationLog
    public ResponseBean<Void> create(@Valid @RequestBody AdminCreatePO adminCreatePO) {
        adminBiz.createAdmin(adminCreatePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-编辑管理员", httpMethod = "POST")
    @PostMapping("/pc/update")
    @OperationLog(queryMethod = "getAdminDetail")
    public ResponseBean<Void> update(@Valid @RequestBody AdminUpdatePO adminUpdatePO) {
        adminBiz.updateAdmin(adminUpdatePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-查询管理员列表", httpMethod = "POST")
    @PostMapping("/pc/pageList")
    public ResponseBean<PageResult<AdminVO>> pageListAdmin(@Valid @RequestBody AdminQueryPO adminQueryPO) {
        return ResponseBean.success(adminBiz.pageListAdmin(adminQueryPO));
    }

    @ApiOperation(value = "PC-查询管理员部门列表", httpMethod = "POST")
    @PostMapping("/pc/pageDepList")
    public ResponseBean<PageResult<AdminVO>> pageListAdminDep(@Valid @RequestBody AdminQueryPO adminQueryPO) {
        return ResponseBean.success(adminBiz.pageListAdminDep(adminQueryPO));
    }


    @ApiOperation(value = "PC-查询管理员详情", httpMethod = "GET")
    @GetMapping("/pc/detail")
    public ResponseBean<AdminDetailVO> getAdminDetail(@RequestParam Long id) {
        return ResponseBean.success(adminBiz.getAdminDetail(id));
    }

    @ApiOperation(value = "PC-修改管理员状态", httpMethod = "POST")
    @PostMapping("/pc/statusBatch")
    @OperationLog(primaryKey = "ids", operationType = OperationType.UPDATE, queryMethod = "getAdminDetail")
    public ResponseBean<Void> updateAdminStatus(@Valid @RequestBody IdStatusPo<Long> idStatusPo) {
        identityBiz.updateStatusByIds(idStatusPo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-删除管理员", httpMethod = "POST")
    @PostMapping("/pc/deleteBatch")
    @OperationLog(primaryKey = "ids")
    public ResponseBean<Void> updateAdminStatus(@Valid @RequestBody IdBatchPO<Long> idBatchPO) {
        identityBiz.deleteByIds(idBatchPO.getIds());
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-查询用户详情", httpMethod = "GET")
    @GetMapping("/pc/userDetail")
    public ResponseBean<UserDetailVo> userDetail(@RequestParam Long id) {
        return ResponseBean.success(adminBiz.userDetail(id));
    }

    @ApiOperation(value = "PC-查询业务员列表（分页）", httpMethod = "POST")
    @PostMapping("/pc/businessPageList")
    public ResponseBean<PageResult<BusinessUserVo>> businessPageList(@RequestBody BusinessUserQueryPo queryPo) {
        return ResponseBean.success(adminBiz.businessPageList(queryPo));
    }
}