package com.hishop.wine.controller;

import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.biz.LoginErrorBiz;
import com.hishop.wine.model.po.LoginErrorQueryPO;
import com.hishop.wine.model.po.bill.BillQueryPo;
import com.hishop.wine.model.vo.LoginErrorVo;
import com.hishop.wine.model.vo.bill.BillVo;
import com.hishop.wine.repository.entity.LoginError;
import io.swagger.annotations.ApiOperation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 登录异常表(LoginError)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-13 10:40:44
 */
@RestController
@RequestMapping("loginError")
public class LoginErrorController {
    /**
     * 服务对象
     */
    @Resource
    private LoginErrorBiz loginErrorBiz;

    @ApiOperation(value = "分页查询", httpMethod = "POST")
    @PostMapping("/pageList")
    public ResponseBean<PageResult<LoginErrorVo>> pageList(@Validated @RequestBody LoginErrorQueryPO loginErrorQueryPo) {
        return ResponseBean.success(loginErrorBiz.pageList(loginErrorQueryPo));
    }

    @ApiOperation(value = "重置微信号", httpMethod = "POST")
    @GetMapping("/resetMinUser")
    public ResponseBean<Void> resetMinUser(@RequestParam("id") Long id) {
        loginErrorBiz.resetMinUser(id);
        return ResponseBean.success();
    }

}

