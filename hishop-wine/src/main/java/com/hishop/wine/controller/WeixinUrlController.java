package com.hishop.wine.controller;

import com.hishop.common.annotation.AuthIgnore;
import com.hishop.wine.common.config.WxOutLinkConfig;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 微信配置回调
* @author: chenpeng
* @date: 2023-08-01
*/

@Api(value = "WeixinUrlController",tags="【微信配置回调】" )
@RestController
@RequestMapping("/weixin")
@Slf4j
public class WeixinUrlController {

    @Resource
    private WxOutLinkConfig wxOutLinkConfig;

    @ApiOperation(value = "普通url跳转小程序配置", httpMethod = "GET")
    @GetMapping("/{key}")
    @AuthIgnore
    public String getMiniUrl(@PathVariable("key") String key) {
        return wxOutLinkConfig.getValue(key);
    }

}