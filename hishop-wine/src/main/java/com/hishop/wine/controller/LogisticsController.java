package com.hishop.wine.controller;

import com.alibaba.fastjson.JSONObject;
import com.hishop.common.pojo.IdPO;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.biz.LogisticsBiz;
import com.hishop.wine.model.po.logistics.LogisticsQueryPO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Getter;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date : 2023/8/25
 */
@Api(value = "LogisticsController", tags = "【物流接口】")
@RestController
@RequestMapping("/logistics")
public class LogisticsController {

    @Resource
    private LogisticsBiz logisticsBiz;

    @ApiOperation(value = "物流查询", httpMethod = "POST")
    @PostMapping("/query")
    public ResponseBean<JSONObject> query(@Valid @RequestBody LogisticsQueryPO queryPO) {
        return ResponseBean.success(logisticsBiz.query(queryPO));
    }

    @ApiOperation(value = "查询余额", httpMethod = "GET")
    @GetMapping("/getBalance")
    public ResponseBean<Integer> query() {
        return ResponseBean.success(logisticsBiz.getBalance());
    }

}
