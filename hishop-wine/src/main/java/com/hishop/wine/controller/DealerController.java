package com.hishop.wine.controller;

import com.hishop.common.pojo.IdBatchPO;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.wine.biz.DealerBiz;
import com.hishop.wine.model.po.dealer.DealerQueryPo;
import com.hishop.wine.model.po.dealer.DealerSavePo;
import com.hishop.wine.model.po.dealer.DealerUpdateStatusPo;
import com.hishop.wine.model.vo.dealer.DealerDetailVo;
import com.hishop.wine.model.vo.dealer.DealerVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * @description: 经销商控制器
 * @author: chenzw
 * @date: 2024/7/5 14:49
 */
@Api(value = "DealerController", tags = "【经销商控制器】")
@RestController
@RequestMapping("/dealer")
@RequiredArgsConstructor
public class DealerController {

    private final DealerBiz dealerBiz;

    @ApiOperation(value = "保存经销商", httpMethod = "POST")
    @PostMapping("/save")
    @OperationLog
    public ResponseBean<Void> save(@RequestBody @Validated DealerSavePo dealerSavePo) {
        dealerBiz.saveDealer(dealerSavePo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "批量删除经销商", httpMethod = "POST")
    @PostMapping("/batchDelete")
    @OperationLog
    public ResponseBean<Void> batchDelete(@Valid @RequestBody IdBatchPO<Long> idBatchPo) {
        dealerBiz.batchDelete(idBatchPo.getIds());
        return ResponseBean.success();
    }

    @ApiOperation(value = "批量更新状态", httpMethod = "POST")
    @PostMapping("/batchUpdateStatus")
    @OperationLog
    public ResponseBean<Void> batchUpdateStatus(@Valid @RequestBody DealerUpdateStatusPo updateStatusPo) {
        dealerBiz.batchUpdateStatus(updateStatusPo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "查询经销商分页列表", httpMethod = "POST")
    @PostMapping("/queryPageList")
    @OperationLog
    public ResponseBean<PageResult<DealerVo>> queryPageList(@RequestBody @Validated DealerQueryPo dealerQueryPo) {
        return ResponseBean.success(dealerBiz.pageList(dealerQueryPo));
    }

    @ApiOperation(value = "经销商详情", httpMethod = "GET")
    @GetMapping("/detail")
    @OperationLog
    public ResponseBean<DealerDetailVo> detail(@RequestParam Long id) {
        return ResponseBean.success(dealerBiz.detail(id));
    }

    @ApiOperation(value = "导入经销商", httpMethod = "POST")
    @PostMapping("/fileImport")
    public ResponseBean<Void> fileImport(MultipartFile file) {
        dealerBiz.importDealer(file);
        return ResponseBean.success();
    }

    @ApiOperation(value = "通过code查询经销商列表", httpMethod = "POST")
    @PostMapping("/queryListByCode")
    public ResponseBean<List<DealerDetailVo>> queryListByCode(@RequestBody @Validated IdBatchPO<String> idBatchPo) {
        return ResponseBean.success(dealerBiz.queryListByCode(idBatchPo.getIds()));
    }
}
