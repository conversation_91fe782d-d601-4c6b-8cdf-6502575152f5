package com.hishop.wine.controller;

import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.log.enums.OperationType;
import com.hishop.wine.biz.StoreBarBiz;
import com.hishop.wine.model.po.storebar.StoreBarPO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@Api(value = "StoreBarController",tags="底部导航相关接口" )
@RestController
@RequestMapping("/storebar")
public class StoreBarController {

    @Resource
    private StoreBarBiz storeBarBiz;

    @ApiOperation("PC-创建底部导航-小程序独立设置")
    @PostMapping(path = "/pc/xcx/create")
    @OperationLog
    public ResponseBean<Boolean> create(@RequestBody @Valid StoreBarPO storeBarPO) {
        boolean result= storeBarBiz.create(storeBarPO,true);
        return ResponseBean.success(result);
    }

    @ApiOperation("PC-创建底部导航-从模块设置")
    @PostMapping(path = "/pc/module/create")
    @OperationLog
    public ResponseBean<Boolean> createByModule(@RequestBody @Valid StoreBarPO storeBarPO) {
        boolean result= storeBarBiz.create(storeBarPO,false);
        return ResponseBean.success(result);
    }

    @ApiOperation("PC-修改底部导航")
    @PostMapping(path = "/pc/update")
    @OperationLog(operationType = OperationType.CREATE)
    public ResponseBean<Boolean> update(@RequestBody @Valid StoreBarPO storeBarPO) {
        boolean result= storeBarBiz.update(storeBarPO);
        return ResponseBean.success(result);
    }
}
