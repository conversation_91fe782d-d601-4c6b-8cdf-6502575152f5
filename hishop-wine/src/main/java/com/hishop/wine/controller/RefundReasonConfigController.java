package com.hishop.wine.controller;

import com.hishop.common.pojo.IdPO;
import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.log.enums.OperationType;
import com.hishop.wine.biz.RefundReasonConfigBiz;
import com.hishop.wine.model.po.RefundReasonCreatePO;
import com.hishop.wine.model.vo.refund.RefundReasonConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
* 退款原因配置表 相关接口
* @author: chenpeng
* @date: 2023-07-08
*/

@Api(value = "RefundReasonConfigController",tags="退款原因配置表相关接口" )
@RestController
@RequestMapping("/refundReasonConfig")
public class RefundReasonConfigController {

    @Resource
    private RefundReasonConfigBiz refundReasonConfigBiz;

    @ApiOperation(value = "mini-获取退款原因列表", httpMethod = "GET")
    @GetMapping("/mini/list")
    public ResponseBean<List<RefundReasonConfigVO>> list(@RequestParam("refundType") Integer refundType) {
        return ResponseBean.success(refundReasonConfigBiz.list(refundType));
    }

    @ApiOperation(value = "PC-获取退款原因列表", httpMethod = "POST")
    @PostMapping("/pc/list")
    public ResponseBean<List<RefundReasonConfigVO>> listForPC() {
        return ResponseBean.success(refundReasonConfigBiz.listForPC());
    }

    @ApiOperation(value = "PC-新增/修改退款原因", httpMethod = "POST")
    @PostMapping("/pc/saveOrUpdate")
    @OperationLog(operationType = OperationType.UPDATE, queryMethod = "get")
    public ResponseBean<Void> saveOrUpdate(@Validated @RequestBody RefundReasonCreatePO createPO) {
        refundReasonConfigBiz.saveOrUpdate(createPO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-查看退款原因", httpMethod = "GET")
    @GetMapping("/pc/get")
    public ResponseBean<RefundReasonConfigVO> get(@RequestParam("id") Long id) {
        return ResponseBean.success(refundReasonConfigBiz.get(id));
    }

    @ApiOperation(value = "PC-删除退款原因", httpMethod = "POST")
    @PostMapping("/pc/delete")
    @OperationLog(queryMethod = "get")
    public ResponseBean<Void> delete(@RequestBody IdPO<Long> idPO) {
        refundReasonConfigBiz.delete(idPO.getId());
        return ResponseBean.success();
    }


}