package com.hishop.wine.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONUtil;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.common.utils.XssUtils;
import com.hishop.log.annotation.OperationLog;
import com.hishop.log.enums.OperationType;
import com.hishop.wine.biz.BasicSettingBiz;
import com.hishop.wine.enums.BasicSettingEnum;
import com.hishop.wine.model.po.setting.*;
import com.hishop.wine.model.vo.logistics.LogisticsSettingVO;
import com.hishop.wine.model.vo.setting.ContentReviewSettingVO;
import com.hishop.wine.model.vo.setting.ProtocolSettingVO;
import com.hishop.wine.model.vo.setting.SmsSettingVO;
import com.hishop.wine.model.vo.setting.SystemSettingVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
* 公共设置表 相关接口
* @author: HuBiao
* @date: 2023-07-12
*/

@Api(value = "BasicSettingController",tags="【PC-公共配置】" )
@RestController
@RequestMapping("/basicSetting")
public class BasicSettingController {

    @Resource
    private BasicSettingBiz basicSettingBiz;

    @ApiOperation(value = "保存短信配置", httpMethod = "POST")
    @PostMapping("/saveSmsSetting")
    @OperationLog(queryMethod = "getSmsSetting", operationType = OperationType.UPDATE, noPrimaryKey = true)
    public ResponseBean<Void> saveSmsSetting(@Valid @RequestBody SaveSmsSettingPO smsSettingPO) {
        smsSettingPO.check();
        basicSettingBiz.saveSetting(BasicSettingEnum.SMS_SETTING, JSONUtil.toJsonStr(smsSettingPO));
        return ResponseBean.success();
    }

    @ApiOperation(value = "查询短信配置", httpMethod = "GET")
    @GetMapping("/getSmsSetting")
    public ResponseBean<SmsSettingVO> getSmsSetting() {
        return ResponseBean.success(basicSettingBiz.getSetting(BasicSettingEnum.SMS_SETTING));
    }

    @ApiOperation(value = "保存系统配置", httpMethod = "POST")
    @PostMapping("/saveSystemSetting")
    @OperationLog(queryMethod = "getSystemSetting", operationType = OperationType.UPDATE, noPrimaryKey = true)
    public ResponseBean<Void> saveSystemSetting(@Valid @RequestBody SaveSystemSettingPO systemSettingPO) {
        systemSettingPO.check();
        basicSettingBiz.saveSystemSetting(systemSettingPO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "查询系统配置", httpMethod = "GET")
    @GetMapping("/getSystemSetting")
    public ResponseBean<SystemSettingVO> getSystemSetting() {
        return ResponseBean.success(basicSettingBiz.getSetting(BasicSettingEnum.SYSTEM_SETTING));
    }

    @ApiOperation(value = "保存物流配置", httpMethod = "POST")
    @PostMapping("/saveLogisticsSetting")
    @OperationLog(queryMethod = "getLogisticsSetting", operationType = OperationType.UPDATE, noPrimaryKey = true)
    public ResponseBean<Void> saveLogisticsSetting(@Valid @RequestBody LogisticsSettingPO logisticsSettingPO) {
        logisticsSettingPO.check();
        basicSettingBiz.saveSetting(BasicSettingEnum.LOGISTICS_SETTING, JSONUtil.toJsonStr(logisticsSettingPO));
        return ResponseBean.success();
    }

    @ApiOperation(value = "查询物流配置", httpMethod = "GET")
    @GetMapping("/getLogisticsSetting")
    public ResponseBean<LogisticsSettingVO> getLogisticsSetting() {
        return ResponseBean.success(basicSettingBiz.getSetting(BasicSettingEnum.LOGISTICS_SETTING));
    }

    @ApiOperation(value = "保存内容审核配置", httpMethod = "POST")
    @PostMapping("/saveContentReviewSetting")
    @OperationLog(queryMethod = "getContentReviewSetting", operationType = OperationType.UPDATE, noPrimaryKey = true)
    public ResponseBean<Void> saveContentReviewSetting(@Valid @RequestBody ContentReviewSettingPO contentReviewSettingPO) {
        contentReviewSettingPO.check();
        basicSettingBiz.saveSetting(BasicSettingEnum.CONTENT_REVIEW_SETTING, JSONUtil.toJsonStr(contentReviewSettingPO));
        return ResponseBean.success();
    }

    @ApiOperation(value = "查询内容审核配置", httpMethod = "GET")
    @GetMapping("/getContentReviewSetting")
    public ResponseBean<ContentReviewSettingVO> getContentReviewSetting() {
        return ResponseBean.success(basicSettingBiz.getSetting(BasicSettingEnum.CONTENT_REVIEW_SETTING));
    }

    @ApiOperation(value = "公共获取配置", httpMethod = "GET", hidden = true)
    @GetMapping("/getSetting")
    public ResponseBean<Object> getSetting(@RequestParam BasicSettingEnum settingEnum) {
        return ResponseBean.success(basicSettingBiz.getSetting(settingEnum));
    }

    @ApiOperation(value = "保存隐私设置", httpMethod = "POST")
    @PostMapping("/saveProtocolSetting")
    @OperationLog(queryMethod = "getProtocolSetting", operationType = OperationType.UPDATE, noPrimaryKey = true)
    public ResponseBean<Void> saveProtocolSetting(@Valid @RequestBody ProtocolSettingPO protocolSettingPO) {
        // XSS 清理用户协议和隐私政策内容
        String cleanedUserAgreement = XssUtils.cleanRichText(protocolSettingPO.getUserAgreement());
        String cleanedPrivacyPolicy = XssUtils.cleanRichText(protocolSettingPO.getPrivacyPolicy());

        protocolSettingPO.setUserAgreement(Base64.encode(cleanedUserAgreement));
        protocolSettingPO.setPrivacyPolicy(Base64.encode(cleanedPrivacyPolicy));
        basicSettingBiz.saveSetting(BasicSettingEnum.PROTOCOL_SETTING, JSONUtil.toJsonStr(protocolSettingPO));
        return ResponseBean.success();
    }

    @ApiOperation(value = "获取隐私设置", httpMethod = "GET")
    @GetMapping("/getProtocolSetting")
    public ResponseBean<ProtocolSettingVO> getProtocolSetting() {
        ProtocolSettingVO settingVO = basicSettingBiz.getSetting(BasicSettingEnum.PROTOCOL_SETTING);
        settingVO.setUserAgreement(Base64.decodeStr(settingVO.getUserAgreement()));
        settingVO.setPrivacyPolicy(Base64.decodeStr(settingVO.getPrivacyPolicy()));
        return ResponseBean.success(settingVO);
    }
}
