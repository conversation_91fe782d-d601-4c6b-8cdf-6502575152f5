package com.hishop.wine.controller;

import com.hishop.common.pojo.IdPO;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.log.enums.OperationType;
import com.hishop.wine.biz.FileImportRecordBiz;
import com.hishop.wine.model.po.fileImport.FileImportRecordQueryPo;
import com.hishop.wine.model.vo.fileImport.FileImportRecordVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * 文件导入记录相关接口
 *
 * @author: LiGuoQiang
 * @date: 2023-07-10
 */

@Api(value = "FileImportRecordController", tags = "文件导入记录相关接口")
@RestController
@RequestMapping("/import/record")
public class FileImportRecordController {

    @Resource
    private FileImportRecordBiz fileImportRecordBiz;

    @ApiOperation(value = "删除导入记录", httpMethod = "POST")
    @PostMapping("/pc/delete")
    @OperationLog(operationType = OperationType.DELETE)
    public ResponseBean<Void> delete(@RequestBody IdPO<Long> idPo) {
        fileImportRecordBiz.deleteById(idPo.getId());
        return ResponseBean.success();
    }

    @ApiOperation(value = "分页获取导入记录", httpMethod = "POST")
    @PostMapping("/pc/pageList")
    public ResponseBean<PageResult<FileImportRecordVo>> pageList(@RequestBody FileImportRecordQueryPo queryPo) {
        return ResponseBean.success(fileImportRecordBiz.pageList(queryPo));
    }

    @ApiOperation(value = "分页获取物流码导入记录", httpMethod = "POST")
    @PostMapping("/pc/pageLogisticsList")
    public ResponseBean<PageResult<FileImportRecordVo>> pageLogisticsList(@RequestBody FileImportRecordQueryPo queryPo) {
        return ResponseBean.success(fileImportRecordBiz.pageLogisticsList(queryPo));
    }

    @ApiOperation(value = "导入物流码", httpMethod = "POST")
    @PostMapping("/fileImportLogisticsCode")
    public ResponseBean<Void> fileImportLogisticsCode(@RequestParam("file") MultipartFile file,
                                         @RequestParam("name") String name) {
        fileImportRecordBiz.fileImportLogisticsCode(file, name);
        return ResponseBean.success();
    }

    @ApiOperation(value = "导入扫码营销物流码", httpMethod = "POST")
    @PostMapping("/fileImportLogisticsCodeScan")
    public ResponseBean<Void> fileImportLogisticsCodeScan(@RequestParam("file") MultipartFile file,
                                                      @RequestParam("name") String name) {
        fileImportRecordBiz.fileImportLogisticsCodeScan(file, name);
        return ResponseBean.success();
    }

    @ApiOperation(value = "查询批次名称是否重复", httpMethod = "GET")
    @GetMapping("/queryNameIsRepeat")
    public ResponseBean<Boolean> queryNameIsRepeat(@RequestParam("name") String name) {
        return ResponseBean.success(fileImportRecordBiz.queryNameIsRepeat(name));
    }

    @ApiOperation(value = "根据产品编码分页获取物流码导入记录", httpMethod = "POST")
    @PostMapping("/pc/queryFileImportCodePageList")
    public ResponseBean<PageResult<FileImportRecordVo>> queryFileImportCodePageList(@RequestBody FileImportRecordQueryPo queryPo) {
        return ResponseBean.success(fileImportRecordBiz.queryFileImportCodePageList(queryPo));
    }

    @ApiOperation(value = "分页获取物流码导入记录", httpMethod = "POST")
    @PostMapping("/pc/queryLogisticsCodePageList")
    public ResponseBean<PageResult<FileImportRecordVo>> queryLogisticsCodePageList(@RequestBody FileImportRecordQueryPo queryPo) {
        return ResponseBean.success(fileImportRecordBiz.queryFileImportCodePageList(queryPo));
    }
}