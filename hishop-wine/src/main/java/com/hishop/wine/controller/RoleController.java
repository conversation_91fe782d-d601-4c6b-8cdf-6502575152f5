package com.hishop.wine.controller;

import com.hishop.log.annotation.OperationLog;
import com.hishop.wine.model.vo.basic.RoleCacheVO;
import com.hishop.wine.model.vo.basic.RoleVO;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hishop.wine.model.po.basic.RoleCreatePO;
import com.hishop.wine.model.po.basic.RoleUpdatePO;
import com.hishop.wine.biz.RoleBiz;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

import com.hishop.common.response.ResponseBean;
import com.hishop.common.pojo.IdPO;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 角色表 相关接口
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Api(value = "RoleController", tags = "【角色管理】")
@RestController
@RequestMapping("/role")
public class RoleController {

    @Resource
    private RoleBiz roleBiz;

    @ApiOperation(value = "PC-新增角色", httpMethod = "POST")
    @PostMapping({"/pc/create"})
    @OperationLog
    public ResponseBean create(@Valid @RequestBody RoleCreatePO roleCreatePO) {
        roleBiz.create(roleCreatePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-编辑角色", httpMethod = "POST")
    @PostMapping({"/pc/update"})
    @OperationLog
    public ResponseBean update(@Valid @RequestBody RoleUpdatePO roleUpdatePO) {
        roleBiz.update(roleUpdatePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-删除角色", httpMethod = "POST")
    @PostMapping({"/pc/delete"})
    @OperationLog
    public ResponseBean delete(@RequestBody IdPO<Long> idPO) {
        roleBiz.deleteById(idPO.getId());
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-查询角色详情", httpMethod = "GET")
    @GetMapping({"/pc/detail"})
    public ResponseBean<RoleVO> detail(@RequestParam("id") Long id) {
        return ResponseBean.success(roleBiz.detail(id));
    }

    @ApiOperation(value = "PC-获取所有角色列表", httpMethod = "GET")
    @GetMapping({"/pc/listAll"})
    public ResponseBean<List<RoleCacheVO>> listAll() {
        return ResponseBean.success(roleBiz.listAll());
    }


}