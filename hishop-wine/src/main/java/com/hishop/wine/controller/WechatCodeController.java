package com.hishop.wine.controller;

import com.hishop.common.response.ResponseBean;
import com.hishop.wine.biz.WechatCodeBiz;
import com.hishop.wine.common.annotation.SwitchWxMini;
import com.hishop.wine.model.po.wechat.WechatCodeCreatePO;
import com.hishop.wine.model.po.wechat.WechatSavePO;
import com.hishop.wine.model.vo.wechat.WechatCodeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2023/6/26
 */
@RestController
@RequestMapping("/wechat/code")
@Api(value = "WechatCodeController", tags = "微信二维码相关接口")
public class WechatCodeController {

    @Resource
    private WechatCodeBiz wechatCodeBiz;

    /**
     * 保存微信二维码，主要是为了不每次生成上传等
     * <AUTHOR>
     * @date 2023/6/26
     */
    @ApiOperation(value = "保存微信二维码，主要是为了不每次生成上传等", httpMethod = "POST")
    @PostMapping("/save")
    public ResponseBean<Void> saveWechatCode(@RequestBody @Valid WechatSavePO savePO) {
        wechatCodeBiz.save(savePO);
        return ResponseBean.success();
    }

    /**
     * 根据key获取微信二维码信息
     * @param codeKey 二维码唯一key
     * @return 微信二维码对象
     */
    @GetMapping("/getByKey")
    public ResponseBean<WechatCodeVO> getByKey(@RequestParam(name = "codeKey") String codeKey) {
        WechatCodeVO vo = wechatCodeBiz.qryByKey(codeKey);
        return ResponseBean.success(vo);
    }

    @ApiOperation(value = "生成小程序二维码(ma=mini-app)", httpMethod = "POST")
    @PostMapping("/genMaCode")
    public ResponseBean<String> genMaCode(@RequestBody @Valid WechatCodeCreatePO createPo) {
        String path = wechatCodeBiz.genMaCode(createPo);
        return ResponseBean.success(path);
    }

    @ApiOperation(value = "获取小程序地址(带参数)", httpMethod = "GET")
    @GetMapping("/getRealPage")
    public ResponseBean<String> getRealPage(@RequestParam(name = "scene") String scene) {
        return ResponseBean.success(wechatCodeBiz.getRealPage(scene));
    }

    @ApiOperation(value = "生成宴席门店小程序二维码(ma=mini-app)", httpMethod = "POST")
    @PostMapping("/genFeastMaCode")
    public ResponseBean<String> genFeastMaCode(@RequestBody @Valid WechatCodeCreatePO createPo) {
        String path = wechatCodeBiz.genFeastMaCode(createPo);
        return ResponseBean.success(path);
    }

    @ApiOperation(value = "生成小程序二维码", httpMethod = "POST")
    @PostMapping("/getWxacode")
    public ResponseBean<String> getWxacode(@RequestBody @Valid WechatCodeCreatePO createPo) {
        String path = wechatCodeBiz.getWxacode(createPo);
        return ResponseBean.success(path);
    }

    @ApiOperation(value = "生成小程序二维码(ma=mini-app)", httpMethod = "POST")
    @PostMapping("/genMaCodeByModule")
    public ResponseBean<String> genMaCodeByModule(@RequestBody @Valid WechatCodeCreatePO createPo) {
        String path = wechatCodeBiz.genMaCodeByModule(createPo);
        return ResponseBean.success(path);
    }

    @ApiOperation(value = "根据业务路径生成小程序二维码", httpMethod = "POST")
    @PostMapping("/getWxacodeByBizPath")
    public ResponseBean<String> getWxacodeByBizPath(@RequestBody @Valid WechatCodeCreatePO createPo) {
        String path = wechatCodeBiz.getWxacodeByBizPath(createPo);
        return ResponseBean.success(path);
    }

}
