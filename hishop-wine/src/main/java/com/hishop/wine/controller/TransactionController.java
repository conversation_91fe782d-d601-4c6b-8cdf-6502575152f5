package com.hishop.wine.controller;

import com.hishop.common.annotation.AuthIgnore;
import com.hishop.common.annotation.RejectRepeat;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.api.TransactionFeign;
import com.hishop.wine.model.po.transaction.*;
import com.hishop.wine.model.vo.transaction.*;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import com.hishop.wine.biz.TransactionBiz;

import javax.annotation.Resource;
import javax.validation.Valid;

import io.swagger.annotations.Api;

/**
 * 交易流水表 相关接口
 *
 * @author: HuBiao
 * @date: 2023-06-28
 */
@Api(value = "TransactionController", tags = "【交易模块】")
@RestController
@RequestMapping("/transaction")
public class TransactionController implements TransactionFeign {

    @Resource
    private TransactionBiz transactionBiz;

    @ApiOperation("发起支付")
    @PostMapping("/pay")
    @Override
    @RejectRepeat
    public ResponseBean<TransactionPayVO> pay(@RequestBody @Valid TransactionPayPO payPO) {
        return ResponseBean.success(transactionBiz.pay(payPO));
    }

    @ApiOperation("判断是否支付中")
    @PostMapping("/queryPaying")
    @Override
    @RejectRepeat
    public ResponseBean<Boolean> queryPaying(@RequestBody @Valid TransactionQueryPO payPO) {
        return ResponseBean.success(transactionBiz.queryPaying(payPO));
    }

    @ApiOperation(value = "支付回调")
    @PostMapping("/notify/pay/{moduleCode}")
    @AuthIgnore
    public String notifyPay(@PathVariable("moduleCode") String moduleCode, @RequestBody String notifyData) {
        return transactionBiz.notifyPay(moduleCode, notifyData);
    }

    @ApiOperation("发起退款")
    @PostMapping("/refund")
    @Override
    @RejectRepeat
    public ResponseBean<TransactionRefundVO> refund(@RequestBody @Valid TransactionRefundPO refundPO) {
        return ResponseBean.success(transactionBiz.refund(refundPO));
    }

    @ApiOperation(value = "退款回调")
    @PostMapping("/notify/refund/{moduleCode}")
    @RejectRepeat
    @AuthIgnore
    public String notifyRefund(@PathVariable("moduleCode") String moduleCode, @RequestBody String notifyData) {
        return transactionBiz.notifyRefund(moduleCode, notifyData);
    }

    @ApiOperation("企业付款到零钱")
    @PostMapping("/entPay")
    @Override
    public ResponseBean<TransactionEntPayVO> entPay(@RequestBody @Valid TransactionEntPayPO entPayPO) {
        return ResponseBean.success(transactionBiz.entPay(entPayPO));
    }

    @ApiOperation("交易查询")
    @PostMapping("/query")
    @Override
    public ResponseBean<PageResult<TransactionInfoVO>> queryTransaction(@RequestBody @Valid TransactionQueryPO queryPO) {
        return ResponseBean.success(transactionBiz.queryTransaction(queryPO));
    }
    @ApiOperation("交易保存")
    @PostMapping("/save")
    @Override
    public ResponseBean<Void> save(TransactionPO transactionPO) {
        transactionBiz.save(transactionPO);
        return ResponseBean.success();
    }

    @ApiOperation("查询交易中与交易成功的数据（只能有一条）")
    @PostMapping("/getTransaction")
    @Override
    @RejectRepeat
    public ResponseBean<TransactionVO> getTransaction(@RequestBody @Valid TransactionQueryPO payPO) {
        return ResponseBean.success(transactionBiz.getTransaction(payPO));
    }

    @ApiOperation("更新交易状态")
    @PostMapping("/updateTransactionStatus")
    @RejectRepeat
    @Override
    public ResponseBean<Boolean> updateTransactionStatus(@RequestBody @Valid TransactionUpdateStatusPO po) {
        return ResponseBean.success(transactionBiz.updateTransactionStatus(po));
    }

    @ApiOperation("按交易流水查交易")
    @PostMapping("/getTransactionByNo")
    @Override
    @RejectRepeat
    public ResponseBean<TransactionVO> getTransactionByNo(@RequestBody @Valid TransactionQueryPO payPO) {
        return ResponseBean.success(transactionBiz.getTransactionByNo(payPO));
    }
    @ApiOperation("按id查交易")
    @PostMapping("/getTransactionById")
    @Override
    @RejectRepeat
    public ResponseBean<TransactionVO> getTransactionById(@RequestBody @Valid TransactionQueryPO payPO) {
        return ResponseBean.success(transactionBiz.getTransactionById(payPO));
    }
}