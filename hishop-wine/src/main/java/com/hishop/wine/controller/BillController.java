package com.hishop.wine.controller;

import cn.hutool.core.lang.Assert;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.biz.BillBiz;
import com.hishop.wine.model.dto.bill.BillCountDto;
import com.hishop.wine.model.po.bill.BillExportPo;
import com.hishop.wine.model.po.bill.BillQueryPo;
import com.hishop.wine.model.vo.bill.BillCountVo;
import com.hishop.wine.model.vo.bill.BillGroupVo;
import com.hishop.wine.model.vo.bill.BillVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;
import java.util.List;

/**
 * @description: 对账管理
 * @author: guoyufeng
 * @date: 2024/1/30 9:16
 */
@Api(value = "BillController",tags="【对账管理】" )
@RestController
@RequestMapping("/bill")
@RequiredArgsConstructor
public class BillController {

    private final BillBiz billBiz;

    /**
     * 明细分页查询
     * @param billQueryPo 入参
     * @return 分页结果
     */
    @ApiOperation(value = "明细分页查询", httpMethod = "POST")
    @PostMapping("/pageList")
    public ResponseBean<PageResult<BillVo>> pageList(@Validated @RequestBody BillQueryPo billQueryPo) {
        //验证请求参数
        billQueryPo.validateParam();
        return ResponseBean.success(billBiz.pageList(billQueryPo));
    }

    /**
     * 明细总收入
     * @param billQueryPo 入参
     * @return 总收入
     */
    @ApiOperation(value = "明细总收入", httpMethod = "POST")
    @PostMapping("/pageListCount")
    public ResponseBean<BillCountVo> pageListCount(@Validated @RequestBody BillQueryPo billQueryPo) {
        //验证请求参数
        billQueryPo.validateParam();
        return ResponseBean.success(billBiz.pageListCount(billQueryPo));
    }

    /**
     * 月统计分页查询
     * @param billQueryPo 入参
     * @return 分页结果
     */
    @ApiOperation(value = "月统计分页查询", httpMethod = "POST")
    @PostMapping("/monthPageList")
    public ResponseBean<PageResult<BillGroupVo>> monthPageList(@Validated @RequestBody BillQueryPo billQueryPo) {
        //验证请求参数
        billQueryPo.validateParam();
        return ResponseBean.success(billBiz.monthPageList(billQueryPo));
    }

    /**
     * 月统计总收入
     * @param billQueryPo 入参
     * @return 总收入
     */
    @ApiOperation(value = "月统计总收入", httpMethod = "POST")
    @PostMapping("/monthPageListCount")
    public ResponseBean<BillCountVo> monthPageListCount(@Validated @RequestBody BillQueryPo billQueryPo) {
        //验证请求参数
        billQueryPo.validateParam();
        return ResponseBean.success(billBiz.monthPageListCount(billQueryPo));
    }

    /**
     * 日统计分页查询
     * @param billQueryPo 入参
     * @return 分页结果
     */
    @ApiOperation(value = "日统计分页查询", httpMethod = "POST")
    @PostMapping("/dayPageList")
    public ResponseBean<PageResult<BillGroupVo>> dayPageList(@Validated @RequestBody BillQueryPo billQueryPo) {
        //验证请求参数
        billQueryPo.validateParam();
        return ResponseBean.success(billBiz.dayPageList(billQueryPo));
    }

    /**
     * 日统计总收入
     * @param billQueryPo 入参
     * @return 总收入
     */
    @ApiOperation(value = "日统计总收入", httpMethod = "POST")
    @PostMapping("/dayPageListCount")
    public ResponseBean<BillCountVo> dayPageListCount(@Validated @RequestBody BillQueryPo billQueryPo) {
        //验证请求参数
        billQueryPo.validateParam();
        return ResponseBean.success(billBiz.dayPageListCount(billQueryPo));
    }

    /**
     * 账单导出
     * @param pagePO 入参
     * @return 导出结果
     */
    @ApiOperation(value = "账单导出查询数据", httpMethod = "POST")
    @PostMapping("/exportList")
    public ResponseBean<List<BillVo>> exportList(@RequestBody @Valid BillExportPo pagePO) {
        if(pagePO.getDay() == null && pagePO.getMonth() == null) {
            Assert.isTrue(false, "月份和天必须传一个");
        }
        return ResponseBean.success(billBiz.exportList(pagePO));
    }


}
