package com.hishop.wine.controller;

import cn.hutool.core.bean.BeanUtil;
import com.hishop.log.annotation.OperationLog;
import com.hishop.log.enums.OperationType;
import com.hishop.wine.model.po.product.ProductCategoryStatusUpdatePO;
import com.hishop.wine.model.vo.product.ProductCategoryCacheVO;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hishop.wine.model.po.product.ProductCategoryCreatePO;
import com.hishop.wine.model.po.product.ProductCategoryUpdatePO;
import com.hishop.wine.model.po.product.ProductCategoryQueryPO;
import com.hishop.wine.model.vo.product.ProductCategoryVO;
import com.hishop.wine.biz.ProductCategoryBiz;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

import com.hishop.common.response.ResponseBean;
import com.hishop.common.pojo.IdPO;
import com.hishop.common.response.PageResult;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 产品分类表 相关接口
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
@Api(value = "ProductCategoryController", tags = "【产品分类管理】")
@RestController
@RequestMapping("/productCategory")
public class ProductCategoryController {

    @Resource
    private ProductCategoryBiz productCategoryBiz;

    @ApiOperation(value = "PC-新增产品分类", httpMethod = "POST")
    @PostMapping("/pc/create")
    @OperationLog
    public ResponseBean create(@Valid @RequestBody ProductCategoryCreatePO productCategoryCreatePO) {
        productCategoryBiz.create(productCategoryCreatePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-编辑产品分类", httpMethod = "POST")
    @PostMapping("/pc/update")
    @OperationLog
    public ResponseBean update(@Valid @RequestBody ProductCategoryUpdatePO productCategoryUpdatePO) {
        productCategoryBiz.update(productCategoryUpdatePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-修改产品分类状态", httpMethod = "POST")
    @PostMapping("/pc/status")
    @OperationLog
    public ResponseBean update(@Valid @RequestBody ProductCategoryStatusUpdatePO productCategoryStatusUpdatePO) {
        productCategoryBiz.update(BeanUtil.copyProperties(productCategoryStatusUpdatePO, ProductCategoryUpdatePO.class));
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-删除产品分类", httpMethod = "POST")
    @PostMapping("/pc/delete")
    @OperationLog(operationType = OperationType.DELETE)
    public ResponseBean delete(@Valid @RequestBody IdPO<Long> idPO) {
        productCategoryBiz.deleteById(idPO.getId());
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-查询分类详情", httpMethod = "GET")
    @GetMapping("/pc/detail")
    public ResponseBean<ProductCategoryVO> detail(@RequestParam(name = "id") Long id) {
        ProductCategoryVO detail = productCategoryBiz.detail(id);
        return ResponseBean.success(detail);
    }

    @ApiOperation(value = "PC-获取所有产品分类下拉框", httpMethod = "GET")
    @GetMapping("/pc/listForSelect")
    public ResponseBean<List<ProductCategoryCacheVO>> listAll() {
        return ResponseBean.success(productCategoryBiz.listAll());
    }

    @ApiOperation(value = "PC-分页获取产品列表", httpMethod = "POST")
    @PostMapping("/pc/pageList")
    public ResponseBean<PageResult<ProductCategoryVO>> pageList(@Valid @RequestBody ProductCategoryQueryPO pagePO) {
        return ResponseBean.success(productCategoryBiz.pageList(pagePO));
    }

}