package com.hishop.wine.controller;

import com.hishop.common.annotation.AuthIgnore;
import com.hishop.common.enums.IdentityTypeEnums;
import com.hishop.common.pojo.login.LoginResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.wine.api.UserFeign;
import com.hishop.wine.biz.UserBiz;
import com.hishop.wine.biz.WechatWebUserBiz;
import com.hishop.wine.model.po.basic.*;
import com.hishop.wine.model.po.login.UserPcPwdLoginPO;
import com.hishop.wine.model.po.login.UserRefreshTokenPO;
import com.hishop.wine.model.po.login.UserWxScanLoginPO;
import com.hishop.wine.model.po.basic.WechatBindPO;
import com.hishop.wine.model.po.user.*;
import com.hishop.wine.model.vo.basic.PcAccountInfoVO;
import com.hishop.wine.model.vo.basic.UserResourceVO;
import com.hishop.wine.model.vo.basic.UserVO;
import com.hishop.wine.model.vo.login.PcLoginVO;
import com.hishop.wine.model.vo.user.PullNewSummaryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


/**
 * 用户表 相关接口
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Api(value = "UserController", tags = "【PC用户管理】")
@RestController
@RequestMapping("/user")
public class UserController implements UserFeign {

    @Resource
    private UserBiz userBiz;
    @Resource
    private WechatWebUserBiz wechatWebUserBiz;

    @ApiOperation("PC-登录")
    @PostMapping("/pc/login")
    @AuthIgnore
    public ResponseBean<PcLoginVO> pcLogin(@RequestBody @Valid UserPcPwdLoginPO loginParam) {
        loginParam.setIdentityType(IdentityTypeEnums.ADMIN.getType());
        return ResponseBean.success(userBiz.pcLogin(loginParam.getAccount(), loginParam.getPassword(), loginParam.getIdentityType(), Boolean.TRUE, loginParam.getRememberMe()));
    }

    @ApiOperation("PC-微信扫码登录")
    @PostMapping("/pc/wxScanLogin")
    @AuthIgnore
    public ResponseBean<PcLoginVO> wxScanLogin(@RequestBody @Valid UserWxScanLoginPO wechatWebScanPO) {
        wechatWebScanPO.setIdentityType(IdentityTypeEnums.ADMIN.getType());
        return ResponseBean.success(userBiz.pcWxScanLogin(wechatWebScanPO.getCode(), wechatWebScanPO.getState(), wechatWebScanPO.getIdentityType()));
    }

    @ApiOperation("PC-登出")
    @GetMapping("/pc/logout")
    public ResponseBean<LoginResult> pcLogout() {
        userBiz.logout();
        return ResponseBean.success();
    }

    @ApiOperation("PC-刷新token")
    @PostMapping("/pc/refreshToken")
    @AuthIgnore
    public ResponseBean<PcLoginVO> pcRefreshToken(@RequestBody @Valid UserRefreshTokenPO refreshTokenParam) {
        return ResponseBean.success(userBiz.pcRefreshToken(refreshTokenParam));
    }

    @Override
    @ApiOperation(value = "fegin-根据用户ID列表获取用户列表", hidden = true)
    @PostMapping("/fegin/listUserByIds")
    public ResponseBean<List<UserVO>> listUserByIds(@RequestBody List<Long> userIds) {
        return ResponseBean.success(userBiz.listUserByIds(userIds, null));
    }

    @Override
    @ApiOperation(value = "fegin-根据用户ID获取用户", hidden = true)
    @GetMapping("/fegin/userById")
    public ResponseBean<UserVO> userById(@RequestParam("userId") Long userId) {
        return ResponseBean.success(userBiz.userById(userId));
    }

    /**
     * 根据条件关联组合查询用户ID，可能会关联用户+身份+小程序用户
     * <AUTHOR>
     * @date 2023/6/30
     */
    @ApiOperation(value = "feign-根据条件关联组合查询用户ID，可能会关联用户+身份+小程序用户", hidden = true)
    @PostMapping("/combine/qryUserId")
    @Override
    public ResponseBean<List<Long>> qryUserId(@RequestBody UserCombineQryPO qryPo) {
        return ResponseBean.success(userBiz.qryUserId(qryPo));
    }

    @ApiOperation(value = "PC-获取账号信息", httpMethod = "GET")
    @GetMapping("/pc/getAccountInfo")
    public ResponseBean<PcAccountInfoVO> getAccountInfo() {
        return ResponseBean.success(userBiz.getPcAccountInfo());
    }

    @ApiOperation(value = "PC-更新账号信息", httpMethod = "POST")
    @PostMapping("/pc/updateAccountInfo")
    @OperationLog(queryMethod = "getAccountInfo", noPrimaryKey = true)
    public ResponseBean updateAccountInfo(@RequestBody @Valid PcAccountUpdatePO updateAccountPO) {
        userBiz.updateAccountInfo(updateAccountPO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-发送修改密码短信", httpMethod = "POST")
    @PostMapping("/pc/mobile/sendForUpdatePwd")
    public ResponseBean<Void> sendSmsForUpdPwd() {
        userBiz.sendUpdPwdSms();
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-修改密码", httpMethod = "POST")
    @PostMapping("/pc/updatePassword")
    public ResponseBean updatePassword(@RequestBody @Valid PasswordUpdatePO updatePasswordPO) {
        userBiz.updatePassword(updatePasswordPO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-发送忘记密码短信", httpMethod = "POST")
    @PostMapping("/pc/mobile/sendForForgetPwd")
    @AuthIgnore
    public ResponseBean<Void> sendForForgetPwd(@RequestBody @Valid MobileSendPO mobileSendPO) {
        userBiz.sendForForgetPwd(mobileSendPO.getMobile(), mobileSendPO.getIdentityType());
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-忘记密码", httpMethod = "POST")
    @PostMapping("/pc/forgetPassword")
    @AuthIgnore
    public ResponseBean forgetPassword(@RequestBody @Valid PasswordForgetPO passwordForgetPO) {
        userBiz.forgetPassword(passwordForgetPO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-发送邮箱验证码", httpMethod = "POST")
    @PostMapping("/pc/email/sendForBind")
    public ResponseBean<Void> sendBindEmail(@RequestBody @Valid EmailSendPO emailSendPO) {
        userBiz.sendBindEmail(emailSendPO.getEmail());
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-绑定邮箱", httpMethod = "POST")
    @PostMapping("/pc/email/bind")
    public ResponseBean<Void> bindEmail(@RequestBody @Valid EmailBindPO emailBindPO) {
        userBiz.bindEmail(emailBindPO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-解绑邮箱", httpMethod = "POST")
    @PostMapping("/pc/email/unBind")
    public ResponseBean<Void> unBindEmail() {
        userBiz.unBindEmail();
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-发送绑定手机号短信", httpMethod = "POST")
    @PostMapping("/pc/mobile/sendForBind")
    public ResponseBean<Void> sendBindMobile(@RequestBody @Valid MobileSendPO mobileSendPO) {
        userBiz.sendBindMobile(mobileSendPO.getMobile());
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-绑定手机号", httpMethod = "POST")
    @PostMapping("/pc/mobile/bind")
    public ResponseBean<Void> sendBindMobile(@RequestBody @Valid MobileBindPO mobileBindPO) {
        userBiz.bindMobile(mobileBindPO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-解绑手机号", httpMethod = "POST")
    @PostMapping("/pc/mobile/unBind")
    public ResponseBean<Void> unBindMobile() {
        userBiz.unBindMobile();
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-绑定微信", httpMethod = "POST")
    @PostMapping("/pc/wechat/bind")
    public ResponseBean<String> bind(@RequestBody @Valid WechatBindPO wechatBindPO) {
        return ResponseBean.success(wechatWebUserBiz.bind(wechatBindPO.getCode(), wechatBindPO.getState()));
    }

    @ApiOperation(value = "PC-解绑微信", httpMethod = "POST")
    @PostMapping("/pc/wechat/unBind")
    public ResponseBean<Void> bind() {
        wechatWebUserBiz.unBind();
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-查询菜单权限", httpMethod = "GET")
    @GetMapping("/pc/menuAuth")
    public ResponseBean<UserResourceVO> menuAuth() {
        return ResponseBean.success(userBiz.menuAuth());
    }

    @Override
    @ApiOperation(value = "PC-检测是否存在角色", httpMethod = "POST", hidden = true)
    @PostMapping("/pc/checkRole")
    public ResponseBean<Boolean> checkRole(@RequestBody @Valid CheckRolePO checkRolePO) {
        return ResponseBean.success(userBiz.checkRole(checkRolePO));
    }

    @Override
    @ApiOperation(value = "PC-获取拉新数据", httpMethod = "POST")
    @PostMapping("/pc/getPullNewSummary")
    public ResponseBean<List<PullNewSummaryVO>> getPullNewSummary(@RequestBody @Valid PullNewSummaryPO pullNewSummaryPO) {
        return ResponseBean.success(userBiz.getPullNewSummary(pullNewSummaryPO));
    }

    @Override
    @ApiOperation(value = "通过手机号查询，如果不存在则创建", httpMethod = "POST")
    @PostMapping("/feign/getOrCreateUser")
    public ResponseBean<UserVO> getOrCreateUser(UserCreatePO userCreatePO) {
        return ResponseBean.success(userBiz.getOrCreate(userCreatePO.getMobile(), userCreatePO.getUserName()));
    }

    @Override
    @ApiOperation(value = "通过手机号 查询用户", httpMethod = "GET")
    @GetMapping("/feign/getByPhone")
    public ResponseBean<UserVO> getByPhone(@RequestParam("phone") String phone) {
        return ResponseBean.success(userBiz.getByPhone(phone));
    }

    @Override
    @ApiOperation(value = "通过会员等级 查询用户 是否存在", httpMethod = "GET")
    @GetMapping("/feign/existsByGrade")
    public ResponseBean<Boolean> existsByGrade(@RequestParam("gradeId") Long gradeId) {
        return ResponseBean.success(userBiz.existsByGrade(gradeId));
    }

    @Override
    @ApiOperation(value = "修改会员等级", httpMethod = "POST")
    @PostMapping("/feign/updateGrade")
    public ResponseBean<Boolean> updateGrade(@RequestBody UserGradeUpdatePO userGradePO) {
        return ResponseBean.success(userBiz.updateGrade(userGradePO));
    }
}