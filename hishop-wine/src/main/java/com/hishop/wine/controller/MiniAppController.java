package com.hishop.wine.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.excel.util.StringUtils;
import com.hishop.common.annotation.AuthIgnore;
import com.hishop.common.pojo.IdPO;
import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.wine.api.MiniAppFeign;
import com.hishop.wine.biz.MiniAppBiz;
import com.hishop.wine.biz.PaymentSettingBiz;
import com.hishop.wine.model.po.miniApp.MiniAppCreatePO;
import com.hishop.wine.model.po.miniApp.MiniAppUpdatePO;
import com.hishop.wine.model.po.miniApp.MiniAppUpdateSecretPO;
import com.hishop.wine.model.po.miniApp.uploadShippingInfo.UploadShippingInfoPo;
import com.hishop.wine.model.vo.miniApp.HomePageAndStoreBarSelectVO;
import com.hishop.wine.model.vo.miniApp.LinkTargetVO;
import com.hishop.wine.model.vo.miniApp.MinAppTokenVo;
import com.hishop.wine.model.vo.miniApp.MiniAppDetailVO;
import com.hishop.wine.model.vo.payment.PaymentSettingOfflineVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


/**
 * 模块表 相关接口
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */

@Api(value = "MiniAppController", tags = "【微信小程序管理】")
@RestController
@RequestMapping("/miniApp")
public class MiniAppController implements MiniAppFeign {

    @Resource
    private MiniAppBiz miniAppBiz;

    @Resource
    private PaymentSettingBiz paymentSettingBiz;;

    @ApiOperation("PC-获取小程序列表")
    @GetMapping(path = "/pc/list")
    public ResponseBean<List<com.hishop.wine.model.vo.miniApp.MiniAppVO>> list() {
        return ResponseBean.success(miniAppBiz.list());
    }

    @ApiOperation("PC-添加小程序")
    @PostMapping(path = "/pc/create")
    @OperationLog
    public ResponseBean<Void> create(@RequestBody @Valid MiniAppCreatePO createPO) {
        miniAppBiz.create(createPO);
        return ResponseBean.success();
    }

    @ApiOperation("PC-更换小程序")
    @PostMapping(path = "/pc/update")
    @OperationLog
    public ResponseBean<Void> update(@RequestBody @Valid MiniAppUpdatePO updatePO) {
        miniAppBiz.update(updatePO);
        return ResponseBean.success();
    }

    @ApiOperation("PC-修改AppSecret")
    @PostMapping(path = "/pc/updateSecret")
    @OperationLog
    public ResponseBean<Void> updateSecret(@RequestBody @Valid MiniAppUpdateSecretPO updatePO) {
        miniAppBiz.update(BeanUtil.copyProperties(updatePO, MiniAppUpdatePO.class));
        return ResponseBean.success();
    }

    @ApiOperation("PC-查询小程序详情")
    @GetMapping(path = "/pc/detail")
    public ResponseBean<MiniAppDetailVO> detail(@RequestParam Long id) {
        return ResponseBean.success(miniAppBiz.detail(id));
    }

    @ApiOperation(value = "PC-小程序和底部导航下拉", httpMethod = "GET")
    @GetMapping({"/pc/listHomePageAndStoreBar"})
    public ResponseBean<List<HomePageAndStoreBarSelectVO>> listHomePageAndStoreBar(@ApiParam("选中的业务模块的集合,逗号隔开")
                                                                                   @RequestParam(required = false) List<String> selectCodes) {
        return ResponseBean.success(miniAppBiz.listHomePageAndStoreBar(selectCodes));
    }

    @ApiOperation("PC-删除小程序")
    @PostMapping(path = "/pc/delete")
    @OperationLog
    public ResponseBean<Void> delete(@RequestBody @Valid IdPO<Long> idPO) {
        miniAppBiz.delete(idPO.getId());
        return ResponseBean.success();
    }

    @ApiOperation("PC-获取链接目标")
    @GetMapping(path = "/pc/link/target")
    @AuthIgnore
    public ResponseBean<List<LinkTargetVO>> listLinkTarget(@RequestParam("linkType") @ApiParam("链接类型 DECORATE-装修类型 NAVIGATION-底部导航") String linkType) {
        return ResponseBean.success(miniAppBiz.listLinkTarget(linkType));
    }

    @Override
    @ApiOperation(value = "获取小程序线下支付列表", httpMethod = "GET")
    @GetMapping("/listForOfflineSelect")
    public ResponseBean<List<PaymentSettingOfflineVO>> listForOfflineSelect() {
        return ResponseBean.success(paymentSettingBiz.listForOfflineSelect());
    }

    @Override
    @ApiOperation(value = "获取小程序token", httpMethod = "GET")
    @GetMapping("/getAccessToken")
    public ResponseBean<MinAppTokenVo> getAccessToken(@RequestParam("appId") @ApiParam("小程序appId")String appId) {
        Assert.isTrue(!StringUtils.isEmpty(appId), "小程序appId不能为空");
        return ResponseBean.success(miniAppBiz.getAccessToken(appId));
    }

    @Override
    @ApiOperation(value = "发货信息录入", httpMethod = "POST")
    @PostMapping("/uploadShippingInfo")
    public ResponseBean<Void> uploadShippingInfo(@RequestBody @Valid UploadShippingInfoPo uploadShippingInfoPo) {
        miniAppBiz.uploadShippingInfo(uploadShippingInfoPo);
        return ResponseBean.success();
    }

    @ApiOperation("PC-查询小程序详情")
    @GetMapping(path = "/qrCode")
    public ResponseBean<String> qrCode(@RequestParam String appId) {
        return ResponseBean.success(miniAppBiz.getQrCode(appId));
    }

}