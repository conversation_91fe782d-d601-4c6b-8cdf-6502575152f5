package com.hishop.wine.mq.subscriber;

import com.hishop.common.util.RedisUtil;
import com.hishop.mq.api.MQConsumerListener;
import com.hishop.mq.api.MQSubscriber;
import com.hishop.wine.biz.TransactionBiz;
import com.hishop.wine.biz.UserBiz;
import com.hishop.wine.constants.BasicCacheConstants;
import com.hishop.wine.constants.RocketMqConstants;
import com.hishop.wine.model.po.transaction.TransactionEntPayPO;
import com.hishop.wine.model.po.user.UserTagPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date : 2023/8/2
 */
@Component
@Slf4j
@MQConsumerListener
public class UserSubscriber {

    @Resource
    private UserBiz userBiz;
    @Resource
    private TransactionBiz transactionBiz;

    /**
     * 注册成功消息处理
     *
     * @param identityId
     */
    @MQSubscriber(topicName = RocketMqConstants.REGISTER_SUCCESS_TOPIC)
    private void registerSuccessListener(Long identityId) {
        log.info("【注册成功】逻辑处理, identityId: {}", identityId);
        String lock = String.format(BasicCacheConstants.REGISTER_SUCCESS_HANDLE_LOCK, identityId);
        RedisUtil.lock(lock, () -> {
            log.info("【注册成功】逻辑处理获取锁成功, identityId: {}", identityId);
            userBiz.registerSuccessHandel(identityId);
            log.info("【注册成功】逻辑处理成功, identityId: {}", identityId);
        });
    }

    /**
     * 用户打标事务消息
     *
     * @param userTagPO
     */
    @MQSubscriber(topicName = RocketMqConstants.USER_MARK_TAG_TOPIC)
    private void userMarkTagListener(UserTagPO userTagPO) {
        log.info("【用户打标】接收到消息: {}", userTagPO);
        String lock = String.format(BasicCacheConstants.USER_TAG_LOCK, userTagPO.getUserId());
        RedisUtil.lock(lock, () -> {
            log.info("【用户打标】, 获取锁成功, 打标数据: {}", userTagPO);
            userBiz.markTags(userTagPO.getUserId(), new ArrayList<>(userTagPO.getTagIds()));
            log.info("【用户打标】处理消息成功, 打标数据: {}", userTagPO);
        });
    }

    /**
     * 发红包的事务消息
     *
     * @param entPayPO
     */
    @MQSubscriber(topicName = RocketMqConstants.GIVE_RED_PACKAGE_TOPIC)
    private void giveRedPackageListener(TransactionEntPayPO entPayPO) {
        log.info("【企业打款】接收到事务消息: {}", entPayPO);
        String lock = String.format(BasicCacheConstants.GIVE_RED_BAG_LOCK, entPayPO.getBizCode());
        RedisUtil.lock(lock, () -> {
            transactionBiz.entPay(entPayPO);
        });
        log.info("【企业打款】发起成功: {}", entPayPO);
    }

}
