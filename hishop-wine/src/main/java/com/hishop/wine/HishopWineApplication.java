package com.hishop.wine;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(scanBasePackages = {"com.hishop"})
@EnableFeignClients(basePackages = {"com.hishop"})
@EnableConfigurationProperties
@EnableScheduling
@EnableAsync
public class HishopWineApplication {

    public static void main(String[] args) {
        SpringApplication.run(HishopWineApplication.class, args);
    }

}
