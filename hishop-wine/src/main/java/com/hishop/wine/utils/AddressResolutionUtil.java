package com.hishop.wine.utils;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @description: 地址解析工具类
 * @author: chenzw
 * @date: 2024/7/5 19:40
 */
public class AddressResolutionUtil {

    /**
     * 直辖市
     */
    public static final List<String> DIRECTLY_ADMINISTERED_CITIES = Arrays.asList("北京市", "天津市", "上海市", "重庆市");

    public static final String PROVINCE_NAME = "province";

    public static final String CITY_NAME = "city";

    public static final String DISTRICT_NAME = "district";

    public static final String ADDRESS_NAME = "address";

    /**
     * 地址解析
     *
     * @param address 地址
     * @return 地址解析结果
     */
    public static Map<String, String> addressResolution(String address) {
        String regex = "(?<province>[^省]+省|.+自治区|[^澳门]+澳门|[^香港]+香港|[^市]+市)?(?<city>[^自治州]+自治州|[^特别行政区]+特别行政区|[^市]+市|.*?地区|.*?行政单位|.+盟|市辖区|[^县]+县)(?<district>[^县]+县|[^市]+市|[^镇]+镇|[^区]+区|[^乡]+乡|.+场|.+旗|.+海域|.+岛)?(?<address>.*)";
        Matcher m = Pattern.compile(regex).matcher(address);
        String provinceName, cityName, districtName,detailAddress;
        Map<String, String> row = new LinkedHashMap<>();
        while (m.find()) {
            provinceName = m.group(PROVINCE_NAME);
            row.put(PROVINCE_NAME, provinceName == null ? "" : provinceName.trim());

            cityName = m.group(CITY_NAME);
            row.put(CITY_NAME, cityName == null ? "" : cityName.trim());

            districtName = m.group(DISTRICT_NAME);
            row.put(DISTRICT_NAME, districtName == null ? "" : districtName.trim());

            detailAddress = m.group(ADDRESS_NAME);
            row.put(ADDRESS_NAME, detailAddress == null ? "" : detailAddress.trim());
        }
        return row;
    }

}
