package com.hishop.wine.biz;

import com.hishop.common.pojo.IdStatusPo;

import java.util.List;

/**
 * 用户身份表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
public interface IdentityBiz {

    /**
     * 移除身份
     *
     * @param ids 身份id的集合
     */
    void deleteByIds(List<Long> ids);

    /**
     * 更新身份状态
     *
     * @param idStatusPo 身份ids和状态
     */
    void updateStatusByIds(IdStatusPo<Long> idStatusPo);
}