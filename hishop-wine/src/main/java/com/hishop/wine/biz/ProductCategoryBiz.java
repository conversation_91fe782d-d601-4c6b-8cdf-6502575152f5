package com.hishop.wine.biz;

import com.hishop.wine.model.po.product.ProductCategoryCreatePO;
import com.hishop.wine.model.po.product.ProductCategoryUpdatePO;
import com.hishop.wine.model.po.product.ProductCategoryQueryPO;
import com.hishop.wine.model.vo.product.ProductCategoryCacheVO;
import com.hishop.wine.model.vo.product.ProductCategoryVO;

import java.util.List;

import com.hishop.common.response.PageResult;
import com.hishop.wine.repository.entity.ProductCategory;

/**
 * 产品分类表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
public interface ProductCategoryBiz {

    /**
     * 新增产品分类
     *
     * @param createPO 分类参数
     */
    void create(ProductCategoryCreatePO createPO);

    /**
     * 编辑产品分类
     *
     * @param updatePO 分类参数
     */
    void update(ProductCategoryUpdatePO updatePO);

    /**
     * 删除产品分类
     *
     * @param id 分类id
     */
    void deleteById(Long id);

    /**
     * 获取产品分类详情
     *
     * @param id 分类id
     * @return 分类详情
     */
    ProductCategoryVO detail(Long id);

    /**
     * 分页查询分类列表
     *
     * @param pagePO 分页参数
     * @return 分类列表
     */
    PageResult<ProductCategoryVO> pageList(ProductCategoryQueryPO pagePO);

    /**
     * 查询所有产品分类
     *
     * @return 产品分类集合
     */
    List<ProductCategoryCacheVO> listAll();

    /**
     * 移除所有产品分类缓存
     */
    void removeAllCategoryCache();

    /**
     * 检测并获取分类信息
     *
     * @param categoryId 分类id
     * @return 分类信息
     */
    ProductCategory checkAndGetCategory(Long categoryId);

    /**
     * 获取有效的分类信息
     *
     * @param categoryId 分类id
     * @return 分类信息
     */
    ProductCategory checkAndGetValidCategory(Long categoryId);
}