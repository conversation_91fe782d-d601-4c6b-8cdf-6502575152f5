package com.hishop.wine.biz;

import com.hishop.wine.model.po.basic.ResourceCreatePO;
import com.hishop.wine.model.po.basic.ResourceSyncPO;
import com.hishop.wine.model.po.basic.ResourceUpdatePO;
import com.hishop.wine.model.vo.basic.ResourceTreeVo;
import com.hishop.wine.repository.entity.Resource;

import java.util.List;

/**
 * 资源表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
public interface ResourceBiz {

    /**
     * 创建资源
     *
     * @param createPO 创建资源参数
     */
    void create(ResourceCreatePO createPO);

    /**
     * 编辑资源
     *
     * @param updatePO 编辑资源参数
     */
    void update(ResourceUpdatePO updatePO);

    /**
     * 删除资源
     *
     * @param id 资源id
     */
    void deleteById(Long id);

    /**
     * 查询资源树
     *
     * @param ignoreButton 是否忽略按钮资源
     * @return 资源树
     */
    List<ResourceTreeVo> tree(Boolean ignoreButton);

    /**
     * 清除缓存
     */
    void removeCache();

    /**
     * 同步资源
     *
     * @param resourceSyncPO 同步参数
     */
    void syncResource(ResourceSyncPO resourceSyncPO);

    /**
     * 排除下级id(如果上级id在集合中存在)
     *
     * @param resourceIds 资源id的集合
     * @return 排除下级后资源id的集合
     */
    List<Long> excludeSubResourceIds(List<Long> resourceIds);
}