package com.hishop.wine.biz;

import com.hishop.wine.model.po.*;
import com.hishop.wine.model.vo.department.DepartmentVO;

import java.util.List;

/**
 * @Description: 部门表 业务逻辑接口
 * @Author: chenpeng
 * @since: 2023-04-25 10:50:00
 */

public interface DepartmentBiz {

    void create(DepartmentCreatePO departmentCreatePO);

    void update(DepartmentUpdatePO departmentUpdatePO);

    void deleteById(Long id);

    List<DepartmentVO> tree();

    /**
     * 设置部门负责人
     * @param headerPO
     */
    void setHeader(DepartmentEmployeeHeaderPO headerPO);


    /**
     * 用户查询部门信息
     * @param userId
     * @return
     */
    DepartmentVO getDepartmentUser(Long userId);


    /**
     * 修改用户部门、批量
     * @param updateIdPO
     * @return
     */
    Boolean updateDep(DepartmentUpdateIdPO updateIdPO);


    /**
     * 根据部门ID向上平铺查找部门信息
     * @param departmentId
     * @return
     */
    List<DepartmentVO> getDepParentUp(Long departmentId);


    /**
     * 根据部门ID向下平铺查找部门信息
     * @param departmentId
     * @return
     */
    List<DepartmentVO> getDepParentLow(Long departmentId);


    /**
     * 移动部门
     * @param updateMovePO
     * @return
     */
    Boolean updateMove(DepartmentUpdateMovePO updateMovePO);



}