package com.hishop.wine.biz.impl;

import com.hishop.common.util.RedisUtil;
import com.hishop.wine.biz.FrontCacheBiz;
import com.hishop.wine.model.po.FrontCacheSavePO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * @description: 文件业务实现
 * @author: chenzw
 * @date: 2024/5/20 16:43
 */
@Slf4j
@Service
public class FrontCacheBizImpl implements FrontCacheBiz {

    private static final String FRONT_CACHE_KEY = "FRONT_CACHE_KEY:";

    @Override
    public void save(FrontCacheSavePO frontCacheSavePO) {
        RedisUtil.set(FRONT_CACHE_KEY + frontCacheSavePO.getKey(), frontCacheSavePO.getValue(), 3, TimeUnit.HOURS);
    }

    @Override
    public String get(String key) {
        return RedisUtil.get(FRONT_CACHE_KEY + key);
    }
}
