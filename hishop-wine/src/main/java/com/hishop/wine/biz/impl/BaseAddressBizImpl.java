package com.hishop.wine.biz.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.wine.biz.DistrictBiz;
import com.hishop.wine.enums.DistrictLevelEnum;
import com.hishop.wine.model.vo.address.BaseAddressSimpleVO;
import com.hishop.wine.model.vo.basic.DistrictDetailVO;
import com.hishop.wine.repository.entity.BaseAddress;
import com.hishop.wine.model.po.address.BaseAddressCreatePO;
import com.hishop.wine.model.po.address.BaseAddressUpdatePO;
import com.hishop.wine.model.po.address.BaseAddressQueryPO;
import com.hishop.wine.model.vo.address.BaseAddressVO;
import com.hishop.wine.biz.BaseAddressBiz;
import com.hishop.wine.repository.service.BaseAddressService;
import com.hishop.wine.repository.param.BaseAddressParam;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.response.ResponseEnum;
import com.hishop.common.response.PageResultHelper;
import com.hishop.common.response.PageResult;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;


/**
 * 地址库表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-07-17
 */
@Slf4j
@Service
public class BaseAddressBizImpl implements BaseAddressBiz {

    @Resource
    private BaseAddressService baseAddressService;
    @Resource
    private DistrictBiz districtBiz;

    /**
     * 创建地址
     *
     * @param createPO 创建地址参数
     * @return 地址id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(BaseAddressCreatePO createPO) {
        // 获取具体的地区信息
        DistrictDetailVO districtDetail = districtBiz.getDistrictDetail(createPO.getStreetId());
        Assert.isTrue(districtDetail.getLevel() >= DistrictLevelEnum.AREA.getLevel(), "请至少选择三级地区");

        BaseAddress entity = BeanUtil.copyProperties(createPO, BaseAddress.class);
        BeanUtil.copyProperties(districtDetail, entity);
        baseAddressService.save(entity);

        // 如果是标记为默认发货的地址
        setDefault(entity.getId(), createPO.getIzDefaultSendAddress(), createPO.getIzDefaultReceiveAddress());
        return entity.getId();
    }

    /**
     * 编辑地址
     *
     * @param updatePO 编辑地址参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(BaseAddressUpdatePO updatePO) {
        BaseAddress entity = baseAddressService.getById(updatePO.getId());
        if (entity == null) {
            throw new BusinessException(ResponseEnum.NOT_FOUND);
        }

        // 获取具体的地区信息
        DistrictDetailVO districtDetail = districtBiz.getDistrictDetail(updatePO.getStreetId());
        Assert.isTrue(districtDetail.getLevel() >= DistrictLevelEnum.AREA.getLevel(), "请至少选择三级地区");

        BaseAddress updateEntity = BeanUtil.copyProperties(updatePO, BaseAddress.class);
        BeanUtil.copyProperties(districtDetail, updateEntity);
        baseAddressService.updateById(updateEntity);

        // 如果是标记为默认发货的地址
        setDefault(entity.getId(), updatePO.getIzDefaultSendAddress(), updatePO.getIzDefaultReceiveAddress());
    }

    /**
     * 删除地址
     *
     * @param id 地址Id
     */
    @Override
    public void deleteById(Long id) {
        baseAddressService.logicRemoveById(id);
    }

    /**
     * 获取地址详情
     *
     * @param id 地址id
     * @return 地址详情
     */
    @Override
    public BaseAddressVO detail(Long id) {
        BaseAddress entity = baseAddressService.getById(id);
        if (entity == null) {
            throw new BusinessException(ResponseEnum.NOT_FOUND);
        }
        return BeanUtil.copyProperties(entity, BaseAddressVO.class);
    }

    /**
     * 获取地址列表
     *
     * @param qryPO 筛选参数
     * @return 地址列表
     */
    @Override
    public List<BaseAddressSimpleVO> list(BaseAddressQueryPO qryPO) {
        BaseAddressParam param = BeanUtil.copyProperties(qryPO, BaseAddressParam.class);
        List<BaseAddress> dbList = baseAddressService.qryList(param);

        List<BaseAddressVO> baseAddressVOS = BeanUtil.copyToList(dbList, BaseAddressVO.class);
        return baseAddressVOS.stream().map(item -> item.simpleBuilder()).collect(Collectors.toList());
    }

    /**
     * 分页查询地址列表
     *
     * @param pagePO 筛选参数
     * @return 地址列表
     */
    @Override
    public PageResult<BaseAddressSimpleVO> pageList(BaseAddressQueryPO pagePO) {
        BaseAddressParam param = BeanUtil.copyProperties(pagePO, BaseAddressParam.class);
        Page<BaseAddress> dbPage = baseAddressService.qryPage(pagePO.buildPage(), param);
        PageResult<BaseAddressVO> transfer = PageResultHelper.transfer(dbPage, BaseAddressVO.class);
        return PageResultHelper.transfer(transfer, BaseAddressSimpleVO.class);
    }

    /**
     * 设置为默认地址
     *
     * @param id                      地址id
     * @param izDefaultSendAddress    是否默认发货
     * @param izDefaultReceiveAddress 是否默认收货
     */
    private void setDefault(Long id, Boolean izDefaultSendAddress, Boolean izDefaultReceiveAddress) {
        // 如果是标记为默认发货的地址
        if (ObjectUtil.isNotNull(izDefaultSendAddress) && izDefaultSendAddress) {
            setDefaultSend(id);
        }
        // 如果是标记为默认收货的地址
        if (ObjectUtil.isNotNull(izDefaultReceiveAddress) && izDefaultReceiveAddress) {
            setDefaultReceive(id);
        }
    }


    /**
     * 修改默认发货地址
     *
     * @param id 地址id
     */
    private void setDefaultSend(Long id) {
        // 将之前的默认地址设置为非默认
        BaseAddress updAddress = new BaseAddress();
        updAddress.setIzDefaultSendAddress(Boolean.FALSE);
        baseAddressService.update(updAddress, new LambdaQueryWrapper<BaseAddress>()
                .eq(BaseAddress::getIzDefaultSendAddress, Boolean.TRUE));

        updAddress.setIzDefaultSendAddress(Boolean.TRUE);
        updAddress.setId(id);
        baseAddressService.updateById(updAddress);
    }

    /**
     * 修改默认收货地址
     *
     * @param id 地址id
     */
    private void setDefaultReceive(Long id) {
        // 将之前的默认地址设置为非默认
        BaseAddress updAddress = new BaseAddress();
        updAddress.setIzDefaultReceiveAddress(Boolean.FALSE);
        baseAddressService.update(updAddress, new LambdaQueryWrapper<BaseAddress>()
                .eq(BaseAddress::getIzDefaultReceiveAddress, Boolean.TRUE));

        updAddress.setId(id);
        updAddress.setIzDefaultReceiveAddress(Boolean.TRUE);
        baseAddressService.updateById(updAddress);
    }

}