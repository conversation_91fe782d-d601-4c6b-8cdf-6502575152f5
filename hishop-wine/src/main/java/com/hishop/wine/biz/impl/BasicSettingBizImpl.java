package com.hishop.wine.biz.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hishop.setting.AbstractSetting;
import com.hishop.wine.common.utils.XssUtils;
import com.hishop.setting.SettingHelper;
import com.hishop.setting.config.annotation.RefreshSetting;
import com.hishop.wine.constants.SettingConstants;
import com.hishop.wine.enums.BasicSettingEnum;
import com.hishop.wine.biz.BasicSettingBiz;
import com.hishop.wine.model.po.setting.SaveSystemSettingPO;
import com.hishop.wine.model.vo.setting.SystemSettingVO;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 公共设置表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-07-12
 */

@Slf4j
@Service
public class BasicSettingBizImpl implements BasicSettingBiz {

    @Resource
    private SettingHelper settingHelper;

    /**
     * 保存配置
     *
     * @param settingEnum  配置枚举
     * @param settingValue 配置值
     */
    @Override
    public void saveSetting(BasicSettingEnum settingEnum, String settingValue) {
        // 对 JSON 配置进行 XSS 清理
        String cleanedValue = XssUtils.cleanJsonString(settingValue);
        settingHelper.saveSetting(settingEnum.name(), cleanedValue);
    }

    /**
     * 读取配置
     *
     * @param settingEnum 配置枚举
     * @return 配置信息
     */
    @Override
    public <T extends AbstractSetting> T getSetting(BasicSettingEnum settingEnum) {
        return settingHelper.getSetting(settingEnum.name(), settingEnum.getSettingClass());
    }

    /**
     * 保存系统配置
     *
     * @param systemSettingPO 保存系统配置参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RefreshSetting(names = {SettingConstants.WX_WEB_SETTING})
    public void saveSystemSetting(SaveSystemSettingPO systemSettingPO) {
        // 如果保存了appSecret, 判断是否变更
        if (StrUtil.isNotEmpty(systemSettingPO.getAppSecret())) {
            SystemSettingVO dbSetting = getSetting(BasicSettingEnum.SYSTEM_SETTING);
            if (systemSettingPO.getAppSecret().equals(StrUtil.hide(dbSetting.getAppSecret(), 0, dbSetting.getAppSecret().length()))) {
                systemSettingPO.setAppSecret(dbSetting.getAppSecret());
            }
        }
        saveSetting(BasicSettingEnum.SYSTEM_SETTING, JSONUtil.toJsonStr(systemSettingPO));
    }
}