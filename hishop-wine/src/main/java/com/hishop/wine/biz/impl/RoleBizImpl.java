package com.hishop.wine.biz.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.enums.DeleteFlagEnums;
import com.hishop.wine.biz.ResourceBiz;
import com.hishop.wine.constants.BasicConstants;
import com.hishop.wine.model.vo.basic.RoleCacheVO;
import com.hishop.wine.model.vo.basic.RoleVO;
import com.hishop.wine.repository.entity.Identity;
import com.hishop.wine.repository.entity.Role;
import com.hishop.wine.model.po.basic.RoleCreatePO;
import com.hishop.wine.model.po.basic.RoleUpdatePO;
import com.hishop.wine.biz.RoleBiz;
import com.hishop.wine.repository.entity.RoleResourceRelate;
import com.hishop.wine.repository.service.IdentityService;
import com.hishop.wine.repository.service.RoleResourceRelateService;
import com.hishop.wine.repository.service.RoleService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 角色表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Slf4j
@Service
public class RoleBizImpl implements RoleBiz {

    @Resource
    private RoleService roleService;
    @Resource
    private IdentityService identityService;
    @Resource
    private RoleResourceRelateService roleResourceRelateService;
    @Resource
    private ResourceBiz resourceBiz;

    /**
     * 创建角色
     *
     * @param createPO 创建角色信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(RoleCreatePO createPO) {
        Role role = BeanUtil.copyProperties(createPO, Role.class);
        roleService.save(role);

        // 关联角色资源
        relateRoleResource(role.getId(), resourceBiz.excludeSubResourceIds(createPO.getResourceIds()), Boolean.FALSE);
    }

    /**
     * 更新角色
     *
     * @param updatePO 更新角色信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(RoleUpdatePO updatePO) {
        checkSuperAdminRole(updatePO.getId());
        Role role = checkAndGetRole(updatePO.getId(), Boolean.FALSE);

        Role updRole = BeanUtil.copyProperties(updatePO, Role.class);
        roleService.updateById(updRole);

        // 关联角色资源
        relateRoleResource(role.getId(), resourceBiz.excludeSubResourceIds(updatePO.getResourceIds()), Boolean.TRUE);
    }

    /**
     * 删除角色
     *
     * @param id 角色Id
     */
    @Override
    public void deleteById(Long id) {
        checkSuperAdminRole(id);
        long count = identityService.count(new LambdaQueryWrapper<Identity>()
                .eq(Identity::getRoleId, id).eq(Identity::getIzDelete, DeleteFlagEnums.NO.getCode()));
        Assert.isTrue(count == 0, "该角色有用户绑定，不允许删除");

        Role updRole = new Role();
        updRole.setId(id);
        updRole.setIzDelete(Boolean.TRUE);
        roleService.updateById(updRole);
    }

    /**
     * 查询所有角色列表
     *
     * @return 角色列表
     */
    @Override
    public List<RoleCacheVO> listAll() {
        List<Role> roleList = roleService.listRoleIncludeUserNum();

        List<RoleCacheVO> roleCacheVOList = BeanUtil.copyToList(roleList, RoleCacheVO.class);
        roleCacheVOList.forEach(role -> role.setEditAble(!role.getId().equals(BasicConstants.SUPER_ADMIN_ROLE_ID)));
        roleCacheVOList.add(0, RoleCacheVO.ofAllRole(roleList.stream().mapToInt(item -> item.getNum()).sum()));
        return roleCacheVOList;
    }

    /**
     * 检测并且返回角色信息
     *
     * @param roleId      角色id
     * @param checkStatus 是否检测状态
     * @return 角色信息
     */
    @Override
    public Role checkAndGetRole(Long roleId, Boolean checkStatus) {
        Role role = roleService.getById(roleId);
        Assert.isTrue(ObjectUtil.isNotNull(role) && !role.getIzDelete(), "角色不存在");

        if (ObjectUtil.isNotNull(checkStatus) && checkStatus) {
            Assert.isTrue(role.getStatus(), "角色已禁用");
        }
        return role;
    }

    /**
     * 查询角色详情
     *
     * @param id 角色id
     * @return 角色详情
     */
    @Override
    public RoleVO detail(Long id) {
        Role role = checkAndGetRole(id, Boolean.FALSE);
        RoleVO roleVO = BeanUtil.copyProperties(role, RoleVO.class);

        // 查询权限
        List<Long> resourceIds = roleResourceRelateService.listObjs(new LambdaQueryWrapper<RoleResourceRelate>()
                .eq(RoleResourceRelate::getRoleId, id).select(RoleResourceRelate::getResourceId))
                .stream().mapToLong(resourceId -> (Long) resourceId).boxed().collect(Collectors.toList());
        roleVO.setResourceIds(resourceIds);
        return roleVO;
    }

    /**
     * 判断角色是否有权限
     *
     * @param roleId     角色id
     * @param resourceId 资源id
     * @return 是否有权限
     */
    @Override
    public Boolean checkResourceAuth(Long roleId, Long resourceId) {
        // 超级管理员 拥有所有权限
        if (BasicConstants.SUPER_ADMIN_ROLE_ID.equals(roleId)) {
            return Boolean.TRUE;
        }
        return roleResourceRelateService.checkResourceAuth(roleId, resourceId) > 0;
    }

    /**
     * 根据角色id 查询权限id
     *
     * @param roleId 角色id
     * @return 权限id集合
     */
    @Override
    public List<Long> listResourceIdsByRoleId(Long roleId) {
        return roleResourceRelateService.listResourceIdsByRoleId(roleId);
    }

    /**
     * 关联角色资源
     *
     * @param roleId      角色id
     * @param resourceIds 资源id的集合
     */
    private void relateRoleResource(Long roleId, List<Long> resourceIds, Boolean izRemoveHistory) {
        // 先删除角色资源关联
        if (ObjectUtil.isNotNull(izRemoveHistory) && izRemoveHistory) {
            roleResourceRelateService.removeByRoleId(roleId);
        }

        // 如果资源id为空 则不需要关联
        if (CollectionUtils.isEmpty(resourceIds)) {
            return;
        }

        List<RoleResourceRelate> roleResourceRelateList = resourceIds.stream().map(resourceId -> RoleResourceRelate.of(roleId, resourceId)).collect(Collectors.toList());
        // 再关联角色资源
        roleResourceRelateService.insertRoleResourceRelateBatch(roleResourceRelateList);
    }

    /**
     * 检测超级管理员
     *
     * @param roleId 角色Id
     */
    private void checkSuperAdminRole(Long roleId) {
        Assert.isTrue(!roleId.equals(BasicConstants.SUPER_ADMIN_ROLE_ID), "超级管理员角色不允许操作");
    }
}