package com.hishop.wine.biz;

import cn.binarywang.wx.miniapp.config.WxMaConfig;
import com.hishop.common.response.PageResult;
import com.hishop.wine.model.po.LoginErrorPO;
import com.hishop.wine.model.po.LoginErrorQueryPO;
import com.hishop.wine.model.po.login.UserMiniMobileLoginPO;
import com.hishop.wine.model.vo.LoginErrorVo;
import com.hishop.wine.repository.dto.WxLoginDataDTO;

/**
 * @description: 登录异常
 * @author: guoyufeng
 * @date: 2025/6/13 11:21
 */
public interface LoginErrorBiz {

    /**
     * 分页查询
     * @param loginErrorQueryPo
     * @return
     */
    PageResult<LoginErrorVo> pageList(LoginErrorQueryPO loginErrorQueryPo);

    /**
     * 重置微信号
     * @param id
     */
    void resetMinUser(Long id);

    void saveLoginErrorAsync(UserMiniMobileLoginPO loginPO, WxMaConfig wxMaConfig, WxLoginDataDTO wxLoginData, Integer type);
}
