package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.wine.biz.RefundReasonConfigBiz;
import com.hishop.wine.common.enums.RefundType;
import com.hishop.wine.model.po.RefundReasonCreatePO;
import com.hishop.wine.model.vo.refund.RefundReasonConfigVO;
import com.hishop.wine.repository.entity.RefundReasonConfig;
import com.hishop.wine.repository.service.RefundReasonConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**   
 * 退款原因配置表 业务逻辑实现类
 * @author: chenpeng
 * @date: 2023-07-08
 */

@Slf4j
@Service
public class RefundReasonConfigBizImpl implements RefundReasonConfigBiz {

    @Resource
    private RefundReasonConfigService refundReasonConfigService;

    private static final long maxNumber = 20;

    private static final long minNumber = 1;

    @Override
    public List<RefundReasonConfigVO> list(Integer refundType) {
        LambdaQueryWrapper<RefundReasonConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(RefundReasonConfig::getRefundTypes, refundType);
        List<RefundReasonConfig> list = refundReasonConfigService.list(queryWrapper);
        if(CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(list, RefundReasonConfigVO.class);
    }

    @Override
    public List<RefundReasonConfigVO> listForPC() {
        LambdaQueryWrapper<RefundReasonConfig> queryWrapper = new LambdaQueryWrapper<>();
        List<RefundReasonConfig> list = refundReasonConfigService.list(queryWrapper);
        if(CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<RefundReasonConfigVO> result  = BeanUtil.copyToList(list, RefundReasonConfigVO.class);
        result.forEach(e -> {
            JSONArray refundTypes = JSONUtil.parseArray(e.getRefundTypes());
            List<String> refundTypeStrList = RefundType.getRefundTypeStrList(refundTypes);
            e.setRefundTypeStrList(refundTypeStrList);
        });
        return  result;
    }

    @Override
    public RefundReasonConfigVO get(Long id) {
        RefundReasonConfig refundReasonConfig = refundReasonConfigService.getById(id);
        if(refundReasonConfig == null){
            throw new RuntimeException("退款原因配置不存在");
        }
        RefundReasonConfigVO result = BeanUtil.copyProperties(refundReasonConfig, RefundReasonConfigVO.class);
        JSONArray refundTypes = JSONUtil.parseArray(result.getRefundTypes());
        result.setRefundTypes((refundTypes.toList(Integer.class)));
        return result;
    }

    @Override
    public void saveOrUpdate(RefundReasonCreatePO createPO) {
        if(createPO.getId() != null){
            // update
            RefundReasonConfig refundReasonConfig = refundReasonConfigService.getById(createPO.getId());
            if(refundReasonConfig == null){
                throw new RuntimeException("退款原因配置不存在");
            }
            checkMax(createPO);
            BeanUtil.copyProperties(createPO, refundReasonConfig);
            refundReasonConfigService.updateById(refundReasonConfig);
            return;
        }else {
            checkMax(createPO);
            RefundReasonConfig refundReasonConfig = new RefundReasonConfig();
            BeanUtil.copyProperties(createPO, refundReasonConfig);
            refundReasonConfigService.save(refundReasonConfig);
        }
    }

    @Override
    public void delete(Long id) {
        RefundReasonConfig refundReasonConfig = refundReasonConfigService.getById(id);
        if(refundReasonConfig == null){
            throw new RuntimeException("退款原因配置不存在");
        }
        checkMin(refundReasonConfig);
        refundReasonConfigService.removeById(id);
    }

    /**
     * 校验最大配置数量
     */
    private void checkMax(RefundReasonCreatePO createPO) {
        for(Integer refundType : createPO.getRefundTypes()){
            LambdaQueryWrapper<RefundReasonConfig> queryWrapper = new LambdaQueryWrapper<>();
            if(createPO.getId() != null){
                queryWrapper.ne(RefundReasonConfig::getId, createPO.getId());
            }
            queryWrapper.like(RefundReasonConfig::getRefundTypes, refundType);
            long totalNumber = refundReasonConfigService.count(queryWrapper);
            if(totalNumber >= maxNumber){
                throw new RuntimeException(RefundType.getDescByType(refundType) + "原因配置最多只能有20条");
            }
        }
    }

    /**
     * 校验最小配置数量
     */
    private void checkMin(RefundReasonConfig refundReasonConfig) {
        JSONArray refundTypes = JSONUtil.parseArray(refundReasonConfig.getRefundTypes());
        for(Object refundTypeStr : refundTypes) {
            int refundType = (Integer) refundTypeStr;
            LambdaQueryWrapper<RefundReasonConfig> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.like(RefundReasonConfig::getRefundTypes, refundType);
            long totalNumber = refundReasonConfigService.count(queryWrapper);
            if (totalNumber == minNumber) {
                throw new RuntimeException(RefundType.getDescByType(refundType) + "最少保留一个退款原因");
            }
        }
    }

}