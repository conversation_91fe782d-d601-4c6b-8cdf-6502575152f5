package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.hishop.wine.common.utils.TreeConverter;
import com.hishop.wine.model.po.material.MaterialCategoryCreatePO;
import com.hishop.wine.model.vo.micropage.MicropageCategoryVO;
import com.hishop.wine.repository.dao.MaterialCategoryMapper;
import com.hishop.wine.repository.dao.MicropageCategoryMapper;
import com.hishop.wine.repository.dto.MaterialCategoryDTO;
import com.hishop.wine.repository.dto.MaterialCategoryTreeDTO;
import com.hishop.wine.common.enums.MaterialType;
import com.hishop.wine.repository.entity.MaterialCategory;
import com.hishop.wine.biz.MaterialCategoryBiz;
import com.hishop.wine.repository.service.MaterialCategoryService;
import com.hishop.common.exception.BusinessException;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

/**
 * 资源分组表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Slf4j
@Service
@AllArgsConstructor
public class MaterialCategoryBizImpl implements MaterialCategoryBiz {

    @Resource
    private MaterialCategoryService materialCategoryService;


    private final MaterialCategoryMapper mapper;
    /**
     * 分组名称最大长度
     */
    public final static int MAX_NAME_LENGTH = 20;

    /**
     * 最大层级深度
     */
    private final static int MAX_LEVEL_DEPTH = 10;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(MaterialCategoryCreatePO createPO) {

        MaterialCategory parentCategory = ensureAndGetCategoryById(createPO.getParentId());

        //设置层级
        Integer newLevel = parentCategory == null ? 1 : parentCategory.getLevel() + 1;
        Preconditions.checkArgument(newLevel <= MAX_LEVEL_DEPTH, String.format("资源分组最大层级不能超过%s级", MAX_LEVEL_DEPTH));

        ensureCategoryNameLegal(createPO.getParentId(), createPO.getName(), createPO.getType());
        MaterialCategory materialCategory = new MaterialCategory();

        materialCategory.setLevel(newLevel);

        //设置层级路径
        String levelPath = getCurrentLevelPathByParentCategory(parentCategory);
        materialCategory.setPath(levelPath);

        materialCategory.setName(createPO.getName());
        materialCategory.setParentId(createPO.getParentId());
        materialCategory.setType(createPO.getType());

        materialCategoryService.save(materialCategory);

        updateParentIds(parentCategory, Arrays.asList(materialCategory.getId()));
    }

    @Override
    public void update(@NonNull Long id, @NonNull Long parentId, @NonNull String name) {
        MaterialCategory materialCategory = materialCategoryService.getById(id);
        Assert.isTrue(ObjectUtil.compare(id, parentId) != 0, "父级不能为自己");

        if (!materialCategory.getParentId().equals(parentId)) {
            //分组不相等时，需要重新处理分组层级
            MaterialCategory parentCategory = ensureAndGetCategoryById(parentId);

            //设置层级
            Integer newLevel = parentCategory == null ? 1 : parentCategory.getLevel() + 1;
            Preconditions.checkArgument(newLevel <= MAX_LEVEL_DEPTH, String.format("分组最大层级不能超过%s级", MAX_LEVEL_DEPTH));

            materialCategory.setLevel(newLevel);

            //设置层级路径
            String levelPath = getCurrentLevelPathByParentCategory(parentCategory);
            materialCategory.setPath(levelPath);
        }
        if (!materialCategory.getName().equals(name)) {
            //分组名称变更时，需要重新判断分组名称的合法性
            ensureCategoryNameLegal(parentId, name, id, materialCategory.getType());
            materialCategory.setName(name);
        }
        materialCategoryService.updateById(materialCategory);

        updateParentIds(materialCategoryService.getById(parentId), Arrays.asList(materialCategory.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(@NonNull Boolean deleteMaterial, @NonNull List<Long> ids) {
        // todo deleteMaterial 根据 该值判断是否需要删除相关素材
        batchDeleteByRecurse(ids);
    }

    @Override
    public void batchMove(@NonNull Long targetId, @NonNull List<Long> ids) {
        ensureAndGetCategoryById(targetId);
        materialCategoryService.batchMove(targetId, ids);

        MaterialCategory parentCategory = materialCategoryService.getById(targetId);
        updateParentIds(parentCategory, ids);

    }

    @Override
    public List<MaterialCategoryTreeDTO> getAllMaterialCategories(Integer materialType) {
        LambdaQueryWrapper<MaterialCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialCategory::getType, materialType);
        //查询所有
        // List<MaterialCategory> allCategories = materialCategoryService.list(queryWrapper);
        // 统计分组下的资源数 暂时使用sql 统计
        List<MaterialCategory> allCategories = materialCategoryService.listAllCategories(materialType);

        List<MaterialCategoryTreeDTO> materialCategoryTreeDTOS = new ArrayList<>();

        //按顺序循环所有分层
        Map<Long, MaterialCategoryTreeDTO> map = new HashMap<>();
        for (int depth = 1; depth <= MAX_LEVEL_DEPTH; depth++) {
            int finalDepth = depth;

            //查询对应层下的所有分组
            List<MaterialCategory> leveledCategoryList = allCategories.stream()
                    .filter(p -> p.getLevel().equals(finalDepth)).collect(Collectors.toList());

            for (MaterialCategory materialCategory : leveledCategoryList) {
                MaterialCategoryTreeDTO materialCategoryTreeDTO = new MaterialCategoryTreeDTO();
                BeanUtils.copyProperties(materialCategory, materialCategoryTreeDTO);
                materialCategoryTreeDTO.setTotal(materialCategory.getTotalMaterial());
                map.put(materialCategory.getId(), materialCategoryTreeDTO);
                //仅需要将第一层的分组加入到分组树中，其它层级分组直接挂载到对应父分组之下
                if (depth == 1) {
                    materialCategoryTreeDTOS.add(materialCategoryTreeDTO);
                } else {
                    MaterialCategoryTreeDTO parent = map.get(materialCategory.getParentId());
                    if (parent != null) {
                        parent.getChildren().add(materialCategoryTreeDTO);
                    }
                }
            }
        }
        return materialCategoryTreeDTOS;
    }

    @Override
    public MaterialCategoryDTO getById(Long materialCategoryId) {
        MaterialCategory materialCategory = materialCategoryService.getById(materialCategoryId);
        if (materialCategory == null) {
            return null;
        }
        MaterialCategoryDTO materialCategoryDTO = new MaterialCategoryDTO();
        BeanUtils.copyProperties(materialCategory, materialCategoryDTO);
        return materialCategoryDTO;
    }

    @Override
    public synchronized void updateMaterialCount(Long materialCategoryId, Integer total) {
        MaterialCategory materialCategory = materialCategoryService.getById(materialCategoryId);
        if (materialCategory == null) {
            throw new BusinessException(String.format("资源分组不存在【%s】", materialCategoryId));
        }
        materialCategory.setTotal(total == null ? 0 : total);
        materialCategoryService.updateById(materialCategory);
    }

    @Override
    public List<MaterialCategoryTreeDTO> listParent(Integer materialType) {
        LambdaQueryWrapper<MaterialCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialCategory::getType, materialType);
        queryWrapper.lt(MaterialCategory::getLevel, 3);
        List<MaterialCategory> list = mapper.selectList(queryWrapper);
        List<MaterialCategoryTreeDTO> listVO = BeanUtil.copyToList(list, MaterialCategoryTreeDTO.class);
        List<MaterialCategoryTreeDTO> result = TreeConverter.convertMaterialCategoryToTreeList(listVO, 0);
        return result;
    }

    @Override
    public List<MaterialCategoryTreeDTO> getListByIds(Integer materialType, List<Long> cateIds) {
        LambdaQueryWrapper<MaterialCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialCategory::getType, materialType);
        queryWrapper.in(MaterialCategory::getId, cateIds);
        List<MaterialCategory> list = mapper.selectList(queryWrapper);
        List<MaterialCategoryTreeDTO> result = BeanUtil.copyToList(list, MaterialCategoryTreeDTO.class);
        return result;
    }

    @Override
    public List<MaterialCategoryTreeDTO> getListLessThanLevel(Integer materialType, int maxLevel, List<Long> cateIds) {
        LambdaQueryWrapper<MaterialCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialCategory::getType, materialType);
        queryWrapper.in(MaterialCategory::getId, cateIds);
        queryWrapper.le(MaterialCategory::getLevel, maxLevel);
        List<MaterialCategory> list = mapper.selectList(queryWrapper);
        List<MaterialCategoryTreeDTO> listVO = BeanUtil.copyToList(list, MaterialCategoryTreeDTO.class);
        List<MaterialCategoryTreeDTO> result = TreeConverter.convertMaterialCategoryToTreeList(listVO, 0);
        return result;
    }

    /**
     * 批量删除指定id分组及其子分组
     *
     * @param ids 待删除的id集合
     */
    private void batchDeleteByRecurse(@NonNull List<Long> ids) {
        Preconditions.checkArgument(ids.stream().allMatch(p -> p > 0), "待删除分组id必须大于0");
        //循环删除所有id的子级分组
        for (Long id : ids) {
            LambdaQueryWrapper<MaterialCategory> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.like(MaterialCategory::getPath, String.format("|%s|", id));
            materialCategoryService.remove(queryWrapper);
        }
        //批量删除指定id的分组
        materialCategoryService.removeBatchByIds(ids);
    }

    /**
     * 根据父级分组获取当前分组的层级路径
     *
     * @param parentCategory 父级分组
     * @return 返回当前分组对应的
     */
    private String getCurrentLevelPathByParentCategory(MaterialCategory parentCategory) {
        String levlePath = "|";
        if (parentCategory != null) {
            levlePath = String.format("%s|%s|", parentCategory.getPath(), parentCategory.getId());
        }
        return levlePath;
    }

    /**
     * 更新
     *
     * @param parentCategory 上级分类
     * @param ids 当前分类的集合
     */
    private void updateParentIds(MaterialCategory parentCategory, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        List<MaterialCategory> updList = new ArrayList<>();
        ids.forEach(id -> {
            String parentIds = StrUtil.EMPTY;
            if (ObjectUtil.isNotNull(parentCategory)) {
                parentIds = parentCategory.getParentIds();
            }
            parentIds = StrUtil.isEmpty(parentIds) ? String.valueOf(id) : parentIds + StrUtil.COMMA + id;

            MaterialCategory updCategory = new MaterialCategory();
            updCategory.setId(id);
            updCategory.setParentIds(parentIds);
            updList.add(updCategory);
        });

        materialCategoryService.updateBatchById(updList);
    }

    /**
     * 检查指定id的分组是否存在并获取该分组
     *
     * @param id
     * @return 如果存在则返回指定分组，否则返回null（仅当id为0时返回null)
     */
    private MaterialCategory ensureAndGetCategoryById(Long id) {
        Preconditions.checkArgument(id != null && id >= 0, "分组id不能小于0");
        if (id == 0L) {
            return null;
        } else {
            MaterialCategory materialCategory = materialCategoryService.getById(id);
            if (materialCategory == null) {
                throw new BusinessException(String.format("资源分组【%s】", id));
            }
            return materialCategory;
        }
    }


    /**
     * 检查指定分组是否存在，如果不存在，则抛出异常 MaterialCategoryNotExistException
     *
     * @param id 待检查的分组id
     */
    private void ensureCategoryExist(Integer id) {
        Preconditions.checkArgument(id != null && id >= 0, "分组id不能小于0");
        LambdaQueryWrapper<MaterialCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialCategory::getId, id);
        long count = materialCategoryService.count(queryWrapper);
        if (count == 0) {
            throw new BusinessException(String.format("资源分组不存在【%s】", id));
        }
    }


    /**
     * 检查分组名称是否合法
     *
     * @param parentId     上级分组id
     * @param name         分组名称
     * @param materialType 资源类型
     */
    private void ensureCategoryNameLegal(Long parentId, String name, Integer materialType) {
        ensureCategoryNameLegal(parentId, name, null, materialType);
    }

    /**
     * 检查分组名称是否合法
     *
     * @param parentId     上级分组id
     * @param name         分组名称
     * @param currentId    当前分组id；null表示当前还未创建分组
     * @param materialType 分组类型
     */
    private void ensureCategoryNameLegal(Long parentId, String name, Long currentId, Integer materialType) {
        Preconditions.checkArgument(parentId != null && parentId >= 0, "上级分组id不合法");
        Preconditions.checkArgument(!Strings.isNullOrEmpty(name), "资源分组名称不能为空");
        if (name.length() > MAX_NAME_LENGTH) {
            throw new BusinessException(String.format("分组名称超出最大长度[%s]限制", MAX_NAME_LENGTH));
        }
        MaterialCategory materialCategory = materialCategoryService.getById(parentId);
        if (parentId > 0 && materialCategory == null) {
            throw new BusinessException(String.format("资源库分组[%s]不存在", parentId));
        }
        if (checkNameExist(parentId, name, currentId, materialType)) {
            throw new BusinessException("当前上级分组下已存在相同的分组名称");
        }
    }

    /**
     * 检查在指定上级分组id的下分组名称是否已经存在
     *
     * @param parentId     上级分组id
     * @param name         指定名称
     * @param ignoreId     需要忽略对比的分组id；null表示不需要忽略对比
     * @param materialType 分组类型
     * @return 存在返回true, 不存在返回false
     */
    private Boolean checkNameExist(Long parentId, String name, Long ignoreId, Integer materialType) {
        LambdaQueryWrapper<MaterialCategory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MaterialCategory::getParentId, parentId);
        queryWrapper.eq(MaterialCategory::getName, name);
        queryWrapper.eq(MaterialCategory::getType, materialType);
        if (ignoreId != null && ignoreId > 0) {
            queryWrapper.ne(MaterialCategory::getId, ignoreId);
        }
        return materialCategoryService.count(queryWrapper) > 0;
    }

}