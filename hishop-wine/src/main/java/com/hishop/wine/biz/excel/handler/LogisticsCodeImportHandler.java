package com.hishop.wine.biz.excel.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Maps;
import com.hishop.common.excel.read.ReadResult;
import com.hishop.common.export.context.DataWrapper;
import com.hishop.common.export.model.BizType;
import com.hishop.wine.biz.LogisticsCodeBiz;
import com.hishop.wine.biz.excel.context.BizContent;
import com.hishop.wine.biz.excel.dealer.AbstractImportHandler;
import com.hishop.wine.biz.excel.wrapper.LogisticsCodeErrWrapper;
import com.hishop.wine.common.enums.CodeTypeEnum;
import com.hishop.wine.common.enums.FileImportType;
import com.hishop.wine.model.po.logisticsCode.LogisticsCodeImportPo;
import com.hishop.wine.repository.entity.*;
import com.hishop.wine.repository.service.*;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.compress.utils.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
@Service
public class LogisticsCodeImportHandler extends AbstractImportHandler<LogisticsCodeImportPo> {

    @Resource
    private LogisticsCodeService logisticsCodeService;
    @Resource
    private LogisticsCodeBiz logisticsCodeBiz;

    @Override
    public BizType bizType() {
        return FileImportType.LOGISTICS_CODE_IMPORT;
    }

    @Override
    public void checkExistsAndSetValue(ReadResult<LogisticsCodeImportPo> importResult, BizContent bizContent, FileImportRecord record) {
        List<LogisticsCodeImportPo> importList = importResult.getDataList();
        // 获取所有箱码
        Set<String> caseCodes = importList.stream().filter(po->CodeTypeEnum.CASE_CODE.getDesc().equals(po.getCodeTypeStr()))
                .map(LogisticsCodeImportPo::getCodeSecondary)
                .collect(Collectors.toSet());
        // 获取数据库中已经存在的编码
        Set<String> caseCodeSet = logisticsCodeBiz.getCodes(caseCodes);
        // 获取所有的盒码
        Set<String> boxCodes = importList.stream().filter(po->CodeTypeEnum.BOX_CODE.getDesc().equals(po.getCodeTypeStr()))
                .map(LogisticsCodeImportPo::getCodeSecondary)
                .collect(Collectors.toSet());
        // 获取数据库中已存在的编码
        Set<String> boxCodeSet = logisticsCodeBiz.getCodes(boxCodes);
        // 获取所有商品编码
        Set<String> productCodes = importList.stream()
                .map(LogisticsCodeImportPo::getProductCode)
                .collect(Collectors.toSet());
        // 找出当批次所有存在的商品编码
        Map<String, Long> productCodeMap = logisticsCodeBiz.getProductCode(productCodes);
        Set<String> productCodeSet = productCodeMap.keySet();
        // 当前批次中的编码（判断重复）
        Set<String> codesAddSet = Sets.newHashSet();
        importList.forEach(item -> {
            item.setFileImportId(record.getId());
            item.setStatus(0);
            item.setCodeCategory(0);
            String errMsg = item.getErrMsg();
            if (errMsg == null) {
                errMsg = "";
            }
            if(StringUtils.isNotEmpty(item.getProductCode())) {
                if (!productCodeSet.contains(item.getProductCode())) {
                    errMsg += "商品编码不存在;";
                } else {
                    item.setProductId(productCodeMap.get(item.getProductCode()));
                }
            }
            if (CodeTypeEnum.getCodeTypeEnumByDesc(item.getCodeTypeStr()) == null) {
                errMsg += "物流码类型只能为盒码/箱码;";
            } else {
                item.setCodeType(CodeTypeEnum.getCodeTypeEnumByDesc(item.getCodeTypeStr()).getType());
            }
            if(CodeTypeEnum.CASE_CODE.getType().equals(item.getCodeType())) {
                // 箱码
                if (caseCodeSet.contains(item.getCodeSecondary())) {
                    errMsg += "库中箱码已存在;";
                }
                if(codesAddSet.contains(item.getCodeSecondary())) {
                    errMsg += "当前批次箱码出现重复;";
                } else {
                    codesAddSet.add(item.getCodeSecondary());
                }
            } else if(CodeTypeEnum.BOX_CODE.getType().equals(item.getCodeType())) {
                // 盒码
                if (boxCodeSet.contains(item.getCodeSecondary())) {
                    errMsg += "库中盒码已存在;";
                }
                if(codesAddSet.contains(item.getCodeSecondary())) {
                    errMsg += "当前批次盒码出现重复;";
                } else {
                    codesAddSet.add(item.getCodeSecondary());
                }
                // 如果一级码存在，就需要判断是否存在对应的箱码
                if(StringUtils.isNotEmpty(item.getCodeFirst())) {
                    if(!caseCodes.contains(item.getCodeFirst())) {
                        errMsg += "对应箱码不存在;";
                    }
                }
            }
            item.setErrMsg(errMsg);
        });
    }

    @Override
    public void saveImportData(List<LogisticsCodeImportPo> successList, FileImportRecord record) {
        List<LogisticsCode> dbList = BeanUtil.copyToList(successList, LogisticsCode.class);
        Map<String, Integer> map = Maps.newHashMap();
        List<LogisticsCode> caseList = Lists.newArrayList();
        dbList.forEach(f->{
            if(f.getCodeType() == 1) {
                // 箱码不存在就存入map
                if(!map.containsKey(f.getCodeSecondary())) {
                    map.put(f.getCodeSecondary(), 0);
                }
                f.setNum(0);
                caseList.add(f);
            } else {
                // 盒码是否存在一级码
                if(StringUtils.isNotEmpty(f.getCodeFirst())) {
                    // 一级码不在map中就put新的
                    if(!map.containsKey(f.getCodeFirst())) {
                        map.put(f.getCodeFirst(), 1);
                    } else {
                        // 存在就数量+1
                        map.put(f.getCodeFirst(), map.get(f.getCodeFirst()) + 1);
                    }
                }
                f.setNum(1);
            }
        });
        caseList.forEach(f->{
            f.setNum(map.get(f.getCodeSecondary()));
        });
        if(CollectionUtil.isNotEmpty(dbList)) {
            logisticsCodeService.saveBatch(dbList);
        }
    }

    @Override
    protected DataWrapper<LogisticsCodeImportPo> wrapData(List<LogisticsCodeImportPo> errList) {
        return new LogisticsCodeErrWrapper(errList);
    }
}
