package com.hishop.wine.biz.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.common.enums.DeleteFlagEnums;
import com.hishop.common.excel.read.ExcelReadHelper;
import com.hishop.common.excel.read.ReadResult;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import com.hishop.wine.biz.FileImportRecordBiz;
import com.hishop.wine.biz.excel.dealer.ImportAssist;
import com.hishop.wine.biz.excel.listener.LogisticsCodeReadListener;
import com.hishop.wine.biz.excel.listener.LogisticsCodeScanReadListener;
import com.hishop.wine.common.enums.FileImportStatus;
import com.hishop.wine.common.enums.FileImportType;
import com.hishop.wine.model.po.logisticsCode.LogisticsCodeImportPo;
import com.hishop.wine.model.po.logisticsCodeScan.LogisticsCodeScanImportPo;
import com.hishop.wine.model.po.fileImport.FileImportRecordQueryPo;
import com.hishop.wine.model.vo.fileImport.FileImportRecordVo;
import com.hishop.wine.repository.entity.FileImportRecord;
import com.hishop.wine.repository.entity.User;
import com.hishop.wine.repository.service.FileImportRecordService;
import com.hishop.wine.repository.service.UserService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 文件导入记录业务实现类
 * @author: chenzw
 * @date: 2024/7/6 10:32
 */
@Service
@RequiredArgsConstructor
public class FileImportRecordBizImpl implements FileImportRecordBiz {

    private final FileImportRecordService fileImportRecordService;

    private final UserService userService;

    private final ImportAssist importAssist;

    @Value("${nfs.obs.domain}")
    private String obsPrefixUrl;

    @Override
    public void deleteById(Long id) {
        FileImportRecord importRecord = fileImportRecordService.getById(id);
        Assert.notNull(importRecord, "文件导入记录不存在");
        if(FileImportType.LOGISTICS_CODE_IMPORT.getType().equals(importRecord.getImportType())
            || FileImportType.LOGISTICS_CODE_SCAN_IMPORT.getType().equals(importRecord.getImportType())) {
           if(importRecord.getSuccessCount() != 0) {
               throw new BusinessException("只有上传失败的记录可以删除（成功数量为0）");
           }
        }
        importRecord.setIzDelete(Boolean.TRUE);
        fileImportRecordService.updateById(importRecord);
    }

    @Override
    public PageResult<FileImportRecordVo> pageList(FileImportRecordQueryPo pagePo) {
        LambdaQueryWrapper<FileImportRecord> wrapper = new LambdaQueryWrapper<FileImportRecord>()
                .eq(FileImportRecord::getIzDelete, DeleteFlagEnums.NO.getCode())
                .eq(FileImportRecord::getImportType, pagePo.getImportType());
        wrapper.orderByDesc(FileImportRecord::getCreateTime);

        Page<FileImportRecord> dbPage = fileImportRecordService.page(pagePo.buildPage(), wrapper);
        if (CollUtil.isEmpty(dbPage.getRecords())) {
            return PageResultHelper.defaultEmpty(pagePo);
        }
        List<Long> userIdList = dbPage.getRecords().stream()
                .map(FileImportRecord::getCreateBy)
                .collect(Collectors.toList());
        Map<Long, User> userMap = userService.list(new LambdaQueryWrapper<User>().in(User::getId, userIdList))
                .stream()
                .collect(Collectors.toMap(User::getId, Function.identity()));
        return PageResultHelper.transfer(dbPage, FileImportRecordVo.class, vo -> {
            vo.setImportStatusDesc(Objects.requireNonNull(FileImportStatus.getFileImportStatusEnum(vo.getImportStatus())).getDesc());
            vo.setCreateName(userMap.get(vo.getCreateBy()).getRealName());
            vo.setErrFilePath("https://" +  obsPrefixUrl +  vo.getErrFilePath());
        });
    }

    @Override
    public PageResult<FileImportRecordVo> pageLogisticsList(FileImportRecordQueryPo pagePo) {
        LambdaQueryWrapper<FileImportRecord> wrapper = new LambdaQueryWrapper<FileImportRecord>()
                .eq(FileImportRecord::getIzDelete, DeleteFlagEnums.NO.getCode());
        if(StringUtils.isEmpty(pagePo.getImportType())) {
            wrapper.and(w -> w.eq(FileImportRecord::getImportType, FileImportType.LOGISTICS_CODE_SCAN_IMPORT.getType())
                    .or().eq(FileImportRecord::getImportType, FileImportType.LOGISTICS_CODE_IMPORT.getType()));
        } else {
            wrapper.eq(FileImportRecord::getImportType, pagePo.getImportType());
        }
        wrapper.orderByDesc(FileImportRecord::getCreateTime);

        Page<FileImportRecord> dbPage = fileImportRecordService.page(pagePo.buildPage(), wrapper);
        if (CollUtil.isEmpty(dbPage.getRecords())) {
            return PageResultHelper.defaultEmpty(pagePo);
        }
        List<Long> userIdList = dbPage.getRecords().stream()
                .map(FileImportRecord::getCreateBy)
                .collect(Collectors.toList());
        Map<Long, User> userMap = userService.list(new LambdaQueryWrapper<User>().in(User::getId, userIdList))
                .stream()
                .collect(Collectors.toMap(User::getId, Function.identity()));
        return PageResultHelper.transfer(dbPage, FileImportRecordVo.class, vo -> {
            vo.setImportStatusDesc(Objects.requireNonNull(FileImportStatus.getFileImportStatusEnum(vo.getImportStatus())).getDesc());
            vo.setCreateName(userMap.get(vo.getCreateBy()).getRealName());
            vo.setErrFilePath("https://" +  obsPrefixUrl +  vo.getErrFilePath());
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fileImportLogisticsCode(MultipartFile file, String name) {
        // 查询名称是否有重复
        Long count = fileImportRecordService.count(new LambdaQueryWrapper<FileImportRecord>()
                .eq(FileImportRecord::getName, name)
                .eq(FileImportRecord::getIzDelete, DeleteFlagEnums.NO.getCode())
                .eq(FileImportRecord::getImportType, "LOGISTICS_CODE"));
        if(count > 0) {
            throw new BusinessException("批次名称已存在");
        }
        FileImportRecord fileImportRecord = new FileImportRecord();
        fileImportRecord.setName(name);
        fileImportRecord.setFileName(file.getOriginalFilename());
        fileImportRecord.setImportType(FileImportType.LOGISTICS_CODE_IMPORT.getType());
        fileImportRecord.setImportStatus(FileImportStatus.PROCESSING.getType());
        fileImportRecordService.save(fileImportRecord);

        ReadResult<LogisticsCodeImportPo> importResult = ExcelReadHelper.read(file, LogisticsCodeImportPo.class, new LogisticsCodeReadListener(), 2);
        if (CollectionUtil.isEmpty(importResult.getDataList())) {
            throw new BusinessException("模板导入数据为空，请检查导入文件");
        }
        importAssist.importData(fileImportRecord, FileImportType.LOGISTICS_CODE_IMPORT, null, importResult);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fileImportLogisticsCodeScan(MultipartFile file, String name) {
        // 查询名称是否有重复
        Long count = fileImportRecordService.count(new LambdaQueryWrapper<FileImportRecord>()
                .eq(FileImportRecord::getName, name)
                .eq(FileImportRecord::getIzDelete, DeleteFlagEnums.NO.getCode())
                .eq(FileImportRecord::getImportType, "LOGISTICS_CODE_SCAN"));
        if(count > 0) {
            throw new BusinessException("批次名称已存在");
        }
        FileImportRecord fileImportRecord = new FileImportRecord();
        fileImportRecord.setName(name);
        fileImportRecord.setFileName(file.getOriginalFilename());
        fileImportRecord.setImportType(FileImportType.LOGISTICS_CODE_SCAN_IMPORT.getType());
        fileImportRecord.setImportStatus(FileImportStatus.PROCESSING.getType());
        fileImportRecordService.save(fileImportRecord);

        ReadResult<LogisticsCodeScanImportPo> importResult = ExcelReadHelper.read(file, LogisticsCodeScanImportPo.class, new LogisticsCodeScanReadListener(), 2);
        if (CollectionUtil.isEmpty(importResult.getDataList())) {
            throw new BusinessException("模板导入数据为空，请检查导入文件");
        }
        importAssist.importData(fileImportRecord, FileImportType.LOGISTICS_CODE_SCAN_IMPORT, null, importResult);
    }

    @Override
    public PageResult<FileImportRecordVo> queryFileImportCodePageList(FileImportRecordQueryPo pagePo) {
        /*if (StringUtils.isBlank(pagePo.getProductCode())){
            throw new BusinessException("产品编码不能为空");
        }*/
        return PageResultHelper.transfer(fileImportRecordService.queryFileImportCodePageList(pagePo.buildPage(), pagePo),FileImportRecordVo.class);
    }

    @Override
    public PageResult<FileImportRecordVo> queryLogisticsCodePageList(FileImportRecordQueryPo pagePo) {
        pagePo.setProductCode(null);
        return PageResultHelper.transfer(fileImportRecordService.queryFileImportCodePageList(pagePo.buildPage(), pagePo),FileImportRecordVo.class);
    }

    @Override
    public Boolean queryNameIsRepeat(String name) {
        Assert.isTrue(StringUtils.isNotBlank(name), "批次名称不能为空");
        // 查询名称是否有重复
        Long count = fileImportRecordService.count(new LambdaQueryWrapper<FileImportRecord>()
                .eq(FileImportRecord::getName, name)
                .eq(FileImportRecord::getIzDelete, DeleteFlagEnums.NO.getCode())
                .eq(FileImportRecord::getImportType, "LOGISTICS_CODE"));
        return count > 0;
    }
}
