package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.pojo.entity.BaseEntity;
import com.hishop.wine.biz.SaleDimBiz;
import com.hishop.wine.model.po.SaleDimPo;
import com.hishop.wine.model.vo.sale.SaleDimVo;
import com.hishop.wine.repository.entity.SaleDim;
import com.hishop.wine.repository.service.SaleDimService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description: 销售维度业务实现类
 * @author: chenzw
 * @date: 2024/7/4 09:17
 */
@Service
@RequiredArgsConstructor
public class SaleDimBizImpl implements SaleDimBiz {

    private final SaleDimService saleDimService;

    @Override
    public List<SaleDimVo> querySaleDimList() {
        return BeanUtil.copyToList(saleDimService.list(new LambdaQueryWrapper<SaleDim>().eq(BaseEntity::getIzDelete, Boolean.FALSE)), SaleDimVo.class);
    }

    @Override
    public void updateSaleDim(SaleDimPo saleDimPo) {
        SaleDim saleDim = saleDimService.getById(saleDimPo.getId());
        Assert.notNull(saleDim, "销售维度不存在");
        saleDimService.updateById(BeanUtil.copyProperties(saleDimPo, SaleDim.class));
    }
}
