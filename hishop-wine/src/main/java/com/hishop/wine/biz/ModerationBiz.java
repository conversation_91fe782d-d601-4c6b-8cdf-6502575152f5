package com.hishop.wine.biz;

import com.hishop.moderation.model.*;
import com.hishop.wine.model.po.moderation.ContentModerationCreatePO;

/**
 * <AUTHOR>
 * @date 2023/8/3
 */
public interface ModerationBiz {

    /**
     * 发起审核
     *
     * @param contentModerationCreatePO 审核入参
     * @return 审核id
     */
    Long check(ContentModerationCreatePO contentModerationCreatePO);

    /**
     * 文本内容审核
     *
     * <AUTHOR>
     * @date 2023/8/3
     */
    ModerationResult checkText(TextData textData);

    /**
     * 图像内容审核
     *
     * <AUTHOR>
     * @date 2023/8/3
     */
    ModerationResult checkImage(ImageData imageData);

    /**
     * 视频内容审核
     *
     * <AUTHOR>
     * @date 2023/8/3
     */
    ModerationResult checkVideo(VideoData videoData);

    /**
     * 音频内容审核
     *
     * <AUTHOR>
     * @date 2023/8/3
     */
    ModerationResult checkAudio(AudioData audioData);

}
