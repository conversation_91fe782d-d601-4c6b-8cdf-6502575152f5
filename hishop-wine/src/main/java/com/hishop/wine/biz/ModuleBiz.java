package com.hishop.wine.biz;

import com.hishop.wine.model.po.miniApp.MiniAppQueryPO;
import com.hishop.wine.model.vo.basic.MiniAppVO;
import com.hishop.wine.model.vo.module.BindModuleVO;
import com.hishop.wine.model.vo.module.ModuleVO;
import java.util.List;

/**
 * 模块表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
public interface ModuleBiz {

    /**
     * 获取授权模块编码列表
     *
     * @return
     */
    List<ModuleVO> listAuthModule();

    /**
     * 获取授权模块(包括基础系统)
     *
     * @return 授权模块
     */
    List<ModuleVO> listAuthModuleIncludeBasic();

    /**
     * 获取授权模块编码
     *
     * @return 授权模块编码
     */
    List<String> listAuthModuleCodes();


    List<MiniAppVO> list(MiniAppQueryPO pagePo);

    MiniAppVO detail(String appId);

    List<ModuleVO> listAuthModuleUnBind();

    List<BindModuleVO> listABindModule(String appId);

    /**
     * 查询是否有扫码营销模块
     * @return
     */
    Boolean queryHasScanCodeModule();
}