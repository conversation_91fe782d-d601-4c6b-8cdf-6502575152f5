package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.pojo.entity.BaseEntity;
import com.hishop.wine.biz.SaleAreaBiz;
import com.hishop.wine.constants.Constant;
import com.hishop.wine.model.po.SaleAreaAddPo;
import com.hishop.wine.model.po.SaleAreaQueryPo;
import com.hishop.wine.model.po.SaleAreaUpdatePo;
import com.hishop.wine.model.vo.sale.SaleAreaVo;
import com.hishop.wine.repository.entity.SaleArea;
import com.hishop.wine.repository.service.SaleAreaService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @description: 销售区域业务实现类
 * @author: chenzw
 * @date: 2024/7/4 09:41
 */
@Service
@RequiredArgsConstructor
public class SaleAreaBizImpl implements SaleAreaBiz {

    private final SaleAreaService saleAreaService;

    @Override
    public List<SaleAreaVo> querySaleAreaList(SaleAreaQueryPo saleAreaQueryPo) {
        LambdaQueryWrapper<SaleArea> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BaseEntity::getIzDelete, Boolean.FALSE);
        wrapper.eq(saleAreaQueryPo.getSaleDimId() != null, SaleArea::getSaleDimId, saleAreaQueryPo.getSaleDimId());
        wrapper.eq(saleAreaQueryPo.getParentId() != null, SaleArea::getParentId, saleAreaQueryPo.getParentId());

        List<SaleArea> saleAreaList = saleAreaService.list(wrapper);
        return BeanUtil.copyToList(saleAreaList, SaleAreaVo.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addSaleArea(SaleAreaAddPo saleAreaAddPo) {

        long count = saleAreaService.count(new LambdaQueryWrapper<SaleArea>()
                .eq(SaleArea::getSaleDimId, saleAreaAddPo.getSaleDimId())
                .eq(SaleArea::getParentId, saleAreaAddPo.getParentId())
                .eq(BaseEntity::getIzDelete, Boolean.FALSE)
                .in(SaleArea::getName, saleAreaAddPo.getName()));

        if (count > 0) {
            throw new BusinessException("当前销售区域名称重复，请重新操作");
        }

        if (saleAreaAddPo.getName().size() != saleAreaAddPo.getName().stream().distinct().count()) {
            throw new BusinessException("当前销售区域名称重复，请重新操作");
        }

        SaleArea parentSaleArea = null;
        //判断是否是最上级销售区域
        if (saleAreaAddPo.getParentId() != 0) {
            parentSaleArea = saleAreaService.getById(saleAreaAddPo.getParentId());
        }
        List<SaleArea> update = Lists.newArrayList();
        for (String name : saleAreaAddPo.getName()) {
            SaleArea saleArea = new SaleArea();
            saleArea.setSaleDimId(saleAreaAddPo.getSaleDimId());
            saleArea.setParentId(saleAreaAddPo.getParentId());
            saleArea.setName(name);
            saleAreaService.save(saleArea);
            update.add(saleArea);
        }

        if (CollectionUtils.isNotEmpty(update)) {
            for (SaleArea saleArea : update) {
                String code = String.format("%05d", saleArea.getId());
                saleArea.setCode(code);
                saleArea.setFullCode(parentSaleArea == null ? saleArea.getCode() : parentSaleArea.getFullCode() + Constant.DEFAULT_SALE_AREA_SPLIT + saleArea.getCode());
                saleArea.setFullName(parentSaleArea == null ? saleArea.getName() : parentSaleArea.getFullName() + Constant.DEFAULT_SALE_AREA_SPLIT + saleArea.getName());
            }
            saleAreaService.updateBatchById(update);
        }

    }

    @Override
    public void updateSaleArea(SaleAreaUpdatePo saleAreaUpdatePo) {
        SaleArea saleArea = saleAreaService.getById(saleAreaUpdatePo.getId());
        Assert.notNull(saleArea, "销售区域不存在");

        long count = saleAreaService.count(new LambdaQueryWrapper<SaleArea>()
                .eq(SaleArea::getSaleDimId, saleArea.getSaleDimId())
                .eq(SaleArea::getParentId, saleArea.getParentId())
                .eq(BaseEntity::getIzDelete, Boolean.FALSE)
                .ne(SaleArea::getId, saleAreaUpdatePo.getId())
                .eq(SaleArea::getName, saleAreaUpdatePo.getName()));

        if (count > 0) {
            throw new BusinessException("当前销售区域名称重复，请重新操作");
        }

        saleArea.setName(saleAreaUpdatePo.getName());
        saleAreaService.updateById(saleArea);
    }

    @Override
    public void deleteSaleArea(Long id) {
        SaleArea saleArea = saleAreaService.getById(id);
        Assert.notNull(saleArea, "销售区域不存在");

        long childCount = saleAreaService.count(new LambdaQueryWrapper<>(SaleArea.class)
                .eq(SaleArea::getParentId, id)
                .eq(BaseEntity::getIzDelete, Boolean.FALSE));

        if (childCount > 0) {
            throw new RuntimeException("当前销售区域存在子级销售区域，不能进行删除操作");
        }
        saleArea.setIzDelete(Boolean.TRUE);
        saleAreaService.updateById(saleArea);
    }
}
