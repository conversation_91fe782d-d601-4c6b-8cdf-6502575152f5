package com.hishop.wine.biz;

import java.util.List;

import com.hishop.common.response.PageResult;
import com.hishop.wine.model.po.rank.RankCreatePO;
import com.hishop.wine.model.po.rank.RankQueryPO;
import com.hishop.wine.model.po.rank.RankUpdatePO;
import com.hishop.wine.model.vo.rank.RankSelectVO;
import com.hishop.wine.model.vo.rank.RankVO;

/**
 * 头衔表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-07-25
 */

public interface RankBiz {

    /**
     * 新增头衔
     *
     * @param createPO 新增头衔参数
     * @return 头衔id
     */
    Long create(RankCreatePO createPO);

    /**
     * 编辑头衔
     *
     * @param updatePO 编辑头衔参数
     */
    void update(RankUpdatePO updatePO);

    /**
     * 删除头衔
     *
     * @param ids
     */
    void deleteByIdS(List<Long> ids);

    /**
     * 查询头衔
     *
     * @param id 头衔id
     * @return 头衔详情
     */
    RankVO detail(Long id);

    /**
     * 查询头衔列表
     *
     * @param queryPO 查询参数
     * @return 头衔列表
     */
    List<RankVO> list(RankQueryPO queryPO);

    /**
     * 分页查询头衔列表
     *
     * @param pagePO 分页参数
     * @return 头衔列表
     */
    PageResult<RankVO> pageList(RankQueryPO pagePO);

    /**
     * 修改头衔状态
     *
     * @param ids    头衔id的集合
     * @param status 头衔状态 true-启用 false-禁用
     */
    void changeStatus(List<Long> ids, Boolean status);

    /**
     * 获取头衔下拉
     *
     * @param needAll 是否需要全部
     * @return 获取头衔下拉
     */
    List<RankSelectVO> listForSelect(Boolean needAll);
}