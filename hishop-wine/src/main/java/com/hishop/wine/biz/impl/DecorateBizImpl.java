package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.util.RedisUtil;
import com.hishop.wine.biz.DecorateBiz;
import com.hishop.wine.enums.DecorateTypeEnums;
import com.hishop.wine.enums.ModuleEnums;
import com.hishop.wine.model.po.decorate.DecoratePO;
import com.hishop.wine.model.po.decorate.DecorateQueryPO;
import com.hishop.wine.model.vo.decorate.DecorateVO;
import com.hishop.wine.repository.entity.Decorate;
import com.hishop.wine.repository.service.DecorateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**   
 * 装修表 业务逻辑实现类
 * @author: chenpeng
 * @date: 2023-07-11
 */

@Slf4j
@Service
public class DecorateBizImpl implements DecorateBiz {

    @Resource
    private DecorateService decorateService;


    @Override
    public void save(DecoratePO createPo) {
        if(StringUtils.isNotEmpty(createPo.getAppId())) {
            //appID不为空，说明是小程序装修。
            if(createPo.getDecorateType().equals(DecorateTypeEnums.HOME_PAGE.getType())) {
                //首页装修每个子系统独立
                Decorate entity = decorateService.getByType(null, createPo.getModuleCode(), createPo.getDecorateType());
                if (entity == null) {
                    entity = new Decorate();
                }
                BeanUtils.copyProperties(createPo, entity);
                entity.setIzDefault(true);
                decorateService.saveOrUpdate(entity);
                decorateService.updateOtherDefault(createPo.getAppId(), createPo.getModuleCode(), createPo.getDecorateType());
            }else {
                Decorate entity = decorateService.getByType(createPo.getAppId(), createPo.getModuleCode(), createPo.getDecorateType());
                if (entity == null) {
                    entity = new Decorate();
                }
                BeanUtils.copyProperties(createPo, entity);
                entity.setIzDefault(true);
                decorateService.saveOrUpdate(entity);
            }
            RedisUtil.del(MiniConfigBizImpl.DECORATE_KEY + createPo.getAppId());
        }else {
            //是其他子系统的装修
            Decorate entity = decorateService.getByType(createPo.getAppId(), createPo.getModuleCode(), createPo.getDecorateType());
            if (entity == null) {
                entity = new Decorate();
            }
            BeanUtils.copyProperties(createPo, entity);
            entity.setModuleCode(createPo.getModuleCode());
            entity.setDecorateType(createPo.getDecorateType());
            entity.setSettingJson(createPo.getSettingJson());
            decorateService.saveOrUpdate(entity);
        }
    }

    @Override
    public DecorateVO detail(DecorateQueryPO queryPO) {
        Decorate entity = decorateService.getByType(queryPO.getAppId(), queryPO.getModuleCode(), queryPO.getDecorateType());
        if(entity == null) {
            //如果没有找到，就找默认的
            entity = decorateService.getByType(null, queryPO.getModuleCode(), queryPO.getDecorateType());
        }
        if (entity == null) {
            return new DecorateVO();
        }
        return BeanUtil.copyProperties(entity, DecorateVO.class);
    }

    @Override
    public List<DecorateVO> listDecorateByAppId(String appId) {
        LambdaQueryWrapper<Decorate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Decorate::getAppId, appId);
        List<Decorate> list = decorateService.list(wrapper);
        return BeanUtil.copyToList(list, DecorateVO.class);
    }

}