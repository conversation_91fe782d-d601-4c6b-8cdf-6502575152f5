package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import com.hishop.sms.api.Channel;
import com.hishop.sms.api.SmsSender;
import com.hishop.wine.biz.SmsBiz;
import com.hishop.wine.common.utils.VerificationCodeUtil;
import com.hishop.wine.enums.SmsEnum;
import com.hishop.wine.model.po.sms.SendSmsPO;
import com.hishop.wine.repository.entity.SmsRecord;
import com.hishop.wine.repository.entity.SmsRecordMobile;
import com.hishop.wine.repository.service.SmsRecordMobileService;
import com.hishop.wine.repository.service.SmsRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 短信业务逻辑
 *
 * <AUTHOR>
 * @date : 2023/7/12
 */
@Service
@Slf4j
public class SmsBizImpl implements SmsBiz {

    @Resource
    private SmsSender smsSender;
    @Resource
    private SmsRecordService smsRecordService;
    @Resource
    private SmsRecordMobileService smsRecordMobileService;

    /**
     * 查询短信余额
     *
     * @return 短信余额
     */
    @Override
    public Integer getBalance() {
        return smsSender.getBalance();
    }

    /**
     * 发送短信 todo 目前用同步发送 后续优化
     *
     * @param sendSmsPO 发送短信参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendSms(SendSmsPO sendSmsPO) {
        // 添加短信主表记录
        SmsRecord smsRecord = BeanUtil.copyProperties(sendSmsPO, SmsRecord.class);
        smsRecord.setSendTime(new Date());
        smsRecordService.save(smsRecord);
        // 添加短信关联手机号记录
        List<SmsRecordMobile> recordMobileList = sendSmsPO.getMobileList().stream().map(mobile -> {
            SmsRecordMobile smsRecordMobile = new SmsRecordMobile();
            smsRecordMobile.setSmsRecordId(smsRecord.getId());
            smsRecordMobile.setMobile(mobile);
            return smsRecordMobile;
        }).collect(Collectors.toList());
        smsRecordMobileService.insertSmsRecordMobileBatch(recordMobileList);

        try {
            // 发送短信
            String[] mobiles = sendSmsPO.getMobileList().toArray(new String[sendSmsPO.getMobileList().size()]);
            smsSender.send(Channel.getEnum(sendSmsPO.getChannel()), sendSmsPO.getContent(), mobiles);

            smsRecord.setStatus(SmsEnum.Status.SUCCESS.getStatus());
        } catch (Exception e) {
            log.error("发送短信失败，手机号：{}，内容：{}", Arrays.toString(sendSmsPO.getMobileList().toArray()), sendSmsPO.getContent(), e);
            smsRecord.setStatus(SmsEnum.Status.FAIL.getStatus());
            smsRecord.setResult(e.getMessage());
            // throw new BusinessException(e.getMessage());
        } finally {
            smsRecordService.updateById(smsRecord);
        }
    }

    /**
     * 公共发送短信验证码
     *
     * @param mobile 手机号
     * @param key    验证key
     * @param tag    验证码标识
     */
    @Override
    public void sendSmsCode(String mobile, String key, String tag) {
        String code = RandomUtil.randomNumbers(4);

        log.info("======> 发送{}手机号验证码, 手机号: {}, 验证码: {}", tag, mobile, code);
        VerificationCodeUtil.setCode(key + mobile, code);
        String content = String.format("您的%s的验证码为：%s，五分钟内有效，请马上进行验证。若非本人操作，请忽略此短信。", tag, code);
        this.sendSms(SendSmsPO.of(SmsEnum.Channel.VERIFICATION.getValue(), content, Collections.singletonList(mobile)));
    }

    /**
     * 公共校验短信验证码
     *
     * @param mobile    手机号
     * @param key       验证key
     * @param inputCode 验证码
     */
    @Override
    public void checkSmsCode(String mobile, String key, String inputCode) {
        VerificationCodeUtil.checkCode(key + mobile, inputCode);
    }

    /**
     * 移除短信验证码
     *
     * @param mobile 手机号
     * @param key    验证Key
     */
    @Override
    public void removeSmsCode(String mobile, String key) {
        VerificationCodeUtil.removeCode(key + mobile);
    }
}
