package com.hishop.wine.biz;

import com.hishop.wine.model.po.payment.PaymentSettingSavePO;
import com.hishop.wine.model.vo.payment.PaymentSettingDetailVO;
import com.hishop.wine.model.vo.payment.PaymentSettingOfflineVO;
import com.hishop.wine.model.vo.payment.PaymentSettingSelectVO;
import com.hishop.wine.model.vo.payment.PaymentSettingVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 支付设置 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-07-18
 */

public interface PaymentSettingBiz {

    /**
     * 新增支付设置
     *
     * @param paymentSettingSavePO 新增支付设置参数
     */
    void create(PaymentSettingSavePO paymentSettingSavePO);

    /**
     * 编辑支付设置
     *
     * @param paymentSettingSavePO 编辑支付设置参数
     */
    void update(PaymentSettingSavePO paymentSettingSavePO);

    /**
     * 保存支付设置
     *
     * @param paymentSettingSavePO 保存支付设置参数
     */
    void save(PaymentSettingSavePO paymentSettingSavePO);

    /**
     * 删除支付设置
     *
     * @param id 支付设置id
     */
    void deleteById(Long id);

    /**
     * 查询支付设置详情
     *
     * @param id 支付设置id
     * @return 支付设置详情
     */
    PaymentSettingDetailVO detail(Long id);

    /**
     * 查询支付设置列表
     *
     * @return 支付设置列表
     */
    List<PaymentSettingVO> list();

    /**
     * 上传支付证书
     *
     * @param file 证书文件
     * @return 证书id
     */
    Long uploadCertificate(MultipartFile file);

    /**
     * 支付设置下拉
     *
     * @return 支付设置下拉
     */
    List<PaymentSettingSelectVO> listForSelect(String group);

    /**
     * 获取线下支付列表
     * @return 线下支付列表
     */
    List<PaymentSettingOfflineVO> listForOfflineSelect();
}