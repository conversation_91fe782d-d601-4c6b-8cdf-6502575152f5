package com.hishop.wine.biz.export.enums;

import com.hishop.common.export.model.BizType;

/**
 * TODO
 *
 * <AUTHOR>
 */
public enum BizTypeEnum implements BizType {

    /**
     * 产品导出
     */
    PRODUCT("PRODUCT", "产品列表"),

    /**
     * 会员列表
     */
    MEMBER("MEMBER", "会员列表");

    private final String type;
    private final String desc;

    BizTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    @Override
    public String type() {
        return type;
    }

    @Override
    public String desc() {
        return desc;
    }
}