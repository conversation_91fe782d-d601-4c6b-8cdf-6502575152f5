package com.hishop.wine.biz.export.wrapper;

import com.hishop.common.export.context.DataWrapper;
import com.hishop.wine.biz.export.model.ProductEO;
import com.hishop.wine.model.vo.member.MemberVO;

import java.util.List;

/**
 * @author: HuBiao
 * @date: 2023-07-05
 */
public class MemberWrapper implements DataWrapper<MemberVO> {

    private final List<MemberVO> dataList;

    public MemberWrapper(List<MemberVO> dataList) {
        this.dataList = dataList;
    }

    @Override
    public List<MemberVO> getDataList() {
        return this.dataList;
    }

    @Override
    public String sheetName() {
        return "会员列表";
    }
}
