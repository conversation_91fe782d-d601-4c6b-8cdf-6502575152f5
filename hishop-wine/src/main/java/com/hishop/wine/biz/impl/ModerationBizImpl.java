package com.hishop.wine.biz.impl;

import com.hishop.moderation.handler.Moderation;
import com.hishop.moderation.model.*;
import com.hishop.moderation.support.ModerationConfig;
import com.hishop.moderation.support.TextModerationConfig;
import com.hishop.wine.biz.BasicSettingBiz;
import com.hishop.wine.biz.ContentModerationBiz;
import com.hishop.wine.biz.ModerationBiz;
import com.hishop.wine.enums.BasicSettingEnum;
import com.hishop.wine.model.po.moderation.ContentModerationCreatePO;
import com.hishop.wine.model.vo.setting.ContentReviewSettingVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/8/3
 */
@Service
public class ModerationBizImpl implements ModerationBiz {

    @Resource
    private Moderation moderation;
    @Resource
    private BasicSettingBiz basicSettingBiz;
    @Resource
    private ContentModerationBiz contentModerationBiz;

    /**
     * 发起审核
     *
     * @param contentModerationCreatePO 审核入参
     * @return 审核id
     */
    @Override
    public Long check(ContentModerationCreatePO contentModerationCreatePO) {
        return contentModerationBiz.create(contentModerationCreatePO);
    }

    @Override
    public ModerationResult checkText(TextData textData) {
        TextModerationConfig config = (TextModerationConfig) getBaseConfig();
        if (config == null) {
            return ModerationResult.passed();
        }
        return moderation.checkText(config, textData);
    }

    @Override
    public ModerationResult checkImage(ImageData imageData) {
        TextModerationConfig config = (TextModerationConfig) getBaseConfig();
        if (config == null) {
            return ModerationResult.passed();
        }
        return moderation.checkText(config, null);
    }

    @Override
    public ModerationResult checkVideo(VideoData videoData) {
        return null;
    }

    @Override
    public ModerationResult checkAudio(AudioData audioData) {
        return null;
    }


    /**
     * 获取基础配置，如果返回null，则代表不需要进行审核
     * <AUTHOR>
     * @date 2023/8/3
     */
    private ModerationConfig getBaseConfig() {
        ContentReviewSettingVO config = basicSettingBiz.getSetting(BasicSettingEnum.CONTENT_REVIEW_SETTING);
        if (config == null) {
            return null;
        }
        if (!Boolean.TRUE.equals(config.getEnable())) {
            return null;
        }
        ModerationConfig moderationConfig = new TextModerationConfig();
        moderationConfig.setRegion("cn-east-3");
        moderationConfig.setAccessKey(config.getAppKey());
        moderationConfig.setSecreteKey(config.getAppSecret());
        return moderationConfig;
    }

}
