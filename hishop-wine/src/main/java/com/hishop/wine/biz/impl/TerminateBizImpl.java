package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.common.constants.LoginConstants;
import com.hishop.common.enums.IdentityTypeEnums;
import com.hishop.common.excel.read.ExcelReadHelper;
import com.hishop.common.excel.read.ReadResult;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.pojo.login.LoginUser;
import com.hishop.common.pojo.page.PageParam;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import com.hishop.common.util.LoginUserUtil;
import com.hishop.common.util.RedisUtil;
import com.hishop.wine.biz.TerminateBiz;
import com.hishop.wine.biz.excel.dealer.ImportAssist;
import com.hishop.wine.biz.excel.listener.TerminateReadListener;
import com.hishop.wine.common.enums.*;
import com.hishop.wine.enums.ModuleEnums;
import com.hishop.wine.model.po.terminate.*;
import com.hishop.wine.model.vo.terminate.TerminateDetailVo;
import com.hishop.wine.model.vo.terminate.TerminateFeignDetailVo;
import com.hishop.wine.model.vo.terminate.TerminateFeignVo;
import com.hishop.wine.model.vo.terminate.TerminateVo;
import com.hishop.wine.repository.entity.FileImportRecord;
import com.hishop.wine.repository.entity.Identity;
import com.hishop.wine.repository.entity.Terminate;
import com.hishop.wine.repository.entity.User;
import com.hishop.wine.repository.service.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 门店业务接口实现类
 * @author: chenzw
 * @date: 2024/7/6 15:31
 */
@Service
@RequiredArgsConstructor
public class TerminateBizImpl implements TerminateBiz {

    private final TerminateService terminateService;

    private final DistrictService districtService;

    private final UserService userService;

    private final DealerService dealerService;

    private final ImportAssist importAssist;

    private final FileImportRecordService fileImportRecordService;

    private final IdentityService identityService;

    @Override
    public void save(TerminateSavePo terminateSavePo) {
        //校验参数
        this.checkTerminateData(terminateSavePo);

        Terminate terminate = BeanUtil.copyProperties(terminateSavePo, Terminate.class);
        if (CollectionUtils.isNotEmpty(terminateSavePo.getTradeWeekList())) {
            terminate.setTradeWeek(JSON.toJSONString(terminateSavePo.getTradeWeekList()));
        }
        if (CollectionUtils.isNotEmpty(terminateSavePo.getPhotoList())) {
            terminate.setPhoto(JSON.toJSONString(terminateSavePo.getPhotoList()));
        }
        terminate.setSource(TerminateSource.ADD.getType());
        terminate.setSquare(terminateSavePo.getTerminateSquare() != null ? terminateSavePo.getTerminateSquare().getType() : null);
        terminate.setIzEnable(Boolean.TRUE);
        terminate.setAuditStatus(AuditStatus.PASS.getType());
        terminate.setProvinceName(districtService.getById(terminateSavePo.getProvinceId()).getName());
        terminate.setCityName(districtService.getById(terminateSavePo.getCityId()).getName());
        if (terminateSavePo.getDistrictId() != null) {
            terminate.setDistrictName(districtService.getById(terminateSavePo.getDistrictId()).getName());
        }
        terminateService.saveOrUpdate(terminate);
    }

    @Override
    public void minSave(TerminateMinSavePo savePo) {
        TerminateSavePo po = BeanUtil.copyProperties(savePo, TerminateSavePo.class);
        //校验参数
        this.checkTerminateData(po);

        Terminate terminate = BeanUtil.copyProperties(savePo, Terminate.class);
        Terminate queryTerminate = terminateService.getOne(new LambdaQueryWrapper<Terminate>().eq(Terminate::getPhone, savePo.getPhone())
                .eq(Terminate::getIzDelete, Boolean.FALSE)
                .last("limit 1"));

        if (queryTerminate != null) {
            terminate.setId(queryTerminate.getId());
        }
        terminate.setSource(TerminateSource.APP_REGISTER.getType());
        terminate.setSquare(savePo.getTerminateSquare() != null ? savePo.getTerminateSquare().getType() : null);
        terminate.setIzEnable(Boolean.TRUE);
        terminate.setAuditStatus(AuditStatus.PROCESSING.getType());
        terminate.setProvinceName(districtService.getById(savePo.getProvinceId()).getName());
        terminate.setCityName(districtService.getById(savePo.getCityId()).getName());
        if (savePo.getDistrictId() != null) {
            terminate.setDistrictName(districtService.getById(savePo.getDistrictId()).getName());
        }
        terminateService.saveOrUpdate(terminate);
    }

    @Override
    public void deleteById(List<Long> ids) {
        List<Terminate> terminateList = terminateService.listByIds(ids);
        if (CollectionUtils.isEmpty(terminateList)) {
            throw new BusinessException("门店不存在");
        }
        terminateList.forEach(terminate -> {
            terminate.setIzDelete(Boolean.TRUE);
            User user = userService.getUserByMobile(terminate.getPhone());
            if(user != null) {
                Identity identity = identityService.getOne(new LambdaQueryWrapper<Identity>().eq(Identity::getUserId, user.getId()).eq(Identity::getIzDelete, Boolean.FALSE).eq(Identity::getIdentityType, IdentityTypeEnums.TERMINAL.getType()).last("limit 1"));
                if(identity != null) {
                    clearToken(user.getId(), identity.getId());
                }
            }
        });
        terminateService.updateBatchById(terminateList);
    }

    private void clearToken(Long userId, Long identityId) {
        // 清空token
        String redisKey = String.format(LoginConstants.LOGIN_USER_MARK, userId, identityId);
        RedisUtil.del(redisKey);
    }

    @Override
    public void batchUpdateStatus(TerminateUpdateStatusPo updateStatusPo) {
        List<Terminate> terminateList = terminateService.listByIds(updateStatusPo.getIds());
        if (CollectionUtils.isEmpty(terminateList)) {
            throw new BusinessException("门店不存在");
        }
        terminateList.forEach(terminate -> {
            terminate.setIzEnable(updateStatusPo.getIzEnable());
            if(!updateStatusPo.getIzEnable()) {
                User user = userService.getUserByMobile(terminate.getPhone());
                if(user != null) {
                    Identity identity = identityService.getOne(new LambdaQueryWrapper<Identity>().eq(Identity::getUserId, user.getId()).eq(Identity::getIzDelete, Boolean.FALSE).eq(Identity::getIdentityType, IdentityTypeEnums.TERMINAL.getType()).last("limit 1"));
                    if(identity != null) {
                        clearToken(user.getId(), identity.getId());
                    }
                }
            }
        });
        terminateService.updateBatchById(terminateList);
    }

    @Override
    public PageResult<TerminateVo> pageList(TerminateQueryPo queryPo) {
        if (queryPo.getIzAudit() != null && queryPo.getIzAudit()) {
            queryPo.setSource(TerminateSource.APP_REGISTER.getType());
            queryPo.setAuditStatusList(Arrays.asList(AuditStatus.PROCESSING.getType(), AuditStatus.REJECT.getType()));
        } else {
            queryPo.setAuditStatusList(Collections.singletonList(AuditStatus.PASS.getType()));
        }
        Page<TerminateVo> terminateVoPage = terminateService.queryTerminatePageList(queryPo.buildPage(), queryPo);
        if (CollectionUtils.isNotEmpty(terminateVoPage.getRecords())) {
            //转换审核状态
            terminateVoPage.getRecords().forEach(terminateVo -> terminateVo.setAuditStatus(AuditStatus.getAuditStatusEnum(terminateVo.getAuditStatusType())));
        }
        return PageResultHelper.transfer(terminateVoPage, TerminateVo.class);
    }

    @Override
    public void importTerminate(MultipartFile file) {
        ReadResult<TerminateImportPo> importResult = ExcelReadHelper.read(file, TerminateImportPo.class, new TerminateReadListener(), 2);
        if (CollectionUtil.isEmpty(importResult.getDataList())) {
            throw new BusinessException("模板导入数据为空，请检查导入文件");
        }

        FileImportRecord fileImportRecord = new FileImportRecord();
        fileImportRecord.setFileName(file.getOriginalFilename());
        fileImportRecord.setImportType(FileImportType.TERMINATE_IMPORT.getType());
        fileImportRecord.setImportStatus(FileImportStatus.PROCESSING.getType());
        fileImportRecordService.save(fileImportRecord);

        importAssist.importData(fileImportRecord, FileImportType.TERMINATE_IMPORT, null, importResult);
    }

    @Override
    public TerminateDetailVo detail(Long id) {
        Terminate terminate = terminateService.getById(id);
        Assert.notNull(terminate, "门店不存在");

        TerminateDetailVo terminateDetailVo = BeanUtil.copyProperties(terminate, TerminateDetailVo.class);

        if (terminate.getTradeWeek() != null) {
            terminateDetailVo.setTradeWeekList(JSON.parseArray(terminate.getTradeWeek(), String.class));
        }
        if (terminate.getPhoto() != null) {
            terminateDetailVo.setPhotoList(JSON.parseArray(terminate.getPhoto(), String.class));
        }
        if (terminate.getSquare() != null) {
            terminateDetailVo.setTerminateSquare(TerminateSquare.getTerminateSquareEnum(terminate.getSquare()));
        }

        if (terminate.getBusinessUserId() != null) {
            terminateDetailVo.setBusinessUserName(userService.getById(terminate.getBusinessUserId()).getRealName());
            terminateDetailVo.setBusinessUserPhone(userService.getById(terminate.getBusinessUserId()).getMobile());

        }

        if (terminate.getDealerId() != null) {
            terminateDetailVo.setDealerName(dealerService.getById(terminate.getDealerId()).getDealerName());
        }
        return terminateDetailVo;
    }

    @Override
    public void audit(TerminateAuditPo auditPo) {
        auditPo.validate();
        Terminate terminate = terminateService.getById(auditPo.getId());
        Assert.notNull(terminate, "门店不存在");

        Terminate updateTerminate = BeanUtil.copyProperties(auditPo, Terminate.class);
        if (CollectionUtils.isNotEmpty(auditPo.getTradeWeekList())) {
            updateTerminate.setTradeWeek(JSON.toJSONString(auditPo.getTradeWeekList()));
        }
        if (CollectionUtils.isNotEmpty(auditPo.getPhotoList())) {
            updateTerminate.setPhoto(JSON.toJSONString(auditPo.getPhotoList()));
        }
        updateTerminate.setSquare(auditPo.getTerminateSquare() != null ? auditPo.getTerminateSquare().getType() : null);
        updateTerminate.setProvinceName(districtService.getById(auditPo.getProvinceId()).getName());
        updateTerminate.setCityName(districtService.getById(auditPo.getCityId()).getName());
        if (auditPo.getDistrictId() != null) {
            updateTerminate.setDistrictName(districtService.getById(auditPo.getDistrictId()).getName());
        }
        updateTerminate.setAuditStatus(auditPo.getIsPass() ? AuditStatus.PASS.getType() : AuditStatus.REJECT.getType());
        terminateService.updateById(updateTerminate);
    }

    @Override
    public List<TerminateFeignVo> queryTerminateList(List<Long> ids) {
        List<Terminate> terminateList = terminateService.listByIds(ids);
        return BeanUtil.copyToList(terminateList, TerminateFeignVo.class);
    }

    @Override
    public List<TerminateFeignVo> queryTerminateByCodeList(List<String> codes) {
        List<Terminate> terminateList = terminateService.list(new LambdaQueryWrapper<Terminate>()
                .in(Terminate::getCode, codes).eq(Terminate::getIzEnable, Boolean.TRUE)
                .eq(Terminate::getAuditStatus, AuditStatus.PASS.getType())
                .eq(Terminate::getIzDelete, Boolean.FALSE));
        return BeanUtil.copyToList(terminateList, TerminateFeignVo.class);
    }

    @Override
    public TerminateFeignDetailVo feignDetail(Long id) {
        return BeanUtil.copyProperties(this.detail(id), TerminateFeignDetailVo.class);
    }

    @Override
    public TerminateVo queryAuditStatusByPhone(String phone) {
        Terminate terminate = terminateService.getOne(new LambdaQueryWrapper<Terminate>().eq(Terminate::getPhone, phone).eq(Terminate::getIzDelete, false).last("limit 1"));
        if (terminate == null){
            return null;
        }
        TerminateVo vo = BeanUtil.copyProperties(terminate, TerminateVo.class);
        vo.setTerminateSquare(terminate.getSquare() != null ? TerminateSquare.getTerminateSquareEnum(terminate.getSquare()).name() : null);
        return vo;
    }

    @Override
    public List<TerminateVo> queryTerminateByCodeListMin() {
        LoginUser user = LoginUserUtil.getLoginUser();
        // 查询业务员
        Identity identity = identityService.getOne(new LambdaQueryWrapper<Identity>().eq(Identity::getUserId, user.getUserId())
                .eq(Identity::getModuleCode, ModuleEnums.feast_wine.getField())
                .eq(Identity::getIdentityType, 5).eq(Identity::getStatus, 1).eq(Identity::getIzDelete, false).last("limit 1"));
        if (identity == null) {
            throw new BusinessException("当前登录用户不是业务员");
        }
        List<Terminate> list = terminateService.list(new LambdaQueryWrapper<Terminate>().eq(Terminate::getIzDelete, false)
                .eq(Terminate::getAuditStatus, AuditStatus.PASS.getType()).eq(Terminate::getBusinessUserId, user.getUserId()));
        return BeanUtil.copyToList(list, TerminateVo.class);
    }

    @Override
    public Boolean queryUserIsSalesman(Long userId, String moduleCode) {
        Identity identity = identityService.getOne(new LambdaQueryWrapper<Identity>().eq(Identity::getUserId, userId)
                .eq(Identity::getModuleCode, moduleCode)
                .eq(Identity::getIdentityType, 5).eq(Identity::getStatus, 1).eq(Identity::getIzDelete, false).last("limit 1"));
        if (identity == null) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public TerminateFeignDetailVo queryTerminateByPhone(String phone) {
        Terminate terminate = terminateService.getOne(new LambdaQueryWrapper<Terminate>().eq(Terminate::getPhone, phone)
                .eq(Terminate::getIzDelete, false)
                .eq(Terminate::getAuditStatus, AuditStatus.PASS.getType())
                .eq(Terminate::getIzEnable, true).last("limit 1"));
        TerminateFeignDetailVo vo = BeanUtil.copyProperties(terminate, TerminateFeignDetailVo.class);
        vo.setTerminateSquare(terminate.getSquare() != null ? TerminateSquare.getTerminateSquareEnum(terminate.getSquare()).name() : null);
        return vo;
    }

    @Override
    public PageResult<TerminateVo> queryTerminateByUserPageList(PageParam pageParam) {
        LambdaQueryWrapper<Terminate> queryWrapper = new LambdaQueryWrapper<>(Terminate.class)
                .eq(Terminate::getIzDelete, Boolean.FALSE).eq(Terminate::getIzEnable, Boolean.TRUE)
                .eq(Terminate::getAuditStatus, AuditStatus.PASS.getType());

        queryWrapper.and(wrapper -> wrapper.eq(Terminate::getBusinessUserId, LoginUserUtil.getLoginUser().getUserId())
                .or()
                .in(Terminate::getDealerId, "select id from hishop_dealer where business_user_id = " + LoginUserUtil.getLoginUser().getUserId()));
        Page<Terminate> page = terminateService.page(pageParam.buildPage(), queryWrapper);
        return PageResultHelper.transfer(page, TerminateVo.class);
    }

    @Override
    public List<Long> listByBusinessUserId(Long businessUserId) {
       List<Terminate> list = terminateService.list(new LambdaQueryWrapper<Terminate>().eq(Terminate::getIzDelete, false).eq(Terminate::getBusinessUserId, businessUserId)
                .eq(Terminate::getAuditStatus, AuditStatus.PASS.getType()));
       List<Long> ids = Lists.newArrayList();
       if(CollectionUtils.isNotEmpty(list)) {
           ids = list.stream().map(Terminate::getId).collect(Collectors.toList());
       }
       return ids;
    }

    /**
     * 校验门店数据
     *
     * @param savePo 门店保存参数
     */
    private void checkTerminateData(TerminateSavePo savePo) {
        //校验参数
        savePo.validate();

        if (savePo.getId() != null) {
            Terminate terminate = terminateService.getById(savePo.getId());
            Assert.notNull(terminate, "门店不存在");
        }
        //校验门店编码和名称是否重复
        LambdaQueryWrapper<Terminate> queryWrapper = new LambdaQueryWrapper<>(Terminate.class)
                .eq(Terminate::getIzDelete, Boolean.FALSE)
                .eq(Terminate::getCode, savePo.getCode())
                .ne(savePo.getId() != null, Terminate::getId, savePo.getId());

        long count = terminateService.count(queryWrapper);
        if (count > 0) {
            throw new BusinessException("门店编码已存在");
        }

        queryWrapper = new LambdaQueryWrapper<>(Terminate.class)
                .eq(Terminate::getIzDelete, Boolean.FALSE)
                .eq(Terminate::getName, savePo.getName())
                .ne(savePo.getId() != null, Terminate::getId, savePo.getId());

        count = terminateService.count(queryWrapper);
        if (count > 0) {
            throw new BusinessException("门店名称已存在");
        }

        queryWrapper = new LambdaQueryWrapper<>(Terminate.class)
                .eq(Terminate::getIzDelete, Boolean.FALSE)
                .eq(Terminate::getPhone, savePo.getPhone())
                .ne(savePo.getId() != null, Terminate::getId, savePo.getId());

        count = terminateService.count(queryWrapper);
        if (count > 0) {
            throw new BusinessException("负责人手机号已存在");
        }
    }
}
