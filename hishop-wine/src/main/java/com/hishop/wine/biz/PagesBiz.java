package com.hishop.wine.biz;

import com.hishop.wine.model.po.pages.PagesCreatePO;
import com.hishop.wine.model.po.pages.PagesQueryPO;
import com.hishop.wine.model.po.pages.PagesUpdatePO;
import com.hishop.wine.model.vo.pages.PagesTabVO;
import com.hishop.wine.model.vo.pages.PagesVO;

import java.util.List;

import com.hishop.common.response.PageResult;

/**
 * 页面配置表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-07-07
 */

public interface PagesBiz {

    void create(PagesCreatePO createPO);

    void update(PagesUpdatePO updatePO);

    void deleteById(Long id);

    PagesVO detail(Long id);

    List<PagesVO> list(PagesQueryPO queryPO);

    PageResult<PagesVO> pageList(PagesQueryPO pagePO);

    /**
     * 查询tab集合
     *
     * @return tab集合
     */
    List<PagesTabVO> listTabs();

    /**
     * 查询模块下的页面集合
     *
     * @return 页面集合
     */
    List<PagesVO> listByModule();
}