package com.hishop.wine.biz;

import com.hishop.wine.model.po.basic.RoleCreatePO;
import com.hishop.wine.model.po.basic.RoleUpdatePO;
import com.hishop.wine.model.vo.basic.RoleCacheVO;
import com.hishop.wine.model.vo.basic.RoleVO;
import com.hishop.wine.repository.entity.Role;

import java.util.List;

/**
 * 角色表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
public interface RoleBiz {

    /**
     * 创建角色
     *
     * @param createPO 创建角色信息
     */
    void create(RoleCreatePO createPO);

    /**
     * 更新角色
     *
     * @param updatePO 更新角色信息
     */
    void update(RoleUpdatePO updatePO);

    /**
     * 删除角色
     *
     * @param id 角色Id
     */
    void deleteById(Long id);

    /**
     * 查询所有角色列表
     *
     * @return 角色列表
     */
    List<RoleCacheVO> listAll();

    /**
     * 检测并且返回角色信息
     *
     * @param roleId      角色id
     * @param checkStatus 是否检测状态
     * @return 角色信息
     */
    Role checkAndGetRole(Long roleId, Boolean checkStatus);

    /**
     * 查询角色详情
     *
     * @param id 角色id
     * @return 角色详情
     */
    RoleVO detail(Long id);

    /**
     * 判断角色是否有权限
     *
     * @param roleId     角色id
     * @param resourceId 资源id
     * @return 是否有权限
     */
    Boolean checkResourceAuth(Long roleId, Long resourceId);

    /**
     * 根据角色id 查询权限id
     *
     * @param roleId 角色id
     * @return 权限id集合
     */
    List<Long> listResourceIdsByRoleId(Long roleId);
}