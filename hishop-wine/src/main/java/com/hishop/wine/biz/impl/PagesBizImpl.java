package com.hishop.wine.biz.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.util.HeaderUtil;
import com.hishop.wine.common.enums.PagesTabEnum;
import com.hishop.wine.model.po.pages.PagesCreatePO;
import com.hishop.wine.model.po.pages.PagesQueryPO;
import com.hishop.wine.model.po.pages.PagesUpdatePO;
import com.hishop.wine.model.vo.pages.PagesTabVO;
import com.hishop.wine.repository.entity.Pages;
import com.hishop.wine.model.vo.pages.PagesVO;
import com.hishop.wine.biz.PagesBiz;
import com.hishop.wine.repository.service.PagesService;
import com.hishop.wine.repository.param.PagesParam;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.response.ResponseEnum;
import com.hishop.common.response.PageResultHelper;
import com.hishop.common.response.PageResult;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import cn.hutool.core.bean.BeanUtil;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


/**   
 * 页面配置表 业务逻辑实现类
 * @author: HuBiao
 * @date: 2023-07-07
 */

@Slf4j
@Service
public class PagesBizImpl implements PagesBiz {

    @Resource
    private PagesService pagesService;

    @Override
    public void create(PagesCreatePO createPO) {
        Pages entity = BeanUtil.copyProperties(createPO, Pages.class);
        pagesService.save(entity);
    }

    @Override
    public void update(PagesUpdatePO updatePO) {
        Pages entity = pagesService.getById(updatePO.getId());
        if(entity == null) {
            throw new BusinessException(ResponseEnum.NOT_FOUND);
        }
        Pages updateEntity = BeanUtil.copyProperties(updatePO, Pages.class);
        pagesService.updateById(updateEntity);
    }

    @Override
    public void deleteById(Long id) {
        pagesService.removeById(id);
    }

    @Override
    public PagesVO detail(Long id) {
        Pages entity = pagesService.getById(id);
        if(entity == null) {
            throw new BusinessException(ResponseEnum.NOT_FOUND);
        }
        return BeanUtil.copyProperties(entity, PagesVO.class);
    }

    @Override
    public List<PagesVO> list(PagesQueryPO qryPO) {
        PagesParam param = BeanUtil.copyProperties(qryPO, PagesParam.class);
        List<Pages> dbList = pagesService.qryList(param);
        return BeanUtil.copyToList(dbList, PagesVO.class);
    }

    @Override
    public PageResult<PagesVO> pageList(PagesQueryPO pagePO) {
        PagesParam param = BeanUtil.copyProperties(pagePO, PagesParam.class);
        Page<Pages> dbPage = pagesService.qryPage(pagePO.buildPage(), param);
        return PageResultHelper.transfer(dbPage, PagesVO.class);
    }

    /**
     * 查询tab集合
     *
     * @return tab集合
     */
    @Override
    public List<PagesTabVO> listTabs() {
        List<PagesTabVO> tabs = new ArrayList<>();
        PagesTabEnum[] tabEnums = PagesTabEnum.values();
        for (PagesTabEnum tabEnum : tabEnums) {
            List<PagesVO> pages = tabEnum.equals(PagesTabEnum.FUNCTION_PAGES) ? BeanUtil.copyToList(pagesService.list(), PagesVO.class) : Collections.emptyList();
            tabs.add(PagesTabVO.of(tabEnum.getType(), tabEnum.getName(), pages));
        }
        return tabs;
    }

    /**
     * 查询模块下的页面集合
     *
     * @return 页面集合
     */
    @Override
    public List<PagesVO> listByModule() {
        List<Pages> pages = pagesService.list(new LambdaQueryWrapper<Pages>().eq(Pages::getModuleCode, HeaderUtil.getModuleCode()));
        return BeanUtil.copyToList(pages, PagesVO.class);
    }
}