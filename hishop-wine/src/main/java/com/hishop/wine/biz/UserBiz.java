package com.hishop.wine.biz;

import com.hishop.common.enums.IdentityTypeEnums;
import com.hishop.common.pojo.login.LoginUser;
import com.hishop.common.response.PageResult;
import com.hishop.wine.model.po.basic.*;
import com.hishop.wine.model.po.login.UserRefreshTokenPO;
import com.hishop.wine.model.po.user.CheckRolePO;
import com.hishop.wine.model.po.user.PullNewSummaryPO;
import com.hishop.wine.model.po.user.UserGradePO;
import com.hishop.wine.model.po.user.UserGradeUpdatePO;
import com.hishop.wine.model.vo.basic.*;
import com.hishop.wine.model.vo.login.PcLoginVO;
import com.hishop.wine.model.vo.user.PullNewSummaryVO;
import com.hishop.wine.repository.entity.Identity;
import com.hishop.wine.repository.entity.MiniUser;
import com.hishop.wine.repository.entity.User;

import java.util.List;

/**
 * 用户表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
public interface UserBiz {

    /**
     * 获取用户身份信息
     *
     * @param username     用户名
     * @param identityType 身份类型
     * @return 用户信息(包含是否存在当前身份)
     */
    User getUserIdentityByUsername(String username, IdentityTypeEnums identityType);

    /**
     * 获取用户身份信息
     *
     * @param mobile       手机号
     * @param identityType 身份类型
     * @return 用户信息(包含是否存在当前身份)
     */
    User getUserIdentityByMobile(String mobile, IdentityTypeEnums identityType);

    /**
     * PC端 用户登录
     *
     * @param account      账号(手机号/邮箱/用户名)
     * @param password     密码
     * @param identityType 登录身份类型
     * @param checkPwd     是否检查密码
     * @param rememberMe   是否记住我
     * @return 登录结果
     */
    PcLoginVO pcLogin(String account, String password, Integer identityType, Boolean checkPwd, Boolean rememberMe);

    /**
     * PC端 微信扫码登录
     *
     * @param code         微信扫描二维码
     * @param state        校验参数
     * @param identityType 登录身份
     * @return 登录结果
     */
    PcLoginVO pcWxScanLogin(String code, String state, Integer identityType);

    /**
     * 用户登出
     * (小程序和pc端可共用)
     */
    void logout();

    /**
     * PC端 刷新token
     *
     * @param refreshTokenParam 刷新token参数
     * @return 登录信息
     */
    PcLoginVO pcRefreshToken(UserRefreshTokenPO refreshTokenParam);

    /**
     * 构建登录用户信息
     *
     * @param user     用户信息
     * @param identity 身份信息
     * @param miniUser 小程序用户信息
     * @return 登录用户信息
     */
    LoginUser buildLoginUser(User user, Identity identity, MiniUser miniUser);

    /**
     * 根据用户id列表查询用户信息
     *
     * @param userIds      用户id列表
     * @param identityType 身份类型
     * @return 用户信息列表
     */
    List<UserVO> listUserByIds(List<Long> userIds, Integer identityType);

    /**
     * 根据用户id查询用户信息
     * @param userId 用户id
     * @return
     */
    UserVO userById(Long userId);

    /**
     * 根据条件关联组合查询用户ID，可能会关联用户+身份+小程序用户
     *
     * <AUTHOR>
     * @date 2023/6/30
     */
    List<Long> qryUserId(UserCombineQryPO qryPo);

    /**
     * 发送验证邮箱
     *
     * @param email 邮箱
     */
    void sendBindEmail(String email);

    /**
     * 绑定邮箱
     *
     * @param emailBindPO 绑定邮箱参数
     */
    void bindEmail(EmailBindPO emailBindPO);

    /**
     * 解绑邮箱
     */
    void unBindEmail();

    /**
     * 发送绑定手机号验证码
     *
     * @param mobile 手机号
     */
    void sendBindMobile(String mobile);

    /**
     * 绑定手机号
     *
     * @param mobileBindPO 绑定手机号参数
     */
    void bindMobile(MobileBindPO mobileBindPO);

    /**
     * 解绑手机号
     */
    void unBindMobile();

    /**
     * 获取PC端账号信息
     *
     * @return 账号信息
     */
    PcAccountInfoVO getPcAccountInfo();

    /**
     * 更新账号信息
     *
     * @param updateAccountPO 更新账号信息参数
     */
    void updateAccountInfo(PcAccountUpdatePO updateAccountPO);

    /**
     * 修改密码发送短信验证码
     */
    void sendUpdPwdSms();

    /**
     * 修改密码
     *
     * @param updatePasswordPO 修改密码参数
     */
    void updatePassword(PasswordUpdatePO updatePasswordPO);

    /**
     * 发送忘记密码验证码
     *
     * @param mobile       手机号
     * @param identityType 身份类型
     */
    void sendForForgetPwd(String mobile, Integer identityType);

    /**
     * 忘记密码
     *
     * @param passwordForgetPO 忘记密码参数
     */
    void forgetPassword(PasswordForgetPO passwordForgetPO);

    /**
     * 查询用户菜单权限
     *
     * @return 菜单权限
     */
    UserResourceVO menuAuth();

    /**
     * 执行注册成功处理
     *
     * @param identityId 身份id
     */
    void registerSuccessHandel(Long identityId);

    /**
     * 检测是否存在角色
     *
     * @param checkRolePO 查询参数
     * @return true-存在 false-没有
     */
    Boolean checkRole(CheckRolePO checkRolePO);

    /**
     * 获取拉新数据
     *
     * @param pullNewSummaryPO 查询拉新参数
     * @return 拉新数据
     */
    List<PullNewSummaryVO> getPullNewSummary(PullNewSummaryPO pullNewSummaryPO);

    /**
     * 用户打标
     *
     * @param userId 用户id
     * @param tagIds 标签id列表
     */
    void markTags(Long userId, List<Long> tagIds);

    /**
     * 通过手机号查询，如果不存在则创建
     */
    UserVO getOrCreate(String mobile, String userName);

    /**
     * 通过手机号 查询用户
     * @param phone
     * @return
     */
    UserVO getByPhone(String phone);

    /**
     * 通过会员等级 查询用户 是否存在
     * @param gradeId
     * @return
     */
    Boolean existsByGrade(Long gradeId);

    /**
     * 修改会员等级
     * @param userGradePO
     * @return
     */
    Boolean updateGrade(UserGradeUpdatePO userGradePO);


}