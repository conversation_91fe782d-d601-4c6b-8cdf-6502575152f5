package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.constants.SystemConstants;
import com.hishop.common.util.RedisUtil;
import com.hishop.wine.biz.BasicSettingBiz;
import com.hishop.wine.biz.DecorateBiz;
import com.hishop.wine.biz.MiniConfigBiz;
import com.hishop.wine.common.helper.AuthModuleHelper;
import com.hishop.wine.enums.BasicSettingEnum;
import com.hishop.wine.enums.DecorateTypeEnums;
import com.hishop.wine.model.vo.MiniInitConfigVO;
import com.hishop.wine.model.vo.PcInitConfigVO;
import com.hishop.wine.model.vo.decorate.DecorateVO;
import com.hishop.wine.model.vo.login.AuthModuleVO;
import com.hishop.wine.model.vo.module.ModuleBusinessAppVo;
import com.hishop.wine.model.vo.setting.PointsSettingVO;
import com.hishop.wine.model.vo.setting.SystemSettingVO;
import com.hishop.wine.repository.entity.ModuleBusiness;
import com.hishop.wine.repository.service.ModuleBusinessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 小程序配置业务逻辑实现类
 * @author: chenpeng
 * @date: 2023-07-11
 */

@Slf4j
@Service
public class MiniConfigBizImpl implements MiniConfigBiz {

    @Resource
    private DecorateBiz decorateBiz;
    @Resource
    private BasicSettingBiz basicSettingBiz;
    @Resource
    private AuthModuleHelper authModuleHelper;

    @Value("${nfs.obs.domain}")
    private String obsPrefixUrl;

    @Value("${hishop.h5.domain}")
    private String h5ProjectDomain;

    @Value("${hishop.default.path}")
    private String defaultPath;

    @Resource
    private ModuleBusinessService moduleBusinessService;

    public static String DECORATE_KEY = "decorates:";

    @Override
    public MiniInitConfigVO getInitConfig(String appId) {
        MiniInitConfigVO miniInitConfigVO = new MiniInitConfigVO();
        //获取站点信息，包括站点名称、logo,obs地址
        SystemSettingVO systemSettingVO = basicSettingBiz.getSetting(BasicSettingEnum.SYSTEM_SETTING);
        miniInitConfigVO.setWebName(systemSettingVO.getSystemName());
        miniInitConfigVO.setWebLogo(systemSettingVO.getLogo());
        miniInitConfigVO.setObsPrefixUrl("https://" + obsPrefixUrl);
        miniInitConfigVO.setH5ProjectDomain(h5ProjectDomain);
        //从授权平台获取授权列表
        AuthModuleVO authModuleVO = authModuleHelper.getAuthModule();
        //获取小程序装修信息
        List<DecorateVO> decorates = RedisUtil.get(DECORATE_KEY + appId);
        if(CollectionUtils.isEmpty(decorates)) {
            decorates = decorateBiz.listDecorateByAppId(appId);
            RedisUtil.set(DECORATE_KEY + appId, decorates, 60 * 60);
        }
        if(CollectionUtil.isNotEmpty(decorates)) {
            List<DecorateVO> homePages = decorates.stream().filter(e -> e.getDecorateType().equals(DecorateTypeEnums.HOME_PAGE.getType())
                    && e.getIzDefault()).collect(Collectors.toList());
            List<DecorateVO> theme = decorates.stream().filter(e -> e.getDecorateType().equals(DecorateTypeEnums.THEME.getType())).collect(Collectors.toList());
            List<DecorateVO> navigation = decorates.stream().filter(e -> e.getDecorateType().equals(DecorateTypeEnums.NAVIGATION.getType())).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(homePages)) {
                miniInitConfigVO.setHomePageModuleCode(homePages.get(0).getModuleCode());
            }
            if(CollectionUtil.isNotEmpty(theme)) {
                miniInitConfigVO.setThemeJson(theme.get(0).getSettingJson());
            }
            if(CollectionUtil.isNotEmpty(navigation)) {
                miniInitConfigVO.setNavigationJson(navigation.get(0).getSettingJson());
            }
        }
        if(SystemConstants.YES.equals(authModuleVO.getPointsMall())) {
            //积分名称配置
            PointsSettingVO pointsSettingVO = basicSettingBiz.getSetting(BasicSettingEnum.POINTS_SETTING);
            if (pointsSettingVO != null) {
                miniInitConfigVO.setPointsName(pointsSettingVO.getPointsName());
            }
        }
        return miniInitConfigVO;
    }

    @Override
    public PcInitConfigVO getInitForPC() {
        PcInitConfigVO pcInitConfigVO = new PcInitConfigVO();
        //获取站点信息，包括站点名称、logo,obs地址
        SystemSettingVO systemSettingVO = basicSettingBiz.getSetting(BasicSettingEnum.SYSTEM_SETTING);
        pcInitConfigVO.setWebName(systemSettingVO.getSystemName());
        pcInitConfigVO.setWebLogo(systemSettingVO.getLogo());
        pcInitConfigVO.setObsPrefixUrl("https://" + obsPrefixUrl);
        pcInitConfigVO.setDefaultPagePath(defaultPath);
        return pcInitConfigVO;
    }
}
