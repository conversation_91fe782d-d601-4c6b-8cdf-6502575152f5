package com.hishop.wine.biz;


import com.hishop.common.response.PageResult;
import com.hishop.wine.model.po.transaction.*;
import com.hishop.wine.model.vo.transaction.*;
import com.hishop.wine.repository.entity.Transaction;

/**
 * 交易流水表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-06-28
 */
public interface TransactionBiz {

    /**
     * 发起支付
     *
     * @param payPO 支付参数
     * @return 支付返回值
     */
    TransactionPayVO pay(TransactionPayPO payPO);

    /**
     * 判断是否支付中
     * @param payPO
     * @return
     */
    Boolean queryPaying(TransactionQueryPO payPO);

    /**
     * 支付回调
     *
     * @param moduleCode 模块编码
     * @param notifyData 回调参数
     * @return 回调结果
     */
    String notifyPay(String moduleCode, String notifyData);

    /**
     * 发起退款
     *
     * @param refundPO 发起退款参数
     * @return 退款返回值
     */
    TransactionRefundVO refund(TransactionRefundPO refundPO);

    /**
     * 退款回调
     *
     * @param moduleCode 模块编码
     * @param notifyData 回调参数
     * @return 回调结果
     */
    String notifyRefund(String moduleCode, String notifyData);

    /**
     * 企业付款到零钱
     *
     * @param entPayPO 付款参数
     * @return 付款返回值
     */
    TransactionEntPayVO entPay(TransactionEntPayPO entPayPO);

    /**
     * 检查企业打款交易状态
     */
    void checkEntPayResult();

    /**
     * 交易查询
     *
     * @param queryPO 查询参数
     * @return 交易流水
     */
    PageResult<TransactionInfoVO> queryTransaction(TransactionQueryPO queryPO);

    /**
     * 保存交易流水
     * @param transactionPO
     */
    void save(TransactionPO transactionPO);

    /**
     * 查询交易中与交易成功的数据（只能有一条）
     * @param payPO
     * @return
     */
    TransactionVO getTransaction(TransactionQueryPO payPO);

    /**
     * 更新状态
     * @param po
     * @return
     */
    Boolean updateTransactionStatus(TransactionUpdateStatusPO po);

    /**
     * 按交易流水查交易
     * @param payPO
     * @return
     */
    TransactionVO getTransactionByNo(TransactionQueryPO payPO);

    /**
     * 按id查交易
     * @param payPO
     * @return
     */
    TransactionVO getTransactionById(TransactionQueryPO payPO);
}