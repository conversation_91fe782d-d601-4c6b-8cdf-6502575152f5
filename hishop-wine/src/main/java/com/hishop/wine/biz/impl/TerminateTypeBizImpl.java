package com.hishop.wine.biz.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.pojo.page.PageParam;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import com.hishop.wine.biz.TerminateTypeBiz;
import com.hishop.wine.model.po.terminate.TerminateTypeSavePo;
import com.hishop.wine.model.vo.terminate.TerminateTypeVo;
import com.hishop.wine.repository.entity.Terminate;
import com.hishop.wine.repository.entity.TerminateType;
import com.hishop.wine.repository.service.TerminateService;
import com.hishop.wine.repository.service.TerminateTypeService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * @description: 门店类型业务实现类
 * @author: chenzw
 * @date: 2024/7/6 14:58
 */
@Service
@RequiredArgsConstructor
public class TerminateTypeBizImpl implements TerminateTypeBiz {

    private final TerminateTypeService terminateTypeService;

    private final TerminateService terminateService;

    @Override
    public void save(TerminateTypeSavePo terminateTypeSavePo) {
        long count = terminateTypeService.count(new LambdaQueryWrapper<>(TerminateType.class)
                .eq(TerminateType::getName, terminateTypeSavePo.getName())
                .ne(terminateTypeSavePo.getId() != null, TerminateType::getId, terminateTypeSavePo.getId())
                .eq(TerminateType::getIzDelete, false));

        if (count > 0) {
            throw new BusinessException("门店类型名称已存在");
        }

        TerminateType terminateType = new TerminateType();
        terminateType.setId(terminateTypeSavePo.getId());
        terminateType.setName(terminateTypeSavePo.getName());
        terminateTypeService.saveOrUpdate(terminateType);
    }

    @Override
    public void deleteById(Long id) {
        TerminateType terminateType = terminateTypeService.getById(id);
        Assert.notNull(terminateType, "门店类型不存在");

        long count = terminateService.count(new LambdaQueryWrapper<Terminate>()
                .eq(Terminate::getTerminateTypeId, id)
                .eq(Terminate::getIzDelete, false));

        if (count > 0) {
            throw new BusinessException("门店类型下存在门店，不能删除");
        }
        terminateTypeService.removeById(id);
    }

    @Override
    public PageResult<TerminateTypeVo> pageList(PageParam pageParam) {
        LambdaQueryWrapper<TerminateType> wrapper = new LambdaQueryWrapper<TerminateType>()
                .eq(TerminateType::getIzDelete, false);
        wrapper.orderByDesc(TerminateType::getId);
        Page<TerminateType> page = terminateTypeService.page(pageParam.buildPage(), wrapper);

        PageResult<TerminateTypeVo> pageResult = PageResultHelper.transfer(page, TerminateTypeVo.class);

        if (CollectionUtils.isNotEmpty(pageResult.getList())) {
            pageResult.getList().forEach(terminateType -> {
                long terminateNumber = terminateService.count(new LambdaQueryWrapper<Terminate>()
                        .eq(Terminate::getTerminateTypeId, terminateType.getId())
                        .eq(Terminate::getIzDelete, false));
                terminateType.setTerminateNumber((int) terminateNumber);
            });
        }
        return pageResult;
    }
}
