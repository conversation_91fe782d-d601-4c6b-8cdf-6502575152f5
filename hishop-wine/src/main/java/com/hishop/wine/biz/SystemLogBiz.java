package com.hishop.wine.biz;

import com.hishop.common.response.PageResult;
import com.hishop.log.model.OperationRecord;
import com.hishop.wine.model.po.log.SystemLogQueryPO;
import com.hishop.wine.model.vo.log.SystemLogDetailVO;
import com.hishop.wine.model.vo.log.SystemLogVO;

/**
 * 操作日志表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-08-01
 */

public interface SystemLogBiz {

    /**
     * 分页查询日志记录
     *
     * @param pagePO 筛选参数
     * @return 日志记录
     */
    PageResult<SystemLogVO> pageList(SystemLogQueryPO pagePO);

    /**
     * 日志详情
     *
     * @param id 日志id
     * @return 日志详情
     */
    SystemLogDetailVO detail(Long id);

    /**
     * 记录操作日志
     *
     * @param record 操作日志
     */
    void createOperationLog(OperationRecord record);
}