package com.hishop.wine.biz;

import com.hishop.common.response.PageResult;
import com.hishop.wine.model.po.member.MemberQueryPO;
import com.hishop.wine.model.po.member.MemberSetRankBatchPO;
import com.hishop.wine.model.po.member.MemberSetRankPO;
import com.hishop.wine.model.vo.member.MemberSelectVO;
import com.hishop.wine.model.vo.member.MemberVO;

import java.util.List;

/**
 * 会员管理
 *
 * <AUTHOR>
 * @date : 2023/7/25
 */
public interface MemberBiz {

    /**
     * 分页查询会员列表
     *
     * @param memberQueryPO 查询条件
     * @return 会员列表
     */
    PageResult<MemberVO> pageList(MemberQueryPO memberQueryPO);

    /**
     * 会员下拉选择
     *
     * @param userId    用户id
     * @param searchKey 筛选值
     * @return 会员列表
     */
    List<MemberSelectVO> listForSelect(Long userId, String searchKey);

    /**
     * 设置会员头衔
     *
     * @param memberSetRankPO 设置会员头衔参数
     */
    void setRank(MemberSetRankPO memberSetRankPO);

    /**
     * 会员详情
     *
     * @param id 会员id
     * @return 会员详情
     */
    MemberVO detail(Long id);

    /**
     * 批量设置头衔
     *
     * @param memberSetRankBatchPO 批量设置头衔参数
     */
    void setRankBatch(MemberSetRankBatchPO memberSetRankBatchPO);
}
