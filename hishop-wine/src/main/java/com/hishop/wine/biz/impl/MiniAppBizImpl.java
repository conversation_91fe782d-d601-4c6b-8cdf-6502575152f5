package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.util.RedisUtil;
import com.hishop.setting.config.annotation.RefreshSetting;
import com.hishop.wine.biz.DecorateBiz;
import com.hishop.wine.biz.MiniAppBiz;
import com.hishop.wine.biz.ModuleBiz;
import com.hishop.wine.biz.WechatCodeBiz;
import com.hishop.wine.common.enums.DecorateEnum;
import com.hishop.wine.common.enums.LinkEnum;
import com.hishop.wine.common.enums.PaymentEnum;
import com.hishop.wine.common.utils.OkHttpClientUtil;
import com.hishop.wine.constants.BasicCacheConstants;
import com.hishop.wine.constants.SettingConstants;
import com.hishop.wine.enums.ModuleEnums;
import com.hishop.wine.enums.OfflineChannel;
import com.hishop.wine.enums.WechatEnum;
import com.hishop.wine.model.po.decorate.DecorateQueryPO;
import com.hishop.wine.model.po.miniApp.MiniAppCreatePO;
import com.hishop.wine.model.po.miniApp.MiniAppUpdatePO;
import com.hishop.wine.model.po.miniApp.uploadShippingInfo.UploadShippingInfoPo;
import com.hishop.wine.model.po.wechat.WechatCodeCreatePO;
import com.hishop.wine.model.vo.miniApp.*;
import com.hishop.wine.model.vo.module.ModuleVO;
import com.hishop.wine.repository.dto.miniApp.MiniAppDTO;
import com.hishop.wine.repository.dto.miniApp.MiniAppModuleDTO;
import com.hishop.wine.repository.dto.miniApp.MiniAppTokenDto;
import com.hishop.wine.repository.dto.miniApp.MiniAppWxResultDto;
import com.hishop.wine.repository.entity.*;
import com.hishop.wine.repository.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 小程序表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-07-18
 */

@Slf4j
@Service
public class MiniAppBizImpl implements MiniAppBiz {

    @Value("${wx.mini.tokenUrl}")
    private String tokenUrl;

    @Value("${wx.mini.uploadUrl}")
    private String uploadUrl;

    @Resource
    private MiniAppService miniAppService;
    @Resource
    private ModuleService moduleService;
    @Resource
    private DecorateBiz decorateBiz;
    @Resource
    private WechatCodeBiz wechatCodeBiz;
    @Resource
    private ModuleBiz moduleBiz;
    @Resource
    private MiniAppPaymentService miniAppPaymentService;
    @Resource
    private PaymentSettingService paymentSettingService;
    @Resource
    private PagesService pagesService;
    @Resource
    private DecorateService decorateService;
    @Resource
    private RedissonClient redissonClient;

    public static final String LOCK_ACCESS_TOKEN_KEY = "lock:access:token:";

    public static final String ACCESS_TOKEN_KEY = "access:token:";

    private final static String WX_MINI_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";

    /**
     * 查询小程序列表
     *
     * @return 小程序列表
     */
    @Override
    public List<MiniAppVO> list() {
        List<MiniAppDTO> miniAppDTOS = miniAppService.listMiniApp();
        miniAppDTOS.forEach(item -> item.setSettingModuleCode(CharSequenceUtil.toCamelCase(item.getSettingModuleCode())));
        return BeanUtil.copyToList(miniAppDTOS, MiniAppVO.class);
    }

    /**
     * 绑定小程序
     *
     * @param createPO 绑定小程序参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RefreshSetting(names = {SettingConstants.WX_MAPPING_SETTING, SettingConstants.WX_MA_SETTING})
    public void create(MiniAppCreatePO createPO) {
        checkExist(createPO.getAppId());

        MiniApp miniApp = BeanUtil.copyProperties(createPO, MiniApp.class);
        miniAppService.save(miniApp);

        // 模块绑定小程序
        moduleService.bindMiniApp(miniApp.getAppId(), createPO.getModuleCodes());

        // 绑定支付方式
        bindPayment(miniApp.getAppId(), createPO.getPaymentIds(), createPO.getOfflineIds());
    }

    /**
     * 更换小程序
     *
     * @param updatePO 更换小程序参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RefreshSetting(names = {SettingConstants.WX_MAPPING_SETTING, SettingConstants.WX_MA_SETTING})
    public void update(MiniAppUpdatePO updatePO) {
        MiniApp miniApp = miniAppService.getById(updatePO.getId());
        Assert.isTrue(ObjectUtil.isNotNull(miniApp), "小程序不存在");

        if (StrUtil.isNotBlank(updatePO.getAppId())) {
            checkExist(updatePO.getAppId());
        }
        // 验证appId和secret可用
        checkAppIdAndSecret(updatePO.getAppId(), updatePO.getAppSecret());

        // 查询之前绑定的模块
        List<ModuleInfo> moduleInfoList = moduleService.list(new LambdaQueryWrapper<ModuleInfo>().eq(ModuleInfo::getAppId, miniApp.getAppId()));

        BeanUtil.copyProperties(updatePO, miniApp);
        miniAppService.updateById(miniApp);

        // 将原来绑定的模块换绑
        moduleService.bindMiniApp(miniApp.getAppId(), moduleInfoList.stream().map(ModuleInfo::getModuleCode).collect(Collectors.toList()));

        // 更换装修绑定
        decorateService.update(new LambdaUpdateWrapper<Decorate>()
                .eq(Decorate::getAppId, miniApp.getAppId()).set(Decorate::getAppId, updatePO.getAppId()));
    }

    /**
     * 查询小程序详情
     *
     * @param id 主键
     * @return 小程序详情
     */
    @Override
    public MiniAppDetailVO detail(Long id) {
        MiniApp miniApp = miniAppService.getById(id);
        Assert.isTrue(ObjectUtil.isNotNull(miniApp), "小程序不存在");

        MiniAppDetailVO miniAppDetailVO = BeanUtil.copyProperties(miniApp, MiniAppDetailVO.class);
        String bindModuleNames = miniAppService.getBindModuleNames(miniApp.getAppId());
        miniAppDetailVO.setModuleNames(bindModuleNames);
        // todo 暂时无法获取
        miniAppDetailVO.setVersion("-");
        List<HomePageAndStoreBarSelectVO> moduleSelect = listHomePageAndStoreBar(Arrays.asList(miniApp.getSettingModuleCode()));
        HomePageAndStoreBarSelectVO module = moduleSelect.stream().filter(select -> select.getCode().equals(miniApp.getSettingModuleCode())).findFirst().orElse(null);
        miniAppDetailVO.setModuleSettingName(ObjectUtil.isNotNull(module) ? module.getName() : StrUtil.EMPTY);

        // 获取小程序装修信息
        DecorateQueryPO decorateQueryPO = new DecorateQueryPO();
        decorateQueryPO.setAppId(miniApp.getAppId());
        decorateQueryPO.setDecorateType(DecorateEnum.Type.homePage.name());
        miniAppDetailVO.setDecorateVO(decorateBiz.detail(decorateQueryPO));

        // 查询底部导航数据
        decorateQueryPO.setDecorateType(DecorateEnum.Type.navigation.name());
        miniAppDetailVO.setNavigation(decorateBiz.detail(decorateQueryPO));

        // 生成小程序二维码
        miniAppDetailVO.setQrCode(getQrCode(miniApp));
        return miniAppDetailVO;
    }

    /**
     * 小程序和底部导航下拉
     *
     * @param selectCodes 选中的模块
     * @return 小程序和底部导航下拉
     */
    @Override
    public List<HomePageAndStoreBarSelectVO> listHomePageAndStoreBar(List<String> selectCodes) {
        List<HomePageAndStoreBarSelectVO> selectList = new ArrayList<>();
        HomePageAndStoreBarSelectVO basicSelect = new HomePageAndStoreBarSelectVO();
        basicSelect.setCode(ModuleEnums.basic_system.name());
        basicSelect.setName("独立设置");
        selectList.add(basicSelect);

        if (CollectionUtils.isNotEmpty(selectCodes)) {
            List<ModuleVO> moduleVOS = moduleBiz.listAuthModule().stream().filter(moduleVO -> selectCodes.contains(moduleVO.getCode())).collect(Collectors.toList());
            List<HomePageAndStoreBarSelectVO> filterSelectVos = BeanUtil.copyToList(moduleVOS, HomePageAndStoreBarSelectVO.class);
            filterSelectVos.forEach(select -> select.setName(String.format("使用「%s」的设置", select.getName())));
            selectList.addAll(filterSelectVos);
        }
        return selectList;
    }

    /**
     * 删除小程序
     *
     * @param id 小程序id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RefreshSetting(names = {SettingConstants.WX_MAPPING_SETTING, SettingConstants.WX_MA_SETTING})
    public void delete(Long id) {
        MiniApp miniApp = miniAppService.getById(id);
        Assert.isTrue(ObjectUtil.isNotNull(miniApp), "小程序不存在");
        miniAppService.removeById(id);

        // 删除商家账户绑定
        miniAppPaymentService.remove(new LambdaQueryWrapper<MiniAppPayment>().eq(MiniAppPayment::getAppId, miniApp.getAppId()));
    }

    /**
     * 查询链接目标
     *
     * @param linkType 链接类型
     * @return 链接目标
     */
    @Override
    public List<LinkTargetVO> listLinkTarget(String linkType) {
        LinkEnum.LinkType linkTypeEnum = LinkEnum.LinkType.valueOf(linkType);
        Assert.isTrue(ObjectUtil.isNotNull(linkTypeEnum), "链接类型不存在");

        // 从缓存中获取
        String redisKey = String.format(BasicCacheConstants.MINI_LINKS_CACHE, linkType);
        List<LinkTargetVO> cacheObj = RedisUtil.get(redisKey);
        if (ObjectUtil.isNotNull(cacheObj)) {
            return cacheObj;
        }

        // 将所有功能页面查询出来, 按照模块分组 模块编码为空的则标识所有模块均可以使用
        Map<String, List<Pages>> pagesMap = pagesService.list().stream()
                .filter(page -> StrUtil.isEmpty(page.getLinkType()) || linkType.equals(page.getLinkType()))
                .collect(Collectors.groupingBy(Pages::getModuleCode));
        // 查询已授权的模块
        List<String> authModules = moduleBiz.listAuthModuleCodes();

        List<LinkTargetVO> linkTargetList = new ArrayList<>();
        // 查询小程序和模块的绑定关系
        List<MiniAppModuleDTO> miniAppModuleList = miniAppService.listMiniAppModule();
        // 筛选出已经授权的模块
        miniAppModuleList = miniAppModuleList.stream().filter(miniAppModule -> authModules.contains(miniAppModule.getModuleCode())).collect(Collectors.toList());

        Map<Long, List<MiniAppModuleDTO>> miniAppModuleMap = miniAppModuleList.stream().collect(Collectors.groupingBy(MiniAppModuleDTO::getId));
        miniAppModuleMap.forEach((id, miniAppList) -> {
            LinkTargetVO linkTarget = new LinkTargetVO();
            linkTarget.setId(String.valueOf(id));
            linkTarget.setAppId(miniAppList.get(0).getAppId());
            linkTarget.setLinkName(miniAppList.get(0).getAppName());
            linkTarget.setTargetType(LinkEnum.TargetType.SYSTEM_MINI_APP.getType());

            // 转换成模块列表
            List<LinkModuleVO> moduleList = miniAppList.stream().map(miniApp -> {
                LinkModuleVO moduleVO = new LinkModuleVO();
                moduleVO.setName(miniApp.getModuleName());
                moduleVO.setCode(miniApp.getModuleCode());

                // 查询模块绑定的tab
                List<LinkTabVO> tabList = listLinkTab(linkTypeEnum, moduleVO.getCode());
                LinkTabVO functionTab = tabList.stream()
                        .filter(item -> item.getCode().equals(LinkEnum.Tab.FUNCTION_PAGES.getCode())).findFirst().orElse(null);
                if (ObjectUtil.isNotNull(functionTab)) {
                    List<LinkPagesVO> pagesList = new ArrayList<>();
                    pagesList.addAll(BeanUtil.copyToList(pagesMap.getOrDefault(StrUtil.EMPTY, Collections.emptyList()), LinkPagesVO.class));
                    pagesList.addAll(BeanUtil.copyToList(pagesMap.getOrDefault(moduleVO.getCode(), Collections.emptyList()), LinkPagesVO.class));
                    functionTab.setPagesList(pagesList);
                }
                moduleVO.setTabList(tabList);
                return moduleVO;
            }).collect(Collectors.toList());

            linkTarget.setModuleList(moduleList);
            linkTargetList.add(linkTarget);
        });

        // 添加其他小程序和公众号文字
        linkTargetList.addAll(otherLinkBuilder(linkTypeEnum));

        RedisUtil.setDefaultTime(redisKey, linkTargetList);
        return linkTargetList;
    }

    /**
     * 查询链接tab
     *
     * @param linkType   链接类型
     * @param moduleCode 模块编码
     * @return 链接tab
     */
    @Override
    public List<LinkTabVO> listLinkTab(LinkEnum.LinkType linkType, String moduleCode) {
        return Arrays.stream(LinkEnum.Tab.values())
                .filter(tab -> CollectionUtils.isEmpty(tab.getSupportModuleCodes()) || tab.getSupportModuleCodes().contains(moduleCode))
                .filter(tab -> CollectionUtils.isEmpty(tab.getSupportTypes()) || tab.getSupportTypes().contains(linkType))
                .map(tab -> {
                    LinkTabVO linkTab = new LinkTabVO();
                    linkTab.setCode(tab.getCode());
                    linkTab.setName(tab.getName());
                    return linkTab;
                }).collect(Collectors.toList());
    }

    /**
     * 添加其他小程序和公众号选项
     *
     * @param linkTypeEnum 链接类型
     * @return 其他小程序和公众号选项
     */
    private List<LinkTargetVO> otherLinkBuilder(LinkEnum.LinkType linkTypeEnum) {
        List<LinkEnum.TargetType> targetTypeList = Arrays.asList(LinkEnum.TargetType.values());

        targetTypeList = targetTypeList.stream().filter(targetType -> targetType.getSupportTypes().contains(linkTypeEnum)).collect(Collectors.toList());
        return targetTypeList.stream().map(targetType -> {
            LinkTargetVO targetVO = new LinkTargetVO();
            targetVO.setId(targetType.getType());
            targetVO.setTargetType(targetType.getType());
            targetVO.setLinkName(targetType.getName());
            return targetVO;
        }).collect(Collectors.toList());
    }

    /**
     * 生成小程序二维码
     *
     * @param miniApp 小程序信息
     * @return 小程序二维码url
     */
    private String getQrCode(MiniApp miniApp) {
        WechatCodeCreatePO wechatCodeCreatePO = new WechatCodeCreatePO();
        wechatCodeCreatePO.setCodeType(WechatEnum.WxCodeTypeEnum.MINI_APP.getCode());
        wechatCodeCreatePO.setCodeFrom(WechatEnum.WxCodeFromEnum.BASIC_SYSTEM.name());
        wechatCodeCreatePO.setAppId(miniApp.getAppId());
        return wechatCodeBiz.genMaCode(wechatCodeCreatePO);
    }

    /**
     * 检查小程序是否存在
     *
     * @param appId 小程序appId
     */
    private void checkExist(String appId) {
        long count = miniAppService.count(new LambdaQueryWrapper<MiniApp>().eq(MiniApp::getAppId, appId));
        Assert.isTrue(count == 0, "小程序已存在");
    }

    /**
     * 绑定支付设置
     *
     * @param appId      appId
     * @param paymentIds 支付设置id
     */
    private void bindPayment(String appId, List<Long> paymentIds, List<Long> offlineIds) {
        miniAppPaymentService.remove(new LambdaQueryWrapper<MiniAppPayment>().eq(MiniAppPayment::getAppId, appId));
        if (CollectionUtils.isEmpty(paymentIds)) {
            return;
        }

        // 因为微信支付需要绑定小程序, 所以一个小程序只能有指定一个微信支付
        long count = paymentSettingService.count(new LambdaQueryWrapper<PaymentSetting>()
                .in(PaymentSetting::getId, paymentIds).eq(PaymentSetting::getPaymentType, PaymentEnum.Type.WX_PAY.getType()));
        Assert.isTrue(count <= 1, "只能绑定一个微信支付");

        // 绑定线上支付
        paymentIds.forEach(paymentId -> {
            PaymentSetting paymentSetting = paymentSettingService.getById(paymentId);
            Assert.isTrue(ObjectUtil.isNotNull(paymentSetting), "支付设置不存在");

            MiniAppPayment miniAppPayment = new MiniAppPayment();
            miniAppPayment.setAppId(appId);
            miniAppPayment.setPaymentId(paymentId);
            miniAppPaymentService.save(miniAppPayment);
        });

        // 线下支付
        if (CollectionUtils.isNotEmpty(offlineIds)) {
            // 线下支付 一个线下渠道只能支持一个
            List<PaymentSetting> paymentSettings = paymentSettingService.list(new LambdaQueryWrapper<PaymentSetting>()
                    .in(PaymentSetting::getId, offlineIds).eq(PaymentSetting::getPaymentType, PaymentEnum.Type.OFFLINE.getType()));
            OfflineChannel[] offlineChannels = OfflineChannel.values();
            Arrays.stream(offlineChannels).forEach(offlineChannel -> {
                long offlineChannelCount = paymentSettings.stream().filter(paymentSetting -> paymentSetting.getOfflineChannel().equals(offlineChannel.getType())).count();
                Assert.isTrue(offlineChannelCount <= 1, String.format("线下收款账号只能绑定一个%s", offlineChannel.getName()));
            });
            // 绑定线下支付
            offlineIds.stream().distinct().forEach(offlineId -> {
                PaymentSetting paymentSetting = paymentSettingService.getById(offlineId);
                Assert.isTrue(ObjectUtil.isNotNull(paymentSetting), "支付设置不存在");

                MiniAppPayment miniAppPayment = new MiniAppPayment();
                miniAppPayment.setAppId(appId);
                miniAppPayment.setPaymentId(offlineId);
                miniAppPaymentService.save(miniAppPayment);
            });
        }
    }

    /**
     * 校验appId 和 secret是否可用
     *
     * @param appId  小程序appId
     * @param secret 小程序secret
     */
    private void checkAppIdAndSecret(String appId, String secret) {
        String resultStr = HttpUtil.get(String.format(WX_MINI_TOKEN_URL, appId, secret));
        JSONObject result = JSONObject.parseObject(resultStr);
        Assert.isTrue(ObjectUtil.isNull(result.get("errcode")), String.format("appId或secret错误,错误信息:%s", result.get("errmsg")));
    }

    @Override
    public MinAppTokenVo getAccessToken(String appId) {
        MiniApp miniApp = miniAppService.getOne(new LambdaQueryWrapper<MiniApp>().eq(MiniApp::getAppId, appId).last("limit 1"));
        if(miniApp == null) {
            Assert.isTrue(false, "未能找到miniApp记录，获取token失败");
        }
        MinAppTokenVo vo = RedisUtil.get(ACCESS_TOKEN_KEY);
        // 当缓存中没有数据 或者 缓存中的数据已经超时的情况下 需要重新获取token
        if(vo == null || (vo != null && vo.getExpirationTime().before(new Date()))) {
            RLock rLock = redissonClient.getLock(LOCK_ACCESS_TOKEN_KEY + appId);
            rLock.lock(10, TimeUnit.SECONDS);
            try {
                vo = RedisUtil.get(ACCESS_TOKEN_KEY);
                // 双重检查 如果检查通过 可以直接返回token数据
                if(vo != null && vo.getExpirationTime().after(new Date())) {
                    return vo;
                }
                Date date = new Date();
                String data = OkHttpClientUtil.doGet(tokenUrl + "?grant_type=client_credential&appid=" + appId + "&secret=" + miniApp.getAppSecret());
                MiniAppTokenDto dto = JSON.parseObject(data, MiniAppTokenDto.class);
                vo = new MinAppTokenVo();
                vo.setAccessToken(dto.getAccess_token());
                vo.setExpiresIn(dto.getExpires_in());
                vo.setExpirationTime(DateUtil.offset(date, DateField.SECOND, dto.getExpires_in()));
                // 存入缓存
                RedisUtil.set(ACCESS_TOKEN_KEY, vo, dto.getExpires_in());
                return vo;
            } catch (Exception e) {
                log.error("获取小程序token异常：" + e.getMessage());
                throw new BusinessException("获取小程序token异常");
            } finally {
                rLock.unlock();
            }
        } else {
            return vo;
        }
    }

    @Override
    public void uploadShippingInfo(UploadShippingInfoPo uploadShippingInfoPo) {
        log.info("小程序发货信息上传接口，json=" + JSON.toJSONString(uploadShippingInfoPo));
        try {
            String data = OkHttpClientUtil.doPost(uploadUrl + "?access_token=" + uploadShippingInfoPo.getAccessToken(), null, JSON.toJSONString(uploadShippingInfoPo));
            MiniAppWxResultDto dto = JSON.parseObject(data ,MiniAppWxResultDto.class);
            if(dto.getErrcode() != 0) {
                throw new BusinessException(dto.getErrcode() + "==" + dto.getErrmsg());
            }
        } catch (Exception e) {
            log.error("小程序发货信息上传接口异常,error:" + e.getMessage());
            throw new BusinessException("小程序发货信息上传接口异常");
        }
    }

    @Override
    public String getQrCode(String appId) {
        long num = miniAppService.count(new LambdaQueryWrapper<MiniApp>().eq(MiniApp::getAppId, appId));
        if(num == 0) {
            throw new BusinessException("appId不存在");
        } else {
            MiniApp miniApp = new MiniApp();
            miniApp.setAppId(appId);
            return this.getQrCode(miniApp);
        }
    }
}