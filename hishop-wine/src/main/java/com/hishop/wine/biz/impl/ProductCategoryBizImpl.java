package com.hishop.wine.biz.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.enums.DeleteFlagEnums;
import com.hishop.common.util.RedisUtil;
import com.hishop.wine.constants.BasicCacheConstants;
import com.hishop.wine.model.vo.product.ProductCategoryCacheVO;
import com.hishop.wine.repository.entity.Product;
import com.hishop.wine.repository.entity.ProductCategory;
import com.hishop.wine.model.po.product.ProductCategoryCreatePO;
import com.hishop.wine.model.po.product.ProductCategoryUpdatePO;
import com.hishop.wine.model.po.product.ProductCategoryQueryPO;
import com.hishop.wine.model.vo.product.ProductCategoryVO;
import com.hishop.wine.biz.ProductCategoryBiz;
import com.hishop.wine.repository.service.ProductCategoryService;
import com.hishop.wine.repository.param.ProductCategoryParam;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.response.PageResultHelper;
import com.hishop.common.response.PageResult;
import com.hishop.wine.repository.service.ProductService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import java.util.List;

import cn.hutool.core.bean.BeanUtil;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


/**
 * 产品分类表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
@Slf4j
@Service
public class ProductCategoryBizImpl implements ProductCategoryBiz {

    @Resource
    private ProductCategoryService productCategoryService;
    @Resource
    private ProductService productService;

    /**
     * 新增产品分类
     *
     * @param createPO 分类参数
     */
    @Override
    public void create(ProductCategoryCreatePO createPO) {
        // 检测名称是否重复
        checkCategoryName(createPO.getCategoryName(), null);

        ProductCategory entity = BeanUtil.copyProperties(createPO, ProductCategory.class);
        productCategoryService.save(entity);

        // 移除缓存
        removeAllCategoryCache();
    }

    /**
     * 编辑产品分类
     *
     * @param updatePO 分类参数
     */
    @Override
    public void update(ProductCategoryUpdatePO updatePO) {
        checkAndGetCategory(updatePO.getId());

        // 检测名称是否重复
        checkCategoryName(updatePO.getCategoryName(), updatePO.getId());

        // 如果是禁用状态 检测是否关联产品
        if (ObjectUtil.isNotNull(updatePO.getStatus()) && !updatePO.getStatus()) {
            checkProductData(updatePO.getId(), "禁用");
        }

        ProductCategory updateEntity = BeanUtil.copyProperties(updatePO, ProductCategory.class);
        productCategoryService.updateById(updateEntity);

        // 移除缓存
        removeAllCategoryCache();
    }

    /**
     * 删除产品分类
     *
     * @param id 分类id
     */
    @Override
    public void deleteById(Long id) {
        checkProductData(id, "删除");

        // 逻辑删除
        productCategoryService.logicDeleteById(id);

        // 移除缓存
        removeAllCategoryCache();
    }

    /**
     * 获取产品分类详情
     *
     * @param id 分类id
     * @return 分类详情
     */
    @Override
    public ProductCategoryVO detail(Long id) {
        ProductCategory entity = checkAndGetCategory(id);
        return BeanUtil.copyProperties(entity, ProductCategoryVO.class);
    }

    /**
     * 分页查询分类列表
     *
     * @param pagePO 分页参数
     * @return 分类列表
     */
    @Override
    public PageResult<ProductCategoryVO> pageList(ProductCategoryQueryPO pagePO) {
        ProductCategoryParam param = BeanUtil.copyProperties(pagePO, ProductCategoryParam.class);
        Page<ProductCategory> dbPage = productCategoryService.qryPage(pagePO.buildPage(), param);
        return PageResultHelper.transfer(dbPage, ProductCategoryVO.class);
    }

    /**
     * 查询所有产品分类
     *
     * @return 产品分类集合
     */
    @Override
    public List<ProductCategoryCacheVO> listAll() {
        List<ProductCategoryCacheVO> cacheObj = RedisUtil.get(BasicCacheConstants.PRODUCT_CATEGORY_ALL_LIST_CACHE);
        if (ObjectUtil.isNotNull(cacheObj)) {
            return cacheObj;
        }

        List<ProductCategory> dbCategoryList = productCategoryService.list(new LambdaQueryWrapper<ProductCategory>()
                .eq(ProductCategory::getIzDelete, DeleteFlagEnums.NO.getCode()).eq(ProductCategory::getStatus, Boolean.TRUE));
        List<ProductCategoryCacheVO> categoryList = BeanUtil.copyToList(dbCategoryList, ProductCategoryCacheVO.class);

        RedisUtil.setDefaultTime(BasicCacheConstants.PRODUCT_CATEGORY_ALL_LIST_CACHE, categoryList);
        return categoryList;
    }

    /**
     * 移除所有产品分类缓存
     */
    @Override
    public void removeAllCategoryCache() {
        RedisUtil.del(BasicCacheConstants.PRODUCT_CATEGORY_ALL_LIST_CACHE);
    }

    /**
     * 检测并获取分类信息
     *
     * @param categoryId 分类id
     * @return 分类信息
     */
    @Override
    public ProductCategory checkAndGetCategory(Long categoryId) {
        ProductCategory entity = productCategoryService.getById(categoryId);
        if (entity == null || DeleteFlagEnums.checkDelete(entity.getIzDelete())) {
            throw new BusinessException("分类数据不存在");
        }
        return entity;
    }

    /**
     * 检测分类是否有效
     *
     * @param categoryId 分类id
     */
    @Override
    public ProductCategory checkAndGetValidCategory(Long categoryId) {
        ProductCategory category = checkAndGetCategory(categoryId);
        Assert.isTrue(category.getStatus(), "分类已禁用");
        return category;
    }

    /**
     * 检测分类名称是否存在
     *
     * @param categoryName 分类名称
     * @param productId    产品id
     */
    private void checkCategoryName(String categoryName, Long productId) {
        long count = productCategoryService.count(new LambdaQueryWrapper<ProductCategory>()
                .eq(ProductCategory::getCategoryName, categoryName)
                .eq(ProductCategory::getIzDelete, DeleteFlagEnums.NO.getCode())
                .ne(ObjectUtil.isNotNull(productId), ProductCategory::getId, productId));

        Assert.isTrue(count == 0, "分类名称已存在");
    }

    /**
     * 检测产品数据是否存在
     *
     * @param categoryId 分类id
     * @param tag        标签
     */
    private void checkProductData(Long categoryId, String tag) {
        long count = productService.count(new LambdaQueryWrapper<Product>()
                .eq(Product::getProductCategoryId, categoryId)
                .eq(Product::getIzDelete, DeleteFlagEnums.NO.getCode()));

        Assert.isTrue(count == 0, String.format("该分类下存在产品数据，无法%s", tag));
    }
}