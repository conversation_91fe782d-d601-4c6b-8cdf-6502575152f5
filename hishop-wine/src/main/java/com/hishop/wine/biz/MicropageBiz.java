package com.hishop.wine.biz;

import com.hishop.common.response.PageResult;
import com.hishop.wine.model.po.micropage.*;
import com.hishop.wine.model.vo.micropage.MicropageDetailVO;
import com.hishop.wine.model.vo.micropage.MicropageVO;
import com.hishop.wine.repository.entity.MicropageVisit;

import java.util.List;

public interface MicropageBiz {
    boolean create(MicropageCreatePO micropageCreatePO);

    boolean update(MicropageUpdatePO micropageCreatePO);

    MicropageDetailVO detail(Long id);

    MicropageDetailVO miniDetail(Long id);

    boolean changeCategory(MicropageChangeCategoryPO micropageChangeCategoryPO);

    boolean deletes(MicropageDeletePO micropageDeletePO);

    PageResult<MicropageVO> pageList(MicropageQueryPO pagePo);

    void saveVisit(List<MicropageVisit> visitList);
}
