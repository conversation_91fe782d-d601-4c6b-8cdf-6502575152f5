package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import com.hishop.wine.biz.MemberBiz;
import com.hishop.wine.model.po.member.MemberQueryPO;
import com.hishop.wine.model.po.member.MemberSetRankBatchPO;
import com.hishop.wine.model.po.member.MemberSetRankPO;
import com.hishop.wine.model.vo.member.MemberSelectVO;
import com.hishop.wine.model.vo.member.MemberVO;
import com.hishop.wine.repository.dto.member.MemberDTO;
import com.hishop.wine.repository.entity.Rank;
import com.hishop.wine.repository.entity.User;
import com.hishop.wine.repository.param.member.MemberParam;
import com.hishop.wine.repository.service.RankService;
import com.hishop.wine.repository.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/7/25
 */
@Service
@Slf4j
public class MemberBizImpl implements MemberBiz {

    @Resource
    private UserService userService;
    @Resource
    private RankService rankService;

    /**
     * 分页查询会员列表
     *
     * @param memberQueryPO 查询条件
     * @return 会员列表
     */
    @Override
    public PageResult<MemberVO> pageList(MemberQueryPO memberQueryPO) {
        Page<MemberDTO> memberDTOPage = userService.pageListMember(memberQueryPO.buildPage(), BeanUtil.copyProperties(memberQueryPO, MemberParam.class));
        return PageResultHelper.transfer(memberDTOPage, MemberVO.class, (db, vo) -> {
            // 如果头衔被禁用 则不显示头衔
            if (ObjectUtil.isNull(db.getRankStatus()) || !db.getRankStatus()) {
                vo.setRankId(null);
                vo.setRankName("-");
            }
        });
    }

    /**
     * 会员下拉选择
     *
     * @param searchKey 筛选值
     * @return 会员列表
     */
    @Override
    public List<MemberSelectVO> listForSelect(Long userId, String searchKey) {
        MemberParam param = new MemberParam();
        param.setUserId(userId);
        param.setMemberSearchKey(searchKey);
        return BeanUtil.copyToList(userService.listMemberSelect(param), MemberSelectVO.class);
    }

    /**
     * 设置会员头衔
     *
     * @param memberSetRankPO 设置会员头衔参数
     */
    @Override
    public void setRank(MemberSetRankPO memberSetRankPO) {
        if (ObjectUtil.isNotNull(memberSetRankPO.getRankId())) {
            Rank rank = rankService.getById(memberSetRankPO.getRankId());
            Assert.isTrue(ObjectUtil.isNotNull(rank), "头衔不存在");
        }

        userService.update(new UpdateWrapper<User>().lambda()
                .set(User::getRankId, memberSetRankPO.getRankId())
                .eq(User::getId, memberSetRankPO.getUserId()));
    }

    /**
     * 会员详情
     *
     * @param id 会员id
     * @return 会员详情
     */
    @Override
    public MemberVO detail(Long id) {
        MemberParam param = new MemberParam();
        param.setUserId(id);
        List<MemberDTO> memberDTOPage = userService.listMember(param);
        return CollectionUtils.isEmpty(memberDTOPage) ? null : BeanUtil.copyProperties(memberDTOPage.get(0), MemberVO.class);
    }

    /**
     * 批量设置头衔
     *
     * @param memberSetRankBatchPO 批量设置头衔参数
     */
    @Override
    public void setRankBatch(MemberSetRankBatchPO memberSetRankBatchPO) {
        if (CollectionUtils.isEmpty(memberSetRankBatchPO.getUserIds())) {
            return;
        }

        if (ObjectUtil.isNotNull(memberSetRankBatchPO.getRankId())) {
            Rank rank = rankService.getById(memberSetRankBatchPO.getRankId());
            Assert.isTrue(ObjectUtil.isNotNull(rank), "头衔不存在");
        }

        userService.update(new UpdateWrapper<User>().lambda()
                .set(User::getRankId, memberSetRankBatchPO.getRankId())
                .in(User::getId, memberSetRankBatchPO.getUserIds()));
    }
}
