package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import com.hishop.wine.biz.LogisticsCodeBiz;
import com.hishop.wine.common.enums.CodeTypeEnum;
import com.hishop.wine.common.enums.LogisticsCodeCategoryEnum;
import com.hishop.wine.common.enums.LogisticsCodeStatusEnum;
import com.hishop.wine.common.enums.LogisticsCodeTypeEnum;
import com.hishop.wine.model.po.logisticsCode.BatchLogisticsCodeStatusPo;
import com.hishop.wine.model.po.logisticsCode.LogisticsCodeFeignPo;
import com.hishop.wine.model.po.logisticsCode.LogisticsCodeQueryPo;
import com.hishop.wine.model.po.logisticsCode.LogisticsCodeStatusPo;
import com.hishop.wine.model.vo.logisticsCode.LogisticsCodeVo;
import com.hishop.wine.repository.entity.LogisticsCode;
import com.hishop.wine.repository.entity.LogisticsCodeBottle;
import com.hishop.wine.repository.entity.Product;
import com.hishop.wine.repository.service.LogisticsCodeBottleService;
import com.hishop.wine.repository.service.LogisticsCodeService;
import com.hishop.wine.repository.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/07/09/ $
 * @description:
 */
@Slf4j
@Service
public class LogisticsCodeBizImpl implements LogisticsCodeBiz {

    private final static Integer SPLIT_SIZE = 2000;

    @Resource
    private LogisticsCodeService logisticsCodeService;

    @Resource
    private ProductService productService;

    @Resource
    private LogisticsCodeBottleService logisticsCodeBottleService;

    @Override
    public Set<String> getCaseCodes(Set<String> codes) {
        Set<String> reSet = Sets.newHashSet();
        List<List<String>> list = CollectionUtil.split(codes, SPLIT_SIZE);
        for (List<String> ary : list) {
            Set<String> set = logisticsCodeService.list(new LambdaQueryWrapper<LogisticsCode>().in(LogisticsCode::getCodeSecondary, ary)
                            .eq(LogisticsCode::getCodeType, CodeTypeEnum.CASE_CODE.getType()).eq(LogisticsCode::getIzDelete, Boolean.FALSE)
                    ).stream()
                    .map(LogisticsCode::getCodeSecondary)
                    .collect(Collectors.toSet());
            reSet.addAll(set);
        }
        return reSet;
    }

    @Override
    public Set<String> getBoxCodes(Set<String> codes) {
        Set<String> reSet = Sets.newHashSet();
        List<List<String>> list = CollectionUtil.split(codes, SPLIT_SIZE);
        for (List<String> ary : list) {
            Set<String> set = logisticsCodeService.list(new LambdaQueryWrapper<LogisticsCode>().in(LogisticsCode::getCodeSecondary, ary)
                            .eq(LogisticsCode::getCodeType, CodeTypeEnum.BOX_CODE.getType()).eq(LogisticsCode::getIzDelete, Boolean.FALSE)
                    ).stream()
                    .map(LogisticsCode::getCodeSecondary)
                    .collect(Collectors.toSet());
            reSet.addAll(set);
        }
        return reSet;
    }

    @Override
    public Map<String, Long> getProductCode(Set<String> codes) {
        Map<String, Long> reSet = Maps.newHashMap();
        List<List<String>> list = CollectionUtil.split(codes, SPLIT_SIZE);
        for (List<String> ary : list) {
            Map<String, Long> set = productService.list(new LambdaQueryWrapper<Product>().in(Product::getProductCode, ary)
                            .eq(Product::getIzDelete, Boolean.FALSE).eq(Product::getStatus, Boolean.TRUE)
                    ).stream()
                    .collect(Collectors.toMap(Product::getProductCode, Product::getId));
            reSet.putAll(set);
        }
        return reSet;
    }

    @Override
    public Set<String> getBottleCode(Set<String> codes) {
        Set<String> reSet = Sets.newHashSet();
        List<List<String>> list = CollectionUtil.split(codes, SPLIT_SIZE);
        for (List<String> ary : list) {
            Set<String> set = logisticsCodeBottleService.list(new LambdaQueryWrapper<LogisticsCodeBottle>().in(LogisticsCodeBottle::getCodeBottle, ary)
                            .eq(LogisticsCodeBottle::getIzDelete, Boolean.FALSE)
                    ).stream()
                    .map(LogisticsCodeBottle::getCodeBottle)
                    .collect(Collectors.toSet());
            reSet.addAll(set);
        }
        return reSet;
    }

    @Override
    public PageResult<LogisticsCodeVo> pageList(LogisticsCodeQueryPo queryPo) {
        Page<LogisticsCodeVo> page = logisticsCodeService.qryPage(queryPo.buildPage(), queryPo);
        PageResult<LogisticsCodeVo> pageResult = PageResultHelper.transfer(page, LogisticsCodeVo.class, f -> {
            f.setCodeTypeShow(LogisticsCodeTypeEnum.getLogisticsCodeTypeEnum(f.getCodeType()).getDesc());
            f.setStatusShow(LogisticsCodeStatusEnum.getLogisticsCodeStatusEnum(f.getStatus()).getDesc());
            f.setCodeCategoryShow(LogisticsCodeCategoryEnum.getLogisticsCodeCategoryEnum(f.getCodeCategory()).getDesc());
        });
        return pageResult;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIds(List<Long> ids) {
        // 查询已使用的物流码
        List<LogisticsCode> usedCodes = logisticsCodeService.list(new LambdaQueryWrapper<LogisticsCode>().eq(LogisticsCode::getIzDelete, Boolean.FALSE)
                .in(LogisticsCode::getId, ids).eq(LogisticsCode::getStatus, LogisticsCodeStatusEnum.USED.getValue()));
        if (CollectionUtil.isNotEmpty(usedCodes)) {
            throw new BusinessException("选中了已使用的物流码不允许删除");
        }
        logisticsCodeService.removeBatchByIds(ids);
        // 同时还要删除瓶内码
        logisticsCodeBottleService.remove(new LambdaQueryWrapper<LogisticsCodeBottle>().in(LogisticsCodeBottle::getLogisticsCodeId, ids));
    }

    @Override
    public void batchUpdateStatus(LogisticsCodeStatusPo codeStatusPo) {
        logisticsCodeService.update(new LambdaUpdateWrapper<LogisticsCode>()
                .in(LogisticsCode::getFileImportId, codeStatusPo.getFileImportIdList())
                .eq(LogisticsCode::getIzDelete, Boolean.FALSE)
                .set(LogisticsCode::getStatus, codeStatusPo.getStatus()));
    }

    @Override
    public LogisticsCodeVo getLogisticsCodeInfo(String code) {

        LogisticsCodeBottle codeBottle = logisticsCodeBottleService.getOne(new LambdaQueryWrapper<LogisticsCodeBottle>()
                .eq(LogisticsCodeBottle::getCodeBottle, code)
                .eq(LogisticsCodeBottle::getIzDelete, Boolean.FALSE)
                .last("limit 1"));

        if (codeBottle != null) {
            LogisticsCode logisticsCode = logisticsCodeService.getById(codeBottle.getLogisticsCodeId());
            Product product = productService.getById(logisticsCode.getProductId());
            LogisticsCodeVo vo = BeanUtil.copyProperties(logisticsCode, LogisticsCodeVo.class);
            vo.setProductName(product.getProductName());
            return vo;
        } else {
            LogisticsCode logisticsCode = logisticsCodeService.getOne(new LambdaQueryWrapper<LogisticsCode>()
                    .eq(LogisticsCode::getCodeSecondary, code)
                    .eq(LogisticsCode::getIzDelete, Boolean.FALSE)
                    .last("limit 1"));
            if (logisticsCode != null) {
                Product product = productService.getById(logisticsCode.getProductId());
                LogisticsCodeVo vo = BeanUtil.copyProperties(logisticsCode, LogisticsCodeVo.class);
                vo.setProductName(product.getProductName());
                return vo;
            }
        }
        return null;
    }

    @Override
    public Set<String> getCodes(Set<String> codes) {
        Set<String> reSet = Sets.newHashSet();
        List<List<String>> list = CollectionUtil.split(codes, SPLIT_SIZE);
        for (List<String> ary : list) {
            Set<String> set = logisticsCodeService.list(new LambdaQueryWrapper<LogisticsCode>().in(LogisticsCode::getCodeSecondary, ary).eq(LogisticsCode::getIzDelete, Boolean.FALSE)
                    ).stream()
                    .map(LogisticsCode::getCodeSecondary)
                    .collect(Collectors.toSet());
            reSet.addAll(set);
        }
        return reSet;
    }

    /**
     * 按状态查询是否有存在的物流码
     * @param codes 物流码列表
     * @param status 0未使用 1已使用
     * @return 存在的物流码
     */
    public Set<String> getCodesByStatus(Set<String> codes, Integer status) {
        Set<String> reSet = Sets.newHashSet();
        List<List<String>> list = CollectionUtil.split(codes, SPLIT_SIZE);
        for (List<String> ary : list) {
            Set<String> set = logisticsCodeService.list(new LambdaQueryWrapper<LogisticsCode>()
                            .in(LogisticsCode::getCodeSecondary, ary)
                            .eq(LogisticsCode::getIzDelete, Boolean.FALSE)
                            .eq(LogisticsCode::getStatus, status)
                    ).stream()
                    .map(LogisticsCode::getCodeSecondary)
                    .collect(Collectors.toSet());
            reSet.addAll(set);
        }
        return reSet;
    }

    @Override
    public Set<String> batchUpdateLogisticsCodesStatus(BatchLogisticsCodeStatusPo po) {
        Set<String> codeSet = po.getCodeList().stream().collect(Collectors.toSet());
        Set<String> set = getCodesByStatus(codeSet, 1);
        if(CollectionUtil.isNotEmpty(set)) {
           return set;
        }
        logisticsCodeService.update(new LambdaUpdateWrapper<LogisticsCode>()
                .in(LogisticsCode::getCodeSecondary, po.getCodeList())
                .eq(LogisticsCode::getIzDelete, Boolean.FALSE)
                .set(LogisticsCode::getStatus, 1));
        return Sets.newHashSet();
    }

    @Override
    public List<LogisticsCodeVo> getLogisticsCodeByFileImportIds(LogisticsCodeFeignPo po) {
        List<LogisticsCodeVo> list = Lists.newArrayList();
        LogisticsCodeBottle codeBottle = logisticsCodeBottleService.getOne(new LambdaQueryWrapper<LogisticsCodeBottle>()
                .eq(LogisticsCodeBottle::getCodeBottle, po.getLogisticsCode())
                .eq(LogisticsCodeBottle::getIzDelete, Boolean.FALSE)
                .in(LogisticsCodeBottle::getFileImportId, po.getIds())
                .last("limit 1"));

        if (codeBottle != null) {
            LogisticsCode logisticsCode = logisticsCodeService.getById(codeBottle.getLogisticsCodeId());
            Product product = productService.getById(logisticsCode.getProductId());
            LogisticsCodeVo vo = BeanUtil.copyProperties(logisticsCode, LogisticsCodeVo.class);
            vo.setProductName(product.getProductName());
            list.add(vo);
            return list;
        } else {
            LogisticsCode logisticsCode = logisticsCodeService.getOne(new LambdaQueryWrapper<LogisticsCode>()
                    .eq(LogisticsCode::getCodeSecondary, po.getLogisticsCode())
                    .eq(LogisticsCode::getIzDelete, Boolean.FALSE)
                    //.eq(LogisticsCode::getStatus, po.getStatus())
                    .in(LogisticsCode::getFileImportId, po.getIds())
                    .last("limit 1"));
            if (logisticsCode != null) {
                Product product = productService.getById(logisticsCode.getProductId());
                // 判断是不是箱码 如果是箱码要查出所有的盒码
                if(logisticsCode.getCodeType() == 1) {
                    List<LogisticsCode> boxs = logisticsCodeService.list(new LambdaQueryWrapper<LogisticsCode>()
                            .eq(LogisticsCode::getIzDelete, false)
                            .eq(LogisticsCode::getStatus, po.getStatus())
                            .eq(LogisticsCode::getCodeFirst, logisticsCode.getCodeSecondary()));
                    boxs.forEach(f->{
                        LogisticsCodeVo vo = BeanUtil.copyProperties(f, LogisticsCodeVo.class);
                        vo.setProductName(product.getProductName());
                        list.add(vo);
                    });
                    return list;
                } else {
                    // 如果扫出来是盒码 要根据状态判断是否返回
                    if(!po.getStatus().equals(logisticsCode.getStatus())) {
                        return null;
                    }
                    LogisticsCodeVo vo = BeanUtil.copyProperties(logisticsCode, LogisticsCodeVo.class);
                    vo.setProductName(product.getProductName());
                    list.add(vo);
                    return list;
                }
            }
        }
        return null;
    }

    @Override
    public void batchUpdateLogisticsCodesStatusNoUsed(BatchLogisticsCodeStatusPo po) {
        logisticsCodeService.update(new LambdaUpdateWrapper<LogisticsCode>()
                .in(LogisticsCode::getCodeSecondary, po.getCodeList())
                .eq(LogisticsCode::getIzDelete, Boolean.FALSE)
                .set(LogisticsCode::getStatus, 0));
    }

}
