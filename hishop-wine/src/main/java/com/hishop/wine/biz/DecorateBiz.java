package com.hishop.wine.biz;

import com.hishop.wine.model.po.decorate.DecoratePO;
import com.hishop.wine.model.po.decorate.DecorateQueryPO;
import com.hishop.wine.model.vo.decorate.DecorateVO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 装修表 业务逻辑接口
 * @author: ch<PERSON><PERSON><PERSON>
 * @date: 2023-07-11
 */

public interface DecorateBiz {

    /**
     * 新增商城装修
     * <AUTHOR>
     * @date 2023/6/26
     */
    void save(DecoratePO createPo);

    /**
     * 根据类型获取商城装修
     * <AUTHOR>
     * @date 2023/6/26
     */
    DecorateVO detail(DecorateQueryPO queryPO);

    /**
     * 获取所有小程序配置首页、底部导航、配色（初始化用）
     * @return
     */
    List<DecorateVO> listDecorateByAppId(String appId);

}