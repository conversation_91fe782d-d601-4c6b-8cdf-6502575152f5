package com.hishop.wine.biz;


import com.hishop.wine.model.vo.logistics.LogisticsCompanyVO;

import java.util.List;

/**
 * 物流公司业务逻辑接口
 *
 * @author: chenpeng
 * @date: 2023-07-19
 */
public interface LogisticsCompanyBiz {

    /**
     * 选中物流公司
     * @param id 物流公司id
     * @return 物流公司id
     */
    void select(Long id);

    /**
     * 删除已选物流公司
     * @param id 物流公司id
     */
    void delete(Long id);

    /**
     * 可选物流公司列表
     * @return
     */
    List<LogisticsCompanyVO> unSelected();

    /**
     * 获取物流公司列表
     * @return
     */
    List<LogisticsCompanyVO> listLogisticsCompany();

}