package com.hishop.wine.biz.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import com.hishop.common.util.MysqlPlusUtil;
import com.hishop.wine.biz.BillBiz;
import com.hishop.wine.constants.Constant;
import com.hishop.wine.enums.bill.TransactionType;
import com.hishop.wine.enums.order.PayMethod;
import com.hishop.wine.model.dto.bill.BillCountDto;
import com.hishop.wine.model.dto.bill.BillGroupDto;
import com.hishop.wine.model.po.bill.BillExportPo;
import com.hishop.wine.model.po.bill.BillQueryPo;
import com.hishop.wine.model.vo.bill.BillCountVo;
import com.hishop.wine.model.vo.bill.BillGroupVo;
import com.hishop.wine.model.vo.bill.BillVo;
import com.hishop.wine.repository.entity.Bill;
import com.hishop.wine.repository.service.BillService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 账单管理业务实现类
 */
@Service
@RequiredArgsConstructor
public class BillBizImpl implements BillBiz {

    private final static Map<String, String> CERTIFICATE_BILL_MAPPING = new HashMap<>();

    public final BillService billService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void billSave(Bill bill) {
        Date date = new Date();
        String year = DateUtil.format(date, "yyyy");
        String month = DateUtil.format(date, "yyyy-MM");
        String day = DateUtil.format(date, "yyyy-MM-dd");
        bill.setCreateYear(year);
        bill.setCreateMonth(month);
        bill.setCreateDay(day);
        bill.setCreateTime(date);
        billService.save(bill);
    }

    @Override
    public PageResult<BillVo> pageList(BillQueryPo billQueryPo) {
        // 处理排序sql
        String orderSql = MysqlPlusUtil.getOrderSql(billQueryPo.getSortList(), CERTIFICATE_BILL_MAPPING);
        if (StrUtil.isEmpty(orderSql)) {
            orderSql = Constant.DEFAULT_ORDER_SQL;
        }
        billQueryPo.setSortSql(orderSql);
        Page<Bill> pageInfo = billService.queryPage(billQueryPo.buildPage(), billQueryPo);
        return PageResultHelper.transfer(pageInfo, BillVo.class, vo->{
            vo.setTransactionTypeStr(TransactionType.fromStringDesc(vo.getTransactionType()));
            vo.setPayMethodStr(PayMethod.fromStringDesc(vo.getPayMethod()));
        });
    }

    @Override
    public BillCountVo pageListCount(BillQueryPo billQueryPo) {
        return billService.pageListCount(billQueryPo);
    }

    @Override
    public PageResult<BillGroupVo> monthPageList(BillQueryPo billQueryPo) {
        Page<BillGroupDto> pageInfo = billService.monthPageList(billQueryPo.buildPage(), billQueryPo);
        return PageResultHelper.transfer(pageInfo, BillGroupVo.class, vo -> {
            vo.setPayMethodStr(PayMethod.fromStringDesc(vo.getPayMethod()));
        });
    }

    @Override
    public BillCountVo monthPageListCount(BillQueryPo billQueryPo) {
        return billService.monthPageListCount(billQueryPo);
    }

    @Override
    public PageResult<BillGroupVo> dayPageList(BillQueryPo billQueryPo) {
        Page<BillGroupDto> pageInfo = billService.dayPageList(billQueryPo.buildPage(), billQueryPo);
        return PageResultHelper.transfer(pageInfo, BillGroupVo.class, vo -> {
            vo.setPayMethodStr(PayMethod.fromStringDesc(vo.getPayMethod()));
        });
    }

    @Override
    public BillCountVo dayPageListCount(BillQueryPo billQueryPo) {
        return billService.dayPageListCount(billQueryPo);
    }

    @Override
    public List<BillVo> exportList(BillExportPo param) {
        return billService.exportList(param);
    }
}
