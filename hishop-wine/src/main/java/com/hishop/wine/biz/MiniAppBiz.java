package com.hishop.wine.biz;

import java.io.IOException;
import java.util.List;

import com.hishop.wine.common.enums.LinkEnum;
import com.hishop.wine.model.po.miniApp.MiniAppCreatePO;
import com.hishop.wine.model.po.miniApp.MiniAppUpdatePO;
import com.hishop.wine.model.po.miniApp.uploadShippingInfo.UploadShippingInfoPo;
import com.hishop.wine.model.vo.miniApp.*;

/**
 * 小程序表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-07-18
 */
public interface MiniAppBiz {

    /**
     * 查询小程序列表
     *
     * @return 小程序列表
     */
    List<MiniAppVO> list();

    /**
     * 绑定小程序
     *
     * @param createPO 绑定小程序参数
     */
    void create(MiniAppCreatePO createPO);

    /**
     * 更换小程序
     *
     * @param updatePO 更换小程序参数
     */
    void update(MiniAppUpdatePO updatePO);

    /**
     * 查询小程序详情
     *
     * @param id 主键
     * @return 小程序详情
     */
    MiniAppDetailVO detail(Long id);

    /**
     * 小程序和底部导航下拉
     *
     * @param selectCodes 选中的模块
     * @return 小程序和底部导航下拉
     */
    List<HomePageAndStoreBarSelectVO> listHomePageAndStoreBar(List<String> selectCodes);

    /**
     * 删除小程序
     *
     * @param id 小程序id
     */
    void delete(Long id);

    /**
     * 查询链接目标
     *
     * @param linkType 链接类型
     * @return 链接目标
     */
    List<LinkTargetVO> listLinkTarget(String linkType);

    /**
     * 查询链接tab
     *
     * @param linkType   链接类型
     * @param moduleCode 模块编码
     * @return 链接tab
     */
    List<LinkTabVO> listLinkTab(LinkEnum.LinkType linkType, String moduleCode);

    /**
     * 获取小程序token
     * @param appId
     * @return
     */
    MinAppTokenVo getAccessToken(String appId);

    /**
     * 发货信息上传
     * @param uploadShippingInfoPo
     */
    void uploadShippingInfo(UploadShippingInfoPo uploadShippingInfoPo);

    /**
     * 获取小程序二维码
     * @return
     */
    String getQrCode(String appId);
}