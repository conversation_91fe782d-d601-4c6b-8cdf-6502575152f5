package com.hishop.wine.biz;


import java.util.List;

import com.hishop.wine.model.po.delivery.DeliveryAddressCreatePO;
import com.hishop.wine.model.po.delivery.DeliveryAddressUpdatePO;
import com.hishop.wine.model.po.delivery.DeliveryWxCreatePO;
import com.hishop.wine.model.vo.delivery.DeliveryAddressSimpleVO;
import com.hishop.wine.model.vo.delivery.DeliveryAddressVO;
import com.hishop.wine.model.vo.logistics.LogisticsCompanyVO;

/**
 * 收货地址表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-06-29
 */
public interface DeliveryAddressBiz {

    /**
     * 创建收货地址
     *
     * @param createPO 创建收货地址参数
     * @return 收货地址id
     */
    Long create(DeliveryAddressCreatePO createPO);

    /**
     * 编辑收货地址
     *
     * @param updatePO 编辑收货地址参数
     */
    void update(DeliveryAddressUpdatePO updatePO);

    /**
     * 删除收货地址
     *
     * @param id 收货地址id
     */
    void deleteById(Long id);

    /**
     * 查询收货地址详情
     *
     * @param id 收货地址id
     * @return 收货地址详情
     */
    DeliveryAddressVO getById(Long id);

    /**
     * 查修收货地址列表
     *
     * @return 收货地址列表
     */
    List<DeliveryAddressSimpleVO> list();

    /**
     * 查询商家收货地址列表
     *
     * @return
     */
    List<DeliveryAddressSimpleVO> listByMerchant();

    /**
     * 根据id列表查询收货地址列表
     *
     * @param ids 收货地址id列表
     * @return 收货地址列表
     */
    List<DeliveryAddressVO> getByIds(List<Long> ids);

    /**
     * 设置为默认地址
     *
     * @param id 地址id
     */
    void setDefault(Long id);

    /**
     * 获取默认收货地址
     *
     * @param userId 用户id
     * @return 默认收货地址
     */
    DeliveryAddressVO getDefaultAddress(Long userId);

    /**
     * 保存微信收货地址
     *
     * @param deliveryWxCreatePO 微信收货地址参数
     * @return 收货地址id
     */
    Long createWxDeliveryAddress(DeliveryWxCreatePO deliveryWxCreatePO);


}