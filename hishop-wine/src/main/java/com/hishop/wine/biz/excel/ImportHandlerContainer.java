package com.hishop.wine.biz.excel;

import cn.hutool.core.map.MapUtil;
import com.hishop.common.export.model.BizType;
import com.hishop.wine.biz.excel.dealer.AbstractImportHandler;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
@Component
@SuppressWarnings("rawtypes")
public class ImportHandlerContainer implements InitializingBean, ApplicationContextAware {

    private ApplicationContext applicationContext;

    private final Map<BizType, AbstractImportHandler> importHandlerMap = MapUtil.newHashMap(false);

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, AbstractImportHandler> beanMap = applicationContext.getBeansOfType(AbstractImportHandler.class);
        beanMap.forEach((k, v) -> importHandlerMap.put(v.bizType(), v));
    }

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public AbstractImportHandler getImportHandler(BizType bizType) {
        return importHandlerMap.get(bizType);
    }
}
