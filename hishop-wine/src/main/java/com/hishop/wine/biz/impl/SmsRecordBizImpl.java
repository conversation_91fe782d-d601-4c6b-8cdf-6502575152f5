package com.hishop.wine.biz.impl;

import com.hishop.wine.enums.SmsEnum;
import com.hishop.wine.model.po.sms.SmsRecordQueryPO;
import com.hishop.wine.model.vo.sms.SmsRecordVO;
import com.hishop.wine.repository.entity.SmsRecord;
import com.hishop.wine.biz.SmsRecordBiz;
import com.hishop.wine.repository.service.SmsRecordService;
import com.hishop.wine.repository.param.SmsRecordParam;
import com.hishop.common.response.PageResultHelper;
import com.hishop.common.response.PageResult;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import cn.hutool.core.bean.BeanUtil;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;


/**   
 * 短信发送记录表 业务逻辑实现类
 * @author: HuBiao
 * @date: 2023-07-12
 */

@Slf4j
@Service
public class SmsRecordBizImpl implements SmsRecordBiz {

    @Resource
    private SmsRecordService smsRecordService;

    @Override
    public PageResult<SmsRecordVO> pageList(SmsRecordQueryPO pagePO) {
        SmsRecordParam param = BeanUtil.copyProperties(pagePO, SmsRecordParam.class);
        Page<SmsRecord> dbPage = smsRecordService.qryPage(pagePO.buildPage(), param);
        PageResult<SmsRecordVO> result = PageResultHelper.transfer(dbPage, SmsRecordVO.class);
        result.getList().forEach(item -> {
            item.setStatusName(SmsEnum.Status.getName(item.getStatus()));
        });
        return result;
    }

}