package com.hishop.wine.biz.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.bean.WxMaRunStepInfo;
import cn.binarywang.wx.miniapp.config.WxMaConfig;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.hishop.common.context.LoginUserContext;
import com.hishop.common.enums.IdentityTypeEnums;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.pojo.login.LoginResult;
import com.hishop.common.pojo.login.LoginUser;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import com.hishop.common.response.ResponseEnum;
import com.hishop.common.util.*;
import com.hishop.wine.assist.UserAssist;
import com.hishop.wine.biz.*;
import com.hishop.wine.common.annotation.SwitchWxMini;
import com.hishop.wine.common.config.WxAutoMapping;
import com.hishop.wine.common.config.WxAutoMappingConfig;
import com.hishop.wine.common.enums.AgreementEnum;
import com.hishop.wine.common.enums.AuditStatus;
import com.hishop.wine.constants.BasicCacheConstants;
import com.hishop.wine.enums.BasicSettingEnum;
import com.hishop.wine.enums.CheckIdentityTypeEnum;
import com.hishop.wine.model.po.LoginErrorPO;
import com.hishop.wine.model.po.basic.MiniUserSavePO;
import com.hishop.wine.model.po.login.UserMiniAuthLoginPO;
import com.hishop.wine.model.po.login.UserMiniDefaultLoginPO;
import com.hishop.wine.model.po.login.UserMiniMobileLoginPO;
import com.hishop.wine.model.po.login.UserRefreshTokenPO;
import com.hishop.wine.model.po.minUser.*;
import com.hishop.wine.model.po.miniApp.MiniEncryptDataPO;
import com.hishop.wine.model.vo.basic.MiniUserDetailVO;
import com.hishop.wine.model.vo.basic.MiniUserVO;
import com.hishop.wine.model.vo.basic.UserTagsVO;
import com.hishop.wine.model.vo.login.MiniLoginUserVO;
import com.hishop.wine.model.vo.login.MiniLoginVO;
import com.hishop.wine.model.vo.minUser.MiniUserBlacklistVo;
import com.hishop.wine.model.vo.setting.ProtocolSettingVO;
import com.hishop.wine.model.vo.terminate.MiniTerminateVo;
import com.hishop.wine.model.vo.user.UserDetailVo;
import com.hishop.wine.mq.publish.RegisterSuccessTransactionPublish;
import com.hishop.wine.repository.dto.WxLoginDataDTO;
import com.hishop.wine.repository.entity.*;
import com.hishop.wine.repository.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 小程序用户表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
@Slf4j
@Service
public class MiniUserBizImpl implements MiniUserBiz {

    @Resource
    private MiniUserService miniUserService;
    @Resource
    private WxMaService wxMaService;
    @Resource
    private UserBiz userBiz;
    @Resource
    private UserService userService;
    @Resource
    private IdentityService identityService;
    @Resource
    private WxAutoMappingConfig wxAutoMappingConfig;
    @Resource
    private SmsBiz smsBiz;
    @Resource
    private BasicSettingBiz basicSettingBiz;
    @Resource
    private RankService rankService;
    @Resource
    private UserAssist userAssist;

    @Resource
    private UserTagsService userTagsService;

    @Resource
    private TagsService tagsService;

    @Resource
    private RegisterSuccessTransactionPublish registerSuccessTransactionPublish;

    @Resource
    private TerminateService terminateService;

    @Resource
    private AdminBiz adminBiz;

    @Resource
    private DepartmentService departmentService;

    @Resource
    private LoginErrorBiz loginErrorBiz;

    static Map<String, String> USERMINI_MAPPING = new HashMap<>();

    String DEFAULT_USERMINI_SQL = "ORDER BY hmu.create_time DESC";

    /**
     * 校验方法
     *
     * @param identityType 身份
     * @param phone        电话
     * @param wxLoginData  微信凭证
     * @return 返回 10000表示无需后续操作 10001表示手机号未注册 10002表示openId已绑定 10003表示当前账号已激活
     */
    private CheckIdentityTypeEnum checkIdentityType(IdentityTypeEnums identityType, String phone, WxLoginDataDTO wxLoginData) {
        CheckIdentityTypeEnum status = CheckIdentityTypeEnum.NORMAL;
        // 如果登录身份是终端，就去查询门店表
        if (identityType != null) {
            if (identityType == IdentityTypeEnums.TERMINAL) {
                // 1.通过手机号码查询门店表
                Terminate terminate = terminateService.getOne(new LambdaQueryWrapper<Terminate>().eq(Terminate::getPhone, phone).eq(Terminate::getIzDelete, false).last("limit 1"));
                if (terminate == null) {
                    return CheckIdentityTypeEnum.PHONE_NOT_REGISTERED;
                }
                if (!terminate.getIzEnable()) {
                    return CheckIdentityTypeEnum.TERMINATE_DISABLE;
                }

                if (terminate.getAuditStatus().equals(AuditStatus.PROCESSING.getType())) {
                    return CheckIdentityTypeEnum.TERMINATE_AUDIT;
                }

                if (terminate.getAuditStatus().equals(AuditStatus.REJECT.getType())) {
                    return CheckIdentityTypeEnum.TERMINATE_REJECT;
                }

                // 2.如果门店表有数据 根据openId与appId 查询hishop_mini_user表是否有数据
                MiniUser miniUser = miniUserService.getOne(new LambdaQueryWrapper<MiniUser>().eq(MiniUser::getAppId, HeaderUtil.getAppId()).eq(MiniUser::getOpenId, wxLoginData.getOpenId()));
                if (miniUser != null) {
                    // 3.如果hishop_mini_user有数据，再根据数据中的user_id 查hishop_user表 判断手机号是否一致
                    User user = userService.getById(miniUser.getUserId());
                    if (user != null && !user.getMobile().equals(phone)) {
                        return CheckIdentityTypeEnum.OPEN_ID_BOUND;
                    }
                } else {
                    // 4.如果hishop_mini_user没有数据，就注册一条数据，并且返回一个状态，用作温馨提示给前端，前端再发起授权就可以直接进去
                    return CheckIdentityTypeEnum.ACTIVATION_FIRST;
                }
            } else if (identityType == IdentityTypeEnums.BUSINESS) {
                User user = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getMobile, phone).eq(User::getStatus, 1).eq(User::getIzDelete, false).last("limit 1"));
                if (user == null) {
                    return CheckIdentityTypeEnum.SALESMAN_NOT_EXIST;
                }
                Identity identity = identityService.getOne(new LambdaQueryWrapper<Identity>().eq(Identity::getUserId, user.getId()).eq(Identity::getIdentityType, IdentityTypeEnums.BUSINESS.getType()).eq(Identity::getIzDelete, false).last("limit 1"));
                if (identity == null) {
                    return CheckIdentityTypeEnum.SALESMAN_NOT_EXIST;
                }
            }
        }
        return status;
    }

    /**
     * 微信授权登录(会自动注册)
     *
     * @param authLoginPO 登录参数
     * @return 登录结果
     */
    @Override
    @SwitchWxMini
    @Transactional(rollbackFor = Exception.class)
    public MiniLoginVO wxAuthLogin(UserMiniAuthLoginPO authLoginPO) {
        IdentityTypeEnums identityType = IdentityTypeEnums.getByType(authLoginPO.getIdentityType());
        Assert.isTrue(ObjectUtil.isNotNull(identityType), "身份类型异常");
        // 解析微信手机号
        String mobile = getPhone(authLoginPO.getMobileCode());
        UserMiniMobileLoginPO mobileLoginPO = BeanUtil.copyProperties(authLoginPO, UserMiniMobileLoginPO.class);
        mobileLoginPO.setMobile(mobile);
        return mobileLogin(mobileLoginPO, Boolean.FALSE);
    }

    @Override
    @SwitchWxMini
    @Transactional(rollbackFor = Exception.class)
    public MiniLoginVO wxAuthLogin2(UserMiniAuthLoginPO authLoginPO) {
        IdentityTypeEnums identityType = IdentityTypeEnums.getByType(authLoginPO.getIdentityType());
        Assert.isTrue(ObjectUtil.isNotNull(identityType), "身份类型异常");
        // 解析微信手机号
        String mobile = authLoginPO.getTestPhone();
        UserMiniMobileLoginPO mobileLoginPO = BeanUtil.copyProperties(authLoginPO, UserMiniMobileLoginPO.class);
        mobileLoginPO.setMobile(mobile);
        return mobileLogin2(mobileLoginPO, Boolean.FALSE);
    }

    @SwitchWxMini
    @Transactional(rollbackFor = Exception.class)
    public MiniLoginVO mobileLogin2(UserMiniMobileLoginPO loginPO, Boolean checkVerifyCode) {
        log.info("小程序手机号登录参数:{}", JSONUtil.toJsonStr(loginPO));
        if (checkVerifyCode) {
            smsBiz.checkSmsCode(loginPO.getMobile(), BasicCacheConstants.MINI_LOGIN_MOBILE_KEY, loginPO.getVerifyCode());
        }

        WxMaConfig wxMaConfig = wxMaService.getWxMaConfig();

        // 解析wx登录凭证
        WxLoginDataDTO wxLoginData = new WxLoginDataDTO();
        wxLoginData.setOpenId(UUID.randomUUID().toString());
        wxLoginData.setSessionKey(UUID.randomUUID().toString());
        wxLoginData.setOpenId(UUID.randomUUID().toString());

        // 特殊数据的情况
        User oldUser = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getMobile, loginPO.getMobile()));
        MiniUser oldMiniUser = miniUserService.getOne(new LambdaQueryWrapper<MiniUser>().eq(MiniUser::getAppId, wxMaConfig.getAppid()).eq(MiniUser::getOpenId, wxLoginData.getOpenId()));
        User user = null;
        MiniUser miniUser = null;
        // 这里特殊情况，属于历史数据迁移导致的user表中存在没有手机号的数据，这里只能做反向绑定手机号
        if (oldUser == null && oldMiniUser != null && oldMiniUser.getUserId() != null) {
            user = userService.getById(oldMiniUser.getUserId());
            user.setMobile(loginPO.getMobile());
            userService.updateById(user);
            miniUser = oldMiniUser;
        } else {
            // 如果没有用户信息 注册一条
            user = addUserIfNotExist(loginPO.getMobile());
            // 关联微信小程序用户
            miniUser = addWxUserIfNotExist(wxMaConfig.getAppid(), user.getId(), HeaderUtil.getModuleCode(), wxLoginData, checkVerifyCode);
            Assert.isTrue(miniUser.getUserId().equals(user.getId()), "该小程序用户已绑定其他用户");
        }

        // 如果没有身份信息注册一条
        Identity identity = addIdentityIfNotExist(user, loginPO.getIdentityType(),
                loginPO.getInviterUserId(), loginPO.getRegisterChannel(), loginPO.getRegisterBizCode());

        // 登录
        LoginResult loginResult = SessionUtil.login(userBiz.buildLoginUser(user, identity, miniUser));

        // 删除验证码
        if (checkVerifyCode) {
            smsBiz.removeSmsCode(loginPO.getMobile(), BasicCacheConstants.MINI_LOGIN_MOBILE_KEY);
        }
        return miniLoginUserVOBuilder(loginResult, user, identity, loginPO);
    }

    /**
     * 手机号登录(会自动注册)
     *
     * @param loginPO         登录参数
     * @param checkVerifyCode 是否校验验证码
     * @return 登录结果
     */
    @Override
    @SwitchWxMini
    @Transactional(rollbackFor = Exception.class)
    public MiniLoginVO mobileLogin(UserMiniMobileLoginPO loginPO, Boolean checkVerifyCode) {
        if (checkVerifyCode) {
            smsBiz.checkSmsCode(loginPO.getMobile(), BasicCacheConstants.MINI_LOGIN_MOBILE_KEY, loginPO.getVerifyCode());
        }

        IdentityTypeEnums identityType = IdentityTypeEnums.getByType(loginPO.getIdentityType());

        WxMaConfig wxMaConfig = wxMaService.getWxMaConfig();

        // 解析wx登录凭证
        WxLoginDataDTO wxLoginData = getWxLoginData(loginPO.getCode());
        // 校验返回 0表示无需后续操作 1表示手机号未注册 2表示openId已绑定 3表示当前账号已激活
        CheckIdentityTypeEnum status = checkIdentityType(identityType, loginPO.getMobile(), wxLoginData);
        if (status != CheckIdentityTypeEnum.NORMAL && status != CheckIdentityTypeEnum.ACTIVATION_FIRST) {
            // 只有这种情况，前端需要根据wxcode拿到对应的手机号码，所以存缓存
            RedisUtil.set(BasicCacheConstants.MINI_LOGIN_MOBILE_CODE_PHONE + loginPO.getCode(), loginPO.getMobile(), 60 * 5);
            throw new BusinessException(String.valueOf(status.getValue()), status.getDesc());
        }
        // 如果没有用户信息 注册一条
        User user = addUserIfNotExist(loginPO.getMobile());
        MiniUser oldUser = miniUserService.getOne(new LambdaQueryWrapper<MiniUser>().eq(MiniUser::getUserId, user.getId()).eq(MiniUser::getIzDelete, false).last("limit 1"));
        if(oldUser != null && !oldUser.getOpenId().equals(wxLoginData.getOpenId())) {
            loginErrorBiz.saveLoginErrorAsync(loginPO, wxMaConfig, wxLoginData, 1);
            throw new BusinessException("该手机号已绑定在其他小程序用户上");
        }
        // 关联微信小程序用户
        MiniUser miniUser = addWxUserIfNotExist(HeaderUtil.getAppId(), user.getId(), HeaderUtil.getModuleCode(), wxLoginData, checkVerifyCode);
        if(!miniUser.getUserId().equals(user.getId())) {
            loginErrorBiz.saveLoginErrorAsync(loginPO, wxMaConfig, wxLoginData, 0);
            throw new BusinessException("该小程序用户已绑定其他手机号上");
        }

        // 如果没有身份信息注册一条
        Identity identity = addIdentityIfNotExist(user, loginPO.getIdentityType(),
                loginPO.getInviterUserId(), loginPO.getRegisterChannel(), loginPO.getRegisterBizCode());

        // 登录
        LoginResult loginResult = SessionUtil.login(userBiz.buildLoginUser(user, identity, miniUser));

        // 删除验证码
        if (checkVerifyCode) {
            smsBiz.removeSmsCode(loginPO.getMobile(), BasicCacheConstants.MINI_LOGIN_MOBILE_KEY);
        }
        MiniLoginVO miniLoginVO = miniLoginUserVOBuilder(loginResult, user, identity, loginPO);
        // 设置终端首次注册状态
        miniLoginVO.setIsTerminalFirstRegister(status == CheckIdentityTypeEnum.ACTIVATION_FIRST);
        miniLoginVO.setTerminalFirstRegisterStr(status == CheckIdentityTypeEnum.ACTIVATION_FIRST ? CheckIdentityTypeEnum.ACTIVATION_FIRST.getDesc() : "");
        if(status == CheckIdentityTypeEnum.ACTIVATION_FIRST) {
            miniLoginVO.setIsFirstLogin(true);
        } else {
            miniLoginVO.setIsFirstLogin(false);
        }
        return miniLoginVO;
    }

    /**
     * 微信静默登录
     *
     * @param defaultLoginPO 静默登录参数
     * @return 登录返回值
     */
    @Override
    @SwitchWxMini
    public MiniLoginVO wxDefaultLogin(UserMiniDefaultLoginPO defaultLoginPO) {
        // 获取微信配置
        WxMaConfig wxMaConfig = wxMaService.getWxMaConfig();

        // 获取微信登录凭证
        WxLoginDataDTO wxLoginData = getWxLoginData(defaultLoginPO.getCode());

        // 如果没有查询到需要先登录
        MiniUser miniUser = miniUserService.getOne(new LambdaQueryWrapper<MiniUser>().eq(MiniUser::getAppId, wxMaConfig.getAppid())
                .eq(MiniUser::getOpenId, wxLoginData.getOpenId()));
        if (ObjectUtil.isNull(miniUser)) {
            throw new BusinessException(ResponseEnum.UNAUTHORIZED);
        }

        // 获取用户信息
        User user = userAssist.checkAndGetUser(miniUser.getUserId());

        // 获取身份信息
        Identity identity = userAssist.checkAndGetIdentity(user.getId(), defaultLoginPO.getIdentityType());

        LoginResult loginResult = SessionUtil.login(userBiz.buildLoginUser(user, identity, miniUser));
        return miniLoginUserVOBuilder(loginResult, user, identity, null);
    }

    /**
     * 刷新token
     *
     * @param refreshTokenParam 刷新token 参数
     * @return 小程序登录信息
     */
    @Override
    public MiniLoginVO refreshToken(UserRefreshTokenPO refreshTokenParam) {
        String refreshToken = refreshTokenParam.getRefreshToken();

        LoginUser loginUser = LoginUserUtil.parseLoginUser(refreshToken, Boolean.TRUE);
        Integer identityType = ObjectUtil.isNotNull(refreshTokenParam.getIdentityType())
                ? refreshTokenParam.getIdentityType() : loginUser.getIdentity().getIdentityType();

        // 获取用户信息
        User user = userAssist.checkAndGetUser(loginUser.getUserId());

        // 获取微信配置信息
        String moduleCode = HeaderUtil.getModuleCode();
        WxAutoMapping wxAutoMapping = wxAutoMappingConfig.getWxAutoMapping(moduleCode);
        // 查询微信用户信息
        MiniUser miniUser = miniUserService.getOne(new LambdaQueryWrapper<MiniUser>()
                .eq(MiniUser::getUserId, user.getId()).eq(MiniUser::getAppId, wxAutoMapping.getAppId()));
        Assert.isTrue(ObjectUtil.isNotNull(miniUser), "微信用户信息不存在");

        // 获取身份信息
        Identity identity = userAssist.checkAndGetIdentity(user.getId(), identityType);

        LoginResult loginResult = SessionUtil.login(userBiz.buildLoginUser(user, identity, miniUser));
        return miniLoginUserVOBuilder(loginResult, user, identity, null);
    }

    /**
     * 添加微信用户(如果不存在)
     *
     * @param appId       小程序appId
     * @param userId      用户id
     * @param moduleCode  模块编码
     * @param wxLoginData 微信登录数据
     * @param checkVerifyCode 是否验证码登录
     * @return 小程序用户
     */
    private MiniUser addWxUserIfNotExist(String appId, Long userId, String moduleCode, WxLoginDataDTO wxLoginData, Boolean checkVerifyCode) {
        MiniUser miniUser = miniUserService.getOne(new LambdaQueryWrapper<MiniUser>().eq(MiniUser::getAppId, appId)
                .eq(MiniUser::getOpenId, wxLoginData.getOpenId()).eq(MiniUser::getIzDelete, false).last("limit 1"));
        // 手机号验证码登录的，需要查询是否已经有minUser
        if(checkVerifyCode) {
            MiniUser oldMiniUser = miniUserService.getOne(new LambdaQueryWrapper<MiniUser>().eq(MiniUser::getUserId, userId).eq(MiniUser::getIzDelete, false).last("limit 1"));
            // 如果有直接返回原来的minUser
            if (ObjectUtil.isNotNull(oldMiniUser)) {
                return oldMiniUser;
            }
        }
        if (ObjectUtil.isNull(miniUser)) {
            miniUser = new MiniUser();
            miniUser.setAppId(appId);
            miniUser.setUserId(userId);
            miniUser.setRegisterModuleCode(moduleCode);
            miniUser.setUnionId(wxLoginData.getUnionId());
            miniUser.setOpenId(wxLoginData.getOpenId());
            miniUser.setSessionKey(wxLoginData.getSessionKey());
            miniUserService.save(miniUser);
        } else {
            MiniUser updateMiniUser = new MiniUser();
            updateMiniUser.setId(miniUser.getId());
            updateMiniUser.setSessionKey(wxLoginData.getSessionKey());
            miniUserService.updateById(updateMiniUser);
            miniUser.setSessionKey(wxLoginData.getSessionKey());
        }
        return miniUser;
    }

    /**
     * 构建小程序登录结果
     *
     * @param loginResult token 信息
     * @param user        用户信息
     * @param identity    身份信息
     * @param loginPO     登录信息
     * @return 小程序登录信息
     */
    private MiniLoginVO miniLoginUserVOBuilder(LoginResult loginResult, User user, Identity identity, UserMiniMobileLoginPO loginPO) {
        MiniLoginVO miniLoginVO = BeanUtil.copyProperties(loginResult, MiniLoginVO.class);
        MiniLoginUserVO miniLoginUserVO = new MiniLoginUserVO();
        miniLoginUserVO.setMobile(user.getMobile());
        miniLoginUserVO.setIdentityType(identity.getIdentityType());
        miniLoginUserVO.setNickName(user.getNickName());
        miniLoginUserVO.setAvatarUrl(user.getIcon());
        miniLoginUserVO.setRankName(getRankName(user.getRankId()));
        miniLoginUserVO.setPhone(user.getMobile());
        miniLoginVO.setUserInfo(miniLoginUserVO);
        if (loginPO != null) {
            miniLoginVO.setInviterUserId(loginPO.getInviterUserId());
        }
        return miniLoginVO;
    }

    /**
     * 添加用户信息(如果不存在)
     *
     * @param mobile 手机号
     * @return 用户信息
     */
    private User addUserIfNotExist(String mobile) {
        User user = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getMobile, mobile));
        if (ObjectUtil.isNull(user)) {
            user = new User();
            // 设置一个默认的账号
            user.setUsername(IdUtil.fastSimpleUUID());
            user.setMobile(mobile);
            userService.save(user);

            // 更新创建人信息
            user.setCreateBy(user.getId());
            user.setUpdateBy(user.getId());
            userService.updateById(user);
        }
        return user;
    }

    /**
     * 添加身份信息(如果不存在)
     *
     * @param user            用户信息
     * @param identityType    身份类型
     * @param inviterUserId   邀请人id
     * @param registerChannel 注册渠道
     * @param registerBizCode 注册业务码
     * @return 身份信息
     */
    private Identity addIdentityIfNotExist(User user, Integer identityType, Long inviterUserId, String registerChannel, String registerBizCode) {
        // 判断身份信息是否存在
        String moduleCode = HeaderUtil.getModuleCode();
        Identity identity = identityService.getIdentity(user.getId(), identityType, moduleCode);
        if (ObjectUtil.isNull(identity)) {
            identity = new Identity();
            identity.setUserId(user.getId());
            identity.setModuleCode(moduleCode);
            identity.setIdentityType(identityType);
            identity.setCreateBy(user.getId());
            identity.setUpdateBy(user.getId());
            identity.setInviterUserId(inviterUserId);
            identity.setRegisterChannel(registerChannel);
            identity.setRegisterBizCode(registerBizCode);
            identityService.save(identity);

            // 触发注册成功事件
            registerSuccessTransactionPublish.sendMsgTransaction(identity.getId());
        }
        return identity;
    }

    /**
     * 获取微信登录信息
     *
     * @param code wx.login获取的code
     * @return 微信登录信息
     */
    private WxLoginDataDTO getWxLoginData(String code) {
        try {
            WxMaJscode2SessionResult wxResult = wxMaService.jsCode2SessionInfo(code);
            return WxLoginDataDTO.of(wxResult.getSessionKey(), wxResult.getOpenid(), wxResult.getUnionid());
        } catch (Exception e) {
            log.error("小程序登录失败: {}", e);
            throw new BusinessException(String.format("小程序登录失败: 【%s】", e.getMessage()));
        }
    }

    /**
     * 获取微信手机号
     *
     * @param code 手机号获取凭证
     * @return 手机号
     */
    public String getPhone(String code) {
        try {
            //临时兼容用
            WxMaService newWxMaService = null;
            try {
                newWxMaService = wxMaService.switchoverTo(HeaderUtil.getAppId());
            } catch (Exception e) {

            }
            if (newWxMaService == null) {
                newWxMaService = wxMaService;
            }
            WxMaPhoneNumberInfo newPhoneNoInfo = newWxMaService.getUserService().getNewPhoneNoInfo(code);
            return newPhoneNoInfo.getPurePhoneNumber();
        } catch (Exception e) {
            log.error("获取微信手机号失败: {}", e);
            throw new BusinessException(String.format("获取微信手机号失败: 【%s】", e.getMessage()));
        }
    }

    @Override
    public List<MiniUserVO> getMiniUser(List<Long> userIdList) {
        List<MiniUser> dbList = miniUserService.getMiniUser(userIdList);
        return BeanUtil.copyToList(dbList, MiniUserVO.class);
    }

    @Override
    public MiniUserVO getMiniUserByUserIdAndModuleCode(Long userId, String moduleCode) {
        return miniUserService.getMiniUserByUserIdAndModuleCode(userId, moduleCode);
    }

    /**
     * 发送小程序登录验证码
     *
     * @param mobile 手机号
     */
    @Override
    public void sendForLogin(String mobile) {
        smsBiz.sendSmsCode(mobile, BasicCacheConstants.MINI_LOGIN_MOBILE_KEY, "登录");
    }

    /**
     * 保存用户信息
     *
     * @param miniUserSavePO 保存用户信息参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveUserData(MiniUserSavePO miniUserSavePO) {
        Long miniUserId = LoginUserUtil.getLoginUser().getMiniUserId();
        Assert.isTrue(ObjectUtil.isNotNull(miniUserId), "小程序用户信息不存在");

        MiniUser miniUser = new MiniUser();
        miniUser.setId(miniUserId);
        miniUser.setNickName(miniUserSavePO.getNickName());
        miniUser.setAvatarUrl(miniUserSavePO.getAvatarUrl());
        miniUserService.updateById(miniUser);

        User updUser = new User();
        updUser.setId(LoginUserUtil.getLoginUser().getUserId());
        updUser.setIcon(miniUserSavePO.getAvatarUrl());
        updUser.setNickName(miniUserSavePO.getNickName());
        userService.updateById(updUser);
    }

    /**
     * 获取用户协议/隐私政策
     * TODO 添加缓存
     *
     * @param type 1:用户协议 2:隐私政策
     * @return 协议内容
     */
    @Override
    public String getAgreement(Integer type) {
        ProtocolSettingVO settingVO = basicSettingBiz.getSetting(BasicSettingEnum.PROTOCOL_SETTING);
        return AgreementEnum.USER_AGREEMENT.getType().equals(type) ? Base64.decodeStr(settingVO.getUserAgreement()) : Base64.decodeStr(settingVO.getPrivacyPolicy());
    }

    @Override
    public List<WxMaRunStepInfo> getRunStep(MiniEncryptDataPO encryptDataPo) {
        log.info("获取微信运动步数，参数：{}", JSONUtil.toJsonStr(encryptDataPo));
        /*WxMaJscode2SessionResult result = null;
        try {
            result = wxMaService.getUserService().getSessionInfo(encryptDataPo.getCode());
        } catch (WxErrorException e) {
            log.error("微信授权失败", e);
            throw new BusinessException("微信授权失败，请稍后重试");
        }*/
        return wxMaService.getRunService().getRunStepInfo(LoginUserContext.get().getSessionKey(),
                encryptDataPo.getEncryptedData(), encryptDataPo.getIv());
    }

    /**
     * 获取小程序用户信息
     *
     * @return 小程序用户信息
     */
    @Override
    public MiniLoginUserVO getMiniUserInfo() {
        MiniLoginUserVO result = new MiniLoginUserVO();

        User user = userAssist.checkAndGetUser(LoginUserUtil.getLoginUser().getUserId());
        result.setNickName(user.getNickName());
        result.setAvatarUrl(user.getIcon());
        result.setIdentityType(LoginUserUtil.getLoginUser().getIdentity().getIdentityType());
        result.setMobile(user.getMobile());
        result.setRankName(getRankName(user.getRankId()));

        //判断是否是门店
        if (result.getIdentityType().equals(IdentityTypeEnums.TERMINAL.getType())) {
            Terminate terminate = terminateService.getOne(new LambdaQueryWrapper<Terminate>()
                    .eq(Terminate::getPhone, user.getMobile())
                    .eq(Terminate::getIzDelete, false).last("limit 1"));
            if (terminate != null) {
                result.setTerminate(BeanUtil.copyProperties(terminate, MiniTerminateVo.class));
            }
        }
        //判断是否是业务员
        if (result.getIdentityType().equals(IdentityTypeEnums.BUSINESS.getType())) {
            Identity identity = identityService.getOne(new LambdaQueryWrapper<Identity>().eq(Identity::getUserId, user.getId())
                    .eq(Identity::getIdentityType, IdentityTypeEnums.BUSINESS.getType()).eq(Identity::getIzDelete, false).last("limit 1"));
            UserDetailVo userDetailVo = adminBiz.userDetail(identity.getId());
            result.setSaleAreaList(userDetailVo.getSaleAreaList());

            if (userDetailVo.getDepartmentId() != null) {
                Department department = departmentService.getById(userDetailVo.getDepartmentId());
                String departmentName = department.getDepartmentName();
                if (department.getParentId() != 0) {
                    while (department.getParentId() != 0) {
                        department = departmentService.getById(department.getParentId());
                        departmentName = department.getDepartmentName() + "/" + departmentName;
                    }
                }
                result.setDepartmentName(departmentName);
            }
        }
        return result;
    }

    /**
     * 获取头衔名称
     *
     * @param rankId 头衔Id
     * @return 头衔名称
     */
    private String getRankName(Long rankId) {
        if (ObjectUtil.isNotNull(rankId)) {
            Rank rank = rankService.getById(rankId);
            if (ObjectUtil.isNotNull(rank) && !rank.getIzDelete() && rank.getStatus()) {
                return rank.getRankName();
            }
        }
        return StrUtil.EMPTY;
    }

    /**
     * 分页查询
     *
     * @param miniUserQueryPo 查询条件
     * @return
     */
    @Override
    public PageResult<MiniUserVO> pageList(MiniUserQueryPo miniUserQueryPo) {
        // 处理排序sql
        String miniUserSql = MysqlPlusUtil.getOrderSql(miniUserQueryPo.getSortList(), USERMINI_MAPPING);
        if (StrUtil.isEmpty(miniUserSql)) {
            miniUserSql = DEFAULT_USERMINI_SQL;
        }
        Page<MiniUserVO> pageInfo = miniUserService.pageList(miniUserQueryPo.buildPage(), miniUserQueryPo);
        List<Long> ids = pageInfo.getRecords().stream().map(MiniUserVO::getUserId).collect(Collectors.toList());
        Map<Long, List<UserTags>> utMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(ids)) {
            utMap = userTagsService.list(new LambdaQueryWrapper<UserTags>().in(UserTags::getUserId, ids)).stream().collect(Collectors.groupingBy(UserTags::getUserId));
        }
        final Map<Long, List<UserTags>> futMap = utMap;
        return PageResultHelper.transfer(pageInfo, MiniUserVO.class, vo -> {
            if (futMap.get(vo.getUserId()) != null) {
                List<UserTagsVO> utvs = BeanUtil.copyToList(futMap.get(vo.getUserId()), UserTagsVO.class);
                vo.setUserTags(utvs);
            }
        });
    }

    /**
     * 查询详情
     *
     * @param id id
     * @return
     */
    @Override
    public MiniUserDetailVO getUserDetail(Long id) {
        MiniUserVO miniUserVO = miniUserService.getUserDetail(id);
        Assert.isTrue(miniUserVO != null, "客户详情未找到");
        MiniUserDetailVO miniUserDetailVO = BeanUtil.copyProperties(miniUserVO, MiniUserDetailVO.class);
        List<UserTags> userTagsList = userTagsService.list(new LambdaQueryWrapper<UserTags>().eq(UserTags::getUserId, miniUserVO.getUserId()));
        if (CollectionUtils.isNotEmpty(userTagsList)) {
            miniUserDetailVO.setUserTags(BeanUtil.copyToList(userTagsList, UserTagsVO.class));
        }
        return miniUserDetailVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void remarkName(MiniUserRemarkNamePo miniUserRemarkNamePo) {
        MiniUser miniUser = miniUserService.getById(miniUserRemarkNamePo.getId());
        Assert.isTrue(miniUser != null, "客户信息未找到");
        MiniUser updateMiniUser = new MiniUser();
        updateMiniUser.setId(miniUserRemarkNamePo.getId());
        updateMiniUser.setRemarkName(miniUserRemarkNamePo.getRemarkName());
        miniUserService.updateById(updateMiniUser);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void blackList(MiniUserBlacklistPo miniUserBlacklistPo) {
        LoginUser loginUser = LoginUserUtil.getLoginUser();
        MiniUser miniUser = new MiniUser();
        miniUser.setBlacklistDate(new Date());
        miniUser.setBlacklistOperateId(loginUser.getUserId());
        miniUser.setBlacklistOperateName(loginUser.getUsername());
        miniUser.setBlacklistOperatePhone(loginUser.getMobile());
        miniUser.setIzBlacklist(miniUserBlacklistPo.getIzBlacklist());
        miniUserService.update(miniUser, new LambdaQueryWrapper<MiniUser>().in(MiniUser::getId, miniUserBlacklistPo.getIdList()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void tags(MiniUserTagsPo miniUserTagsPo) {
        Set<Long> userSet = miniUserService.list(new LambdaQueryWrapper<MiniUser>().in(MiniUser::getId, miniUserTagsPo.getIdList())).stream().map(MiniUser::getUserId).collect(Collectors.toSet());
        Map<Long, List<Long>> userTagsIdMap = userTagsService.list(new LambdaQueryWrapper<UserTags>().in(UserTags::getUserId, userSet))
                .stream().collect(Collectors.groupingBy(UserTags::getUserId, Collectors.mapping(UserTags::getTagId, Collectors.toList())));
        // 查出所有标签的信息
        Map<Long, String> map = tagsService.list(new LambdaQueryWrapper<Tags>().in(Tags::getId, miniUserTagsPo.getTagIdList())).stream().map(tags -> {
            return new HashMap.SimpleEntry<>(tags.getId(), tags.getTagName());
        }).collect(Collectors.toMap(HashMap.SimpleEntry::getKey, HashMap.SimpleEntry::getValue));
        // 组成列表批量插入
        List<UserTags> utList = Lists.newArrayList();
        userSet.forEach(userId -> {
            List<Long> userTagIds = userTagsIdMap.get(userId) != null ? userTagsIdMap.get(userId) : Lists.newArrayList();
            miniUserTagsPo.getTagIdList().forEach(tagId -> {
                if(userSet.size() == 1) {
                    UserTags userTags = new UserTags();
                    userTags.setUserId(userId);
                    userTags.setTagId(tagId);
                    userTags.setTagName(StringUtils.isNotBlank(map.get(tagId)) ? map.get(tagId) : "");
                    userTags.setCreateBy(LoginUserUtil.getLoginUser().getUserId());
                    userTags.setCreateTime(new Date());
                    userTags.setUpdateBy(LoginUserUtil.getLoginUser().getUserId());
                    userTags.setUpdateTime(new Date());
                    utList.add(userTags);
                } else {
                    if (!userTagIds.contains(tagId)) {
                        UserTags userTags = new UserTags();
                        userTags.setUserId(userId);
                        userTags.setTagId(tagId);
                        userTags.setTagName(StringUtils.isNotBlank(map.get(tagId)) ? map.get(tagId) : "");
                        userTags.setCreateBy(LoginUserUtil.getLoginUser().getUserId());
                        userTags.setCreateTime(new Date());
                        userTags.setUpdateBy(LoginUserUtil.getLoginUser().getUserId());
                        userTags.setUpdateTime(new Date());
                        utList.add(userTags);
                    }
                }
            });
        });
        // 这种处理当做是当个用户处理，单个用户是替换标签的操作，批量用户是追加操作
        if(userSet.size() == 1) {
            userSet.forEach(userId -> {
                userTagsService.remove(new LambdaQueryWrapper<UserTags>().eq(UserTags::getUserId, userId));
            });
        }
        userTagsService.saveBatch(utList);
    }

    @Override
    public PageResult<MiniUserBlacklistVo> blacklistPage(MiniUserBlacklistQueryPo miniUserBlacklistQueryPo) {
        // 处理排序sql
        String miniUserSql = MysqlPlusUtil.getOrderSql(miniUserBlacklistQueryPo.getSortList(), USERMINI_MAPPING);
        if (StrUtil.isEmpty(miniUserSql)) {
            miniUserSql = DEFAULT_USERMINI_SQL;
        }
        Page<MiniUserBlacklistVo> pageInfo = miniUserService.blacklistPage(miniUserBlacklistQueryPo.buildPage(), miniUserBlacklistQueryPo);
        List<Long> ids = pageInfo.getRecords().stream().map(MiniUserBlacklistVo::getUserId).collect(Collectors.toList());
        Map<Long, List<UserTags>> utMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(ids)) {
            utMap = userTagsService.list(new LambdaQueryWrapper<UserTags>().in(UserTags::getUserId, ids)).stream().collect(Collectors.groupingBy(UserTags::getUserId));
        }
        final Map<Long, List<UserTags>> futMap = utMap;
        return PageResultHelper.transfer(pageInfo, MiniUserBlacklistVo.class, vo -> {
            if (futMap.get(vo.getUserId()) != null) {
                List<UserTagsVO> utvs = BeanUtil.copyToList(futMap.get(vo.getUserId()), UserTagsVO.class);
                vo.setUserTags(utvs);
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delTag(MinUserDelTagPo minUserDelTagPo) {
        userTagsService.remove(new LambdaQueryWrapper<UserTags>().eq(UserTags::getUserId, minUserDelTagPo.getId()).eq(UserTags::getTagId, minUserDelTagPo.getTagId()));
    }

    @Override
    public String getPhoneByWx(UserMiniAuthLoginPO authLoginPO) {
        return RedisUtil.get(BasicCacheConstants.MINI_LOGIN_MOBILE_CODE_PHONE + authLoginPO.getCode());
    }
}
