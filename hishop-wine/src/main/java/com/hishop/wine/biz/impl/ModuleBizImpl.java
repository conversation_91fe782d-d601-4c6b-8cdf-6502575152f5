package com.hishop.wine.biz.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.constants.SystemConstants;
import com.hishop.wine.biz.ModuleBiz;
import com.hishop.wine.common.enums.DecorateEnum;
import com.hishop.wine.common.helper.AuthModuleHelper;
import com.hishop.wine.enums.ModuleEnums;
import com.hishop.wine.model.po.miniApp.MiniAppQueryPO;
import com.hishop.wine.model.vo.basic.MiniAppVO;
import com.hishop.wine.model.vo.login.AuthModuleVO;
import com.hishop.wine.model.vo.module.BindModuleVO;
import com.hishop.wine.model.vo.module.ModuleVO;
import com.hishop.wine.repository.dao.MiniAppMapper;
import com.hishop.wine.repository.dao.ModuleMapper;
import com.hishop.wine.repository.dao.StoreBarMapper;
import com.hishop.wine.repository.entity.*;
import com.hishop.wine.repository.service.DecorateService;
import com.hishop.wine.repository.service.ModuleService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 模块表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
@Slf4j
@Service
@AllArgsConstructor
public class ModuleBizImpl implements ModuleBiz {

    private final AuthModuleHelper authModuleHelper;
    private final MiniAppMapper miniAppMapper;
    private final ModuleMapper moduleMapper;
    private final StoreBarMapper storeBarMapper;
    private final ModuleService moduleService;
    private final DecorateService decorateService;

    @Override
    public List<ModuleVO> listAuthModule() {
        List<ModuleVO> list = new ArrayList<>();
        AuthModuleVO authModuleVO = authModuleHelper.getAuthModule();
        if (authModuleVO == null) {
            return list;
        }
        // 遍历授权模块
        List<ModuleEnums> moduleEnumsList = ModuleEnums.list();
        for (ModuleEnums moduleEnums : moduleEnumsList){
            if (moduleEnums == ModuleEnums.basic_system){
                continue;
            }
            String field = moduleEnums.getField();
            String value = ReflectUtil.invoke(authModuleVO, StrUtil.format("get{}", StrUtil.upperFirst(field)));
            if (StringUtils.isNotEmpty(value) && SystemConstants.YES.equals(value)) {
                ModuleVO moduleVO = new ModuleVO();
                moduleVO.setName(moduleEnums.getDesc());
                moduleVO.setCode(moduleEnums.name());
                list.add(moduleVO);
            }
        }
        return list;
    }

    /**
     * 获取授权模块(包括基础系统)
     *
     * @return 授权模块
     */
    @Override
    public List<ModuleVO> listAuthModuleIncludeBasic() {
        List<ModuleVO> moduleVOS = listAuthModule();
        moduleVOS.add(0, ModuleVO.ofBasicSystem());
        return moduleVOS;
    }

    /**
     * 获取授权模块编码
     *
     * @return 授权模块编码
     */
    @Override
    public List<String> listAuthModuleCodes() {
        return listAuthModule().stream().map(ModuleVO::getCode).collect(Collectors.toList());
    }

    @Override
    public List<MiniAppVO> list(MiniAppQueryPO pagePo) {
        LambdaQueryWrapper<MiniApp> queryWrapper = new LambdaQueryWrapper<>();
        List<MiniApp> list = miniAppMapper.selectList(queryWrapper);
        List<MiniAppVO> result = BeanUtil.copyToList(list, MiniAppVO.class);

        //查询绑定的业务模块
        if (!CollUtil.isEmpty(result)) {
            List<String> appIds = result.stream().map(MiniAppVO::getAppId).collect(Collectors.toList());
            LambdaQueryWrapper<ModuleInfo> moduleLambdaQueryWrapper = new LambdaQueryWrapper<>();
            moduleLambdaQueryWrapper.in(ModuleInfo::getAppId, appIds);
            List<ModuleInfo> moduleInfos = moduleMapper.selectList(moduleLambdaQueryWrapper);
            for (MiniAppVO item : result){
                List<ModuleInfo> itemModuleInfo = moduleInfos.stream().filter(t->t.getAppId().equals(item.getAppId())).collect(Collectors.toList());
                if (!CollUtil.isEmpty(result)){
                    //设置模块名称
                    item.setModuleName(itemModuleInfo.stream().map(ModuleInfo::getModuleName).collect(Collectors.joining(",")));
                }
            }

        }
        return result;
    }


    @Override
    public MiniAppVO detail(String appId) {
        LambdaQueryWrapper<MiniApp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MiniApp::getAppId,appId);
        MiniApp app= miniAppMapper.selectOne(queryWrapper);
        MiniAppVO result = BeanUtil.copyProperties(app, MiniAppVO.class);

        if (ObjectUtil.isNotNull(result)) {
            //查询绑定模块
            LambdaQueryWrapper<ModuleInfo> moduleLambdaQueryWrapper = new LambdaQueryWrapper<>();
            moduleLambdaQueryWrapper.eq(ModuleInfo::getAppId, appId);
            List<ModuleInfo> moduleInfos = moduleMapper.selectList(moduleLambdaQueryWrapper);
            result.setModuleName(moduleInfos.stream().map(ModuleInfo::getModuleName).collect(Collectors.joining(",")));
            result.setModuleCode(moduleInfos.stream().map(ModuleInfo::getModuleCode).collect(Collectors.toList()));

            //查询绑定的底部导航
            LambdaQueryWrapper<StoreBar> storeBarLambdaQueryWrapper = new LambdaQueryWrapper<>();
            storeBarLambdaQueryWrapper.eq(StoreBar::getAppId,appId);
            StoreBar storeBar =storeBarMapper.selectOne(storeBarLambdaQueryWrapper);
            if (ObjectUtil.isNotNull(storeBar)){
                if (StringUtils.isNotEmpty(storeBar.getModuleCode())){
                    result.setIzSelf(false);
                    result.setStoreBarModuleCode(storeBar.getModuleCode());
                }else {
                    result.setIzSelf(true);
                }
                result.setStoreBarId(storeBar.getId());
            }
        }

        return result;
    }

    @Override
    public List<ModuleVO> listAuthModuleUnBind() {
        List<ModuleVO> listAuthModule = listAuthModule();
        List<ModuleInfo> moduleInfoList = moduleService.list();
        if(CollectionUtil.isNotEmpty(moduleInfoList)) {
            List<String> moduleNames = moduleInfoList.stream().map(ModuleInfo::getModuleName).collect(Collectors.toList());
            listAuthModule = listAuthModule.stream().filter(e -> !moduleNames.contains(e.getName())).collect(Collectors.toList());
        }
        return listAuthModule;
    }

    @Override
    public List<BindModuleVO> listABindModule(String appId) {
        LambdaQueryWrapper<ModuleInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ModuleInfo::getAppId,appId);
        List<ModuleInfo> moduleInfoList = moduleMapper.selectList(queryWrapper);
        List<BindModuleVO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(moduleInfoList)){
            Decorate defaultDecorate = decorateService.getByType(appId, null, DecorateEnum.Type.homePage.name());
            String defaultModuleCode = ObjectUtil.isNotNull(defaultDecorate) ? defaultDecorate.getModuleCode() : StrUtil.EMPTY;

            moduleInfoList.forEach(item->{
                BindModuleVO moduleVO = new BindModuleVO();
                moduleVO.setCode(item.getModuleCode());
                moduleVO.setName(item.getModuleName());
                moduleVO.setIzDefault(defaultModuleCode.equals(item.getModuleCode()));
                result.add(moduleVO);
            });
        }
        return result;
    }

    @Override
    public Boolean queryHasScanCodeModule() {
        List<ModuleVO> moduleVOS = listAuthModule();
        moduleVOS.add(0, ModuleVO.ofBasicSystem());
        List<ModuleVO> scanList = moduleVOS.stream().filter(f -> ModuleEnums.scan_marketing.name().equals(f.getCode())).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(scanList);
    }
}