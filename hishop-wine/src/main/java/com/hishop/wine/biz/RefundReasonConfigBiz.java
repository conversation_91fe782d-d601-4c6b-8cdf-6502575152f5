package com.hishop.wine.biz;


import com.hishop.wine.model.po.RefundReasonCreatePO;
import com.hishop.wine.model.vo.refund.RefundReasonConfigVO;
import io.swagger.models.auth.In;

import java.util.List;

/**
 * 退款原因配置表 业务逻辑接口
 * @author: chenpeng
 * @date: 2023-07-08
 */

public interface RefundReasonConfigBiz {

    List<RefundReasonConfigVO> list(Integer refundType);

    List<RefundReasonConfigVO> listForPC();

    RefundReasonConfigVO get(Long id);

    void saveOrUpdate(RefundReasonCreatePO createPO);

    void delete(Long id);


}