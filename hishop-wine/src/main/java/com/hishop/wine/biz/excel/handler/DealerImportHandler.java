package com.hishop.wine.biz.excel.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.excel.read.ReadResult;
import com.hishop.common.export.context.DataWrapper;
import com.hishop.common.export.model.BizType;
import com.hishop.wine.biz.excel.context.BizContent;
import com.hishop.wine.biz.excel.dealer.AbstractImportHandler;
import com.hishop.wine.biz.excel.wrapper.DealerErrWrapper;
import com.hishop.wine.common.enums.FileImportType;
import com.hishop.wine.model.po.dealer.DealerImportPo;
import com.hishop.wine.repository.entity.Dealer;
import com.hishop.wine.repository.entity.District;
import com.hishop.wine.repository.entity.FileImportRecord;
import com.hishop.wine.repository.entity.User;
import com.hishop.wine.repository.service.DealerService;
import com.hishop.wine.repository.service.DistrictService;
import com.hishop.wine.repository.service.UserService;
import com.hishop.wine.utils.AddressResolutionUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
@Service
public class DealerImportHandler extends AbstractImportHandler<DealerImportPo> {

    @Resource
    private DealerService dealerService;

    @Resource
    private DistrictService districtService;

    @Resource
    private UserService userService;


    @Override
    public BizType bizType() {
        return FileImportType.DEALER_IMPORT;
    }

    @Override
    public void checkExistsAndSetValue(ReadResult<DealerImportPo> importResult, BizContent bizContent, FileImportRecord record) {
        List<DealerImportPo> importList = importResult.getDataList();


        // 获取所有的经销商编码
        List<String> importCodeList = importList.stream()
                .map(DealerImportPo::getDealerCode)
                .collect(Collectors.toList());

        List<Dealer> dealerCodeList = dealerService.list(new LambdaQueryWrapper<Dealer>().in(Dealer::getDealerCode, importCodeList));
        Map<String, String> dealerCodeMap = CollectionUtils.isEmpty(dealerCodeList) ? MapUtil.newHashMap(false) : dealerCodeList.stream()
                .collect(Collectors.toMap(Dealer::getDealerCode, Dealer::getDealerCode));


        // 获取所有的经销商名称
        List<String> importNameList = importList.stream()
                .map(DealerImportPo::getDealerName)
                .collect(Collectors.toList());

        List<Dealer> dealerNameList = dealerService.list(new LambdaQueryWrapper<Dealer>().in(Dealer::getDealerName, importNameList));

        Map<String, String> dealerNameMap = CollectionUtils.isEmpty(dealerNameList) ? MapUtil.newHashMap(false) : dealerNameList.stream()
                .collect(Collectors.toMap(Dealer::getDealerName, Dealer::getDealerCode));


        // 获取所有的经销商负责人手机号码
        List<String> phoneList = importList.stream()
                .map(DealerImportPo::getPhone)
                .collect(Collectors.toList());

        List<Dealer> dealerPhoneList = dealerService.list(new LambdaQueryWrapper<Dealer>().in(Dealer::getPhone, phoneList));

        Map<String, String> dealerPhoneMap = CollectionUtils.isEmpty(dealerPhoneList) ? MapUtil.newHashMap(false) : dealerPhoneList.stream()
                .collect(Collectors.toMap(Dealer::getPhone, Dealer::getDealerCode));


        // 获取所有的业务员手机号码
        List<String> userPhoneList = importList.stream()
                .map(DealerImportPo::getBusinessPhone)
                .collect(Collectors.toList());

        Map<String, Long> businessUserMap = MapUtil.newHashMap(false);
        if (CollectionUtils.isNotEmpty(userPhoneList)) {

            List<User> userList = userService.list(new LambdaQueryWrapper<User>().in(User::getMobile, userPhoneList));
            businessUserMap = CollectionUtils.isEmpty(userList) ? MapUtil.newHashMap(false) : userList.stream()
                    .collect(Collectors.toMap(User::getMobile, User::getId));
        }

        List<District> provinceList = districtService.list(new LambdaQueryWrapper<District>().eq(District::getLevel, 1));

        List<District> cityList = districtService.list(new LambdaQueryWrapper<District>().eq(District::getLevel, 2));

        List<District> districtList = districtService.list(new LambdaQueryWrapper<District>().eq(District::getLevel, 3));


        Map<String, District> provinceMap = CollectionUtils.isEmpty(provinceList) ? MapUtil.newHashMap(false) : provinceList.stream()
                .collect(Collectors.toMap(District::getName, item -> item));

        Map<String, String> uniqueMap = MapUtil.newHashMap(false);

        Map<String, Long> finalBusinessUserMap = businessUserMap;


        for (DealerImportPo item : importList) {
            String errMsg = item.getErrMsg();
            if (errMsg == null) {
                errMsg = "";
            } else {
                continue;
            }

            if (dealerCodeMap.containsKey(item.getDealerCode())) {
                errMsg += "经销商编码已存在;";
            }

            if (dealerNameMap.containsKey(item.getDealerName())) {
                errMsg += "经销商名称已存在;";
            }

            if (dealerPhoneMap.containsKey(item.getPhone())) {
                errMsg += "负责人手机号已存在;";
            }

            if (uniqueMap.containsKey(item.getDealerCode())) {
                errMsg += "经销商编码重复;";
            } else {
                uniqueMap.put(item.getDealerCode(), item.getDealerCode());
            }

            if (uniqueMap.containsKey(item.getDealerName())) {
                errMsg += "经销商名称重复;";
            } else {
                uniqueMap.put(item.getDealerName(), item.getDealerName());
            }

            if (uniqueMap.containsKey(item.getPhone())) {
                errMsg += "负责人手机号重复;";
            } else {
                uniqueMap.put(item.getPhone(), item.getPhone());
            }

            if (StringUtils.isNotBlank(item.getBusinessPhone())) {
                if (finalBusinessUserMap.containsKey(item.getBusinessPhone())) {
                    item.setBusinessUserId(finalBusinessUserMap.get(item.getBusinessPhone()));
                } else {
                    errMsg += "所属业务员手机号不存在;";
                }
            }

            if (StringUtils.isNotBlank(item.getRegion())) {
                //获取省市区地址
                Map<String, String> addressResolution = AddressResolutionUtil.addressResolution(item.getRegion());

                if (addressResolution.isEmpty()) {
                    errMsg += "经销商详细地址不匹配;";
                } else {
                    if (StringUtils.isBlank(addressResolution.get(AddressResolutionUtil.PROVINCE_NAME))) {
                        if (AddressResolutionUtil.DIRECTLY_ADMINISTERED_CITIES.contains(addressResolution.get(AddressResolutionUtil.CITY_NAME))) {
                            addressResolution.put(AddressResolutionUtil.PROVINCE_NAME, addressResolution.get(AddressResolutionUtil.CITY_NAME).replace("市", ""));
                        } else {
                            errMsg += "经销商详细地址不匹配;";
                        }
                    } else {
                        //获取省市区地址
                        item.setProvinceName(addressResolution.get(AddressResolutionUtil.PROVINCE_NAME));
                        if (provinceMap.get(item.getProvinceName()) != null) {
                            item.setProvinceId(provinceMap.get(item.getProvinceName()).getId());
                            //获取市
                            cityList.stream().filter(city -> city.getParentId().equals(item.getProvinceId()) && city.getName().equals(addressResolution.get(AddressResolutionUtil.CITY_NAME))).findFirst().ifPresent(city -> {
                                item.setCityId(city.getId());
                                item.setCityName(city.getName());
                            });
                            //获取区县
                            districtList.stream().filter(district -> district.getParentId().equals(item.getCityId()) && district.getName().equals(addressResolution.get(AddressResolutionUtil.DISTRICT_NAME))).findFirst().ifPresent(district -> {
                                item.setDistrictId(district.getId());
                                item.setDistrictName(district.getName());
                            });
                            //设置地址
                            item.setAddress(addressResolution.get(AddressResolutionUtil.ADDRESS_NAME));
                        }
                    }
                }
            }
            item.setErrMsg(errMsg);
        }
    }

    @Override
    public void saveImportData(List<DealerImportPo> successList, FileImportRecord record) {
        List<Dealer> dbList = BeanUtil.copyToList(successList, Dealer.class);
        dbList.forEach(f -> f.setIzEnable(Boolean.TRUE));
        dealerService.saveBatch(dbList);
    }

    @Override
    protected DataWrapper<DealerImportPo> wrapData(List<DealerImportPo> errList) {
        return new DealerErrWrapper(errList);
    }
}
