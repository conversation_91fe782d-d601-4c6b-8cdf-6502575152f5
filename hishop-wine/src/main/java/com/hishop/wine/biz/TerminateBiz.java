package com.hishop.wine.biz;

import com.hishop.common.pojo.page.PageParam;
import com.hishop.common.response.PageResult;
import com.hishop.wine.model.po.terminate.*;
import com.hishop.wine.model.vo.terminate.TerminateDetailVo;
import com.hishop.wine.model.vo.terminate.TerminateFeignDetailVo;
import com.hishop.wine.model.vo.terminate.TerminateFeignVo;
import com.hishop.wine.model.vo.terminate.TerminateVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @description: 门店业务接口类
 * @author: chenzw
 * @date: 2024/7/6 14:49
 */
public interface TerminateBiz {
    /**
     * 保存门店
     * @param terminateSavePo 门店保存参数
     */
    void save(TerminateSavePo terminateSavePo);

    /**
     * 小程序-保存门店
     * @param savePo 小程序-门店保存参数
     */
    void minSave(TerminateMinSavePo savePo);

    /**
     * 删除门店
     * @param ids 门店id
     */
    void deleteById(List<Long> ids);

    /**
     * 更新状态
     * @param updateStatusPo 请求参数
     */
    void batchUpdateStatus(TerminateUpdateStatusPo updateStatusPo);

    /**
     * 分页查询
     * @param queryPo 查询参数
     * @return 分页结果
     */
    PageResult<TerminateVo> pageList(TerminateQueryPo queryPo);

    /**
     * 导入门店
     * @param file 文件
     */
    void importTerminate(MultipartFile file);

    /**
     * 门店详情
     * @param id 门店id
     * @return 门店详情
     */
    TerminateDetailVo detail(Long id);

    /**
     * 审核门店
     * @param auditPo 审核参数
     */
    void audit(TerminateAuditPo auditPo);

    /**
     * 通过id查询门店列表
     * @param ids 门店id
     * @return 门店列表
     */
    List<TerminateFeignVo> queryTerminateList(List<Long> ids);

    /**
     * 通过code查询门店列表
     * @param codes 门店编码
     * @return 门店列表
     */
    List<TerminateFeignVo> queryTerminateByCodeList(List<String> codes);

    /**
     * 门店详情
     * @param id 门店id
     * @return 门店详情
     */
    TerminateFeignDetailVo feignDetail(Long id);

    /**
     * 小程序-查询手机号门店情况
     * @param phone 手机号码
     * @return 返回null为没有注册记录，1审核通过 2审核不通过 3审核中
     */
    TerminateVo queryAuditStatusByPhone(String phone);

    /**
     * 小程序查询门店列表
     * @return 门店列表信息
     */
    List<TerminateVo> queryTerminateByCodeListMin();

    /**
     * 小程序查询是否是业务员
     * @param userId
     * @param moduleCode
     * @return
     */
    Boolean queryUserIsSalesman(Long userId, String moduleCode);

    /**
     * 根据电话号码查询门店
     * @param phone 手机号码
     * @return  门店信息
     */
    TerminateFeignDetailVo queryTerminateByPhone(String phone);

    /**
     * 根据当前用户查询管辖的门店列表
     * @param pageParam 分页参数
     * @return 门店列表
     */
    PageResult<TerminateVo> queryTerminateByUserPageList(PageParam pageParam);

    /**
     * 根据业务员id查询门店id集合
     * @param businessUserId 业务员id
     * @return 门店id集合
     */
    List<Long> listByBusinessUserId(Long businessUserId);
}
