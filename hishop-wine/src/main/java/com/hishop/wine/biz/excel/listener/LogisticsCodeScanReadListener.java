package com.hishop.wine.biz.excel.listener;

import cn.hutool.core.util.StrUtil;
import com.hishop.common.excel.read.DefaultReadListener;
import com.hishop.wine.model.po.logisticsCodeScan.LogisticsCodeScanImportPo;

/**
 * 扫码营销物流码导入监听器
 * <AUTHOR>
 * @date 2023/7/10
 */
public class LogisticsCodeScanReadListener extends DefaultReadListener<LogisticsCodeScanImportPo> {

    @Override
    protected String checkData(LogisticsCodeScanImportPo data) {
        String errMsg = "";
        if (StrUtil.isBlank(data.getCodeTypeStr())) {
            errMsg += "物流码类型不能为空;";
        }
        if (StrUtil.isBlank(data.getCodeSecondary())) {
            errMsg += "二级物流码不能为空;";
        }
        if (StrUtil.isBlank(data.getProductCode())) {
            errMsg += "商品编码不能为空;";
        }
        return errMsg;
    }

}
