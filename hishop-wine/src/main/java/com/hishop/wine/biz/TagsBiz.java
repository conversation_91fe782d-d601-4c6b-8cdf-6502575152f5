package com.hishop.wine.biz;

import com.hishop.common.response.PageResult;
import com.hishop.wine.model.po.TagsCreatePO;
import com.hishop.wine.model.po.TagsQueryPO;
import com.hishop.wine.model.po.TagsUpdatePO;
import com.hishop.wine.model.po.minUser.MinUserDelTagPo;
import com.hishop.wine.model.po.minUser.MiniUserTagsPo;
import com.hishop.wine.model.vo.tags.TagsVO;
import com.hishop.wine.repository.dto.MyTagsDto;

import java.util.List;

/**
 * 标签表 业务逻辑接口
 * @author: chenpeng
 * @date: 2023-07-17
 */

public interface TagsBiz {

    void create(TagsCreatePO createPO);

    void deleteById(Long id);

    TagsVO detail(Long id);

    List<TagsVO> list();

    PageResult<TagsVO> pageList(TagsQueryPO pagePO);

    void update(TagsUpdatePO tagsUpdatePO);

    /**
     * 定制酒PC-打标签查询
     * @param pagePO
     * @return
     */
    MyTagsDto getMyList(TagsQueryPO pagePO);

    /**
     * 定制酒PC-批量打标签查询
     * @param pagePO
     * @return
     */
    MyTagsDto getBatchList(TagsQueryPO pagePO);

    /**
     * 定制酒PC-详情添加标签查询
     * @param pagePO
     * @return
     */
    MyTagsDto getDetailList(TagsQueryPO pagePO);

    /**
     * 批量打标
     * @param miniUserTagsPo
     */
    void tags(MiniUserTagsPo miniUserTagsPo);

    /**
     * 定制酒PC-详情添加标签
     * @param miniUserTagsPo
     */
    void detailTags(MiniUserTagsPo miniUserTagsPo);

    /**
     * 删除标签
     * @param minUserDelTagPo
     */
    void delTag(MinUserDelTagPo minUserDelTagPo);
}