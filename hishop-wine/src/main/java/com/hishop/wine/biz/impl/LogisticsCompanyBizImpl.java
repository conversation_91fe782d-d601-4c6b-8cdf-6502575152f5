package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hishop.wine.biz.LogisticsCompanyBiz;
import com.hishop.wine.model.vo.logistics.LogisticsCompanyVO;
import com.hishop.wine.repository.entity.LogisticsCompany;
import com.hishop.wine.repository.service.LogisticsCompanyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


/**
 * 物流公司业务逻辑实现类
 *
 * @author: chenpeng
 * @date: 2023-07-19
 */

@Slf4j
@Service
public class LogisticsCompanyBizImpl implements LogisticsCompanyBiz {

    @Resource
    private LogisticsCompanyService logisticsCompanyService;

    @Override
    public void select(Long id) {
        LambdaUpdateWrapper<LogisticsCompany> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(LogisticsCompany::getIzSelected, Boolean.TRUE);
        updateWrapper.eq(LogisticsCompany::getId, id);
        logisticsCompanyService.update(updateWrapper);
    }

    @Override
    public void delete(Long id) {
        LambdaUpdateWrapper<LogisticsCompany> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(LogisticsCompany::getIzSelected, Boolean.FALSE);
        updateWrapper.eq(LogisticsCompany::getId, id);
        logisticsCompanyService.update(updateWrapper);
    }

    @Override
    public List<LogisticsCompanyVO> unSelected() {
        LambdaQueryWrapper<LogisticsCompany> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LogisticsCompany::getIzSelected, Boolean.FALSE);
        List<LogisticsCompany> list = logisticsCompanyService.list(queryWrapper);
        if(CollectionUtil.isNotEmpty(list)) {
            return BeanUtil.copyToList(list, LogisticsCompanyVO.class);
        }
        return null;
    }

    @Override
    public List<LogisticsCompanyVO> listLogisticsCompany() {
        LambdaQueryWrapper<LogisticsCompany> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LogisticsCompany::getIzSelected, Boolean.TRUE);
        List<LogisticsCompany> list = logisticsCompanyService.list(queryWrapper);
        if(CollectionUtil.isNotEmpty(list)) {
            return BeanUtil.copyToList(list, LogisticsCompanyVO.class);
        }
        return null;
    }

}