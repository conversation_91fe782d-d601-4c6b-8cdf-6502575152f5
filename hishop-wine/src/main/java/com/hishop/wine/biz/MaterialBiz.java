package com.hishop.wine.biz;

import com.hishop.common.response.PageResult;
import com.hishop.wine.repository.dto.MaterialDTO;
import com.hishop.wine.model.po.material.MaterialQueryPO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 资源库表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
public interface MaterialBiz {


    /**
     * 请求上传资源文件
     *
     * @param materialCategoryId 分组id
     * @param title              标题
     * @param banner             封面
     * @return 资源对象
     */
    MaterialDTO requestMaterialUpload(Long materialCategoryId, String title, String filePath, String banner, Long fileSize, Integer duration, boolean izAudio);


    /**
     * 添加图片
     *
     * @param materialCategoryId 分组id
     * @param title              图片名称
     * @param imagePath          图片路径
     * @return 资源对象
     */
    MaterialDTO requestImageCreate(Long materialCategoryId, String title, String imagePath, Long fileSize);

    /**
     * 上传成功
     *
     * @param filePath 上传成功的文件路径
     * @param size     文件大小（Byte)
     * @param duration 音视频长度(单位秒)
     */
    void uploadSuccess(String filePath, Long size, Integer duration);


    /**
     * 查询资源
     *
     * @param materialQueryPO 查询对象
     * @return 返回符合条件的资源
     */
    PageResult<MaterialDTO> getMaterials(MaterialQueryPO materialQueryPO);

    /**
     * 修改资源
     *
     * @param id                 资源文件id
     * @param materialCategoryId 资源文件分组id
     * @param title              标题
     */
    void updateMaterial(Long id, Long materialCategoryId, String title);

    /**
     * 批量修改资源分组
     *
     * @param newMaterialCategoryId 新资源分组id
     * @param ids                   待修改的资源id集合
     */
    void changeMaterialCategory(Long newMaterialCategoryId, Collection<Long> ids);

    /**
     * 删除资源文件
     *
     * @param ids 待删除的资源文件集合
     */
    void deleteMaterialsByIds(List<Long> ids, boolean izRemoveFile);

    /**
     * 删除指定资源分组下所有资源文件
     *
     * @param materialCategoryId 待删除的资源分组id
     */
    void deleteMaterialsByMaterialCategoryId(Long materialCategoryId);


    /**
     * 更新指定分组的资源文件数量
     *
     * @param materialCategoryId 分组id
     */
    void updateCountByMaterialCategoryId(Long materialCategoryId);

    /**
     * 设置转码状态
     *
     * @param jobId   转码任务id
     * @param success 转码是否成功
     */
    void setTranscodeStatus(String jobId, Boolean success);

}