package com.hishop.wine.biz;

import com.hishop.wine.model.po.product.*;
import com.hishop.wine.model.vo.product.ProductInnerVO;
import com.hishop.wine.model.vo.product.ProductPageVO;
import com.hishop.wine.model.vo.product.ProductVO;

import java.util.List;

import com.hishop.common.response.PageResult;
import com.hishop.wine.repository.dto.ProductImportDTO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 产品表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
public interface ProductBiz {

    /**
     * 新增产品
     *
     * @param createPO 新增产品参数
     */
    void create(ProductCreatePO createPO);

    /**
     * 编辑产品
     *
     * @param updatePO 编辑产品参数
     */
    void update(ProductUpdatePO updatePO);

    /**
     * 通过id批量删除
     *
     * @param ids id的集合
     */
    void deleteByIds(List<Long> ids);

    /**
     * 查询产品详情
     *
     * @param id 产品id
     * @return 产品详情
     */
    ProductVO detail(Long id);

    /**
     * 分页查询产品列表
     *
     * @param pagePO 分页参数
     * @return 分页结果
     */
    PageResult<ProductPageVO> pageList(ProductQueryPO pagePO);

    /**
     * 更新产品价格
     *
     * @param updatePricePO 更新参数
     */
    void updatePrice(ProductUpdatePricePO updatePricePO);

    /**
     * 导入产品信息
     *
     * @param file 产品压缩包
     * @return 导入结果
     */
    String importProduct(MultipartFile file);

    /**
     * 导入检测成功的数据
     *
     * @param productList 产品集合
     */
    void insertCheckSuccessForImport(List<ProductImportDTO> productList);

    /**
     * 查询产品详情
     *
     * @param id 产品id
     * @return 产品详情
     */
    ProductInnerVO getDetailForInner(Long id);

    /**
     * 根据ID批量查询
     *
     * <AUTHOR>
     * @date 2023/6/20
     */
    List<ProductInnerVO> listById(ProductQueryPO qryPO);

    /**
     * 查询产品id的集合
     *
     * @param productIdQueryPO 筛选参数
     * @return 产品id的集合
     */
    List<Long> queryIds(ProductIdQueryPO productIdQueryPO);
}