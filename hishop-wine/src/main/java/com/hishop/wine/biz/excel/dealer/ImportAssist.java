package com.hishop.wine.biz.excel.dealer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.hishop.common.excel.read.ReadResult;
import com.hishop.common.excel.read.RowReadResult;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.export.context.DataWrapper;
import com.hishop.common.export.handler.ExportHelper;
import com.hishop.common.export.model.BizType;
import com.hishop.wine.biz.excel.ImportHandlerContainer;
import com.hishop.wine.biz.excel.context.BizContent;
import com.hishop.wine.common.enums.FileImportStatus;
import com.hishop.wine.model.vo.fileImport.FileImportVo;
import com.hishop.wine.repository.entity.FileImportRecord;
import com.hishop.wine.repository.service.FileImportRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
@Service
@Slf4j
@SuppressWarnings("rawtypes")
public class ImportAssist {

    @Resource
    private FileImportRecordService fileImportRecordService;
    @Resource
    private ExportHelper exportHelper;
    @Resource
    private ImportHandlerContainer importHandlerContainer;

    private final static String ERR_PREFIX_PATH = "hishop/error";

    @Async
    @Transactional(rollbackFor = Exception.class)
    public <D extends RowReadResult> void importData(FileImportRecord record, BizType bizType, BizContent bizContent,ReadResult<D> importResult) {
        if (CollUtil.isEmpty(importResult.getDataList())) {
            throw new BusinessException("待导入数据为空");
        }
        long begin = System.currentTimeMillis();
        String exception = "";
        int successCount = 0;
        int failCount = 0;
        String filePath = "";
        int status = FileImportStatus.SUCCESS.getType();
        try {
            AbstractImportHandler abstractImportHandler = importHandlerContainer.getImportHandler(bizType);
            FileImportVo importVO = new FileImportVo();
            // 根据实际业务校验数据
            abstractImportHandler.checkExistsAndSetValue(importResult, bizContent, record);
            // 对数据根据是否有异常进行分组
            Map<Boolean, List<D>> errMap = importResult.getDataList().stream()
                    .collect(Collectors.groupingBy(item -> StrUtil.isNotBlank(item.getErrMsg())));
            List<D> successList = errMap.get(false);
            List<D> errList = errMap.get(true);
            // 有异常，则把异常数据生成excel文件
            successCount = CollUtil.isEmpty(successList) ? 0 : successList.size();
            failCount = CollUtil.isEmpty(errList) ? 0 : errList.size();
            if (CollUtil.isNotEmpty(errList)) {
                status = FileImportStatus.PARTIAL_FAIL.getType();
                DataWrapper<D> dataWrapper = abstractImportHandler.wrapData(errList);
                filePath = exportHelper.errExport(dataWrapper, buildFilePath(bizType));
                importVO.setFilePath(filePath);
            }
            importVO.setHasErr(failCount > 0);
            if (CollUtil.isNotEmpty(successList)) {
                abstractImportHandler.saveImportData(successList, record);
            } else {
                status = FileImportStatus.FAIL.getType();
            }
        } catch (Exception e) {
            log.error("导入业务数据异常", e);
            exception = e.getMessage();
            if (StrUtil.isBlank(exception)) {
                exception = "导入业务数据异常";
            }
            status = FileImportStatus.ERROR.getType();
        } finally {
            long end = System.currentTimeMillis();
            long cost = end - begin;
            fileImportRecordService.updateFileImportRecord(record, cost, status, exception, successCount, failCount, filePath);
        }
    }

    private String buildFilePath(BizType bizType) {
        return String.format("%s/%s/%s错误数据-", ERR_PREFIX_PATH, bizType.type(), bizType.desc()) + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
    }

}
