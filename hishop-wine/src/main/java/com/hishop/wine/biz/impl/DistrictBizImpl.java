package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.response.tencent.TencentDistrictBean;
import com.hishop.common.response.tencent.TencentResponse;
import com.hishop.common.util.LoginUserUtil;
import com.hishop.common.util.RedisUtil;
import com.hishop.wine.biz.BasicSettingBiz;
import com.hishop.wine.biz.DistrictBiz;
import com.hishop.wine.common.utils.TencentMapUtils;
import com.hishop.wine.constants.BasicCacheConstants;
import com.hishop.wine.constants.BasicConstants;
import com.hishop.wine.enums.BasicSettingEnum;
import com.hishop.wine.enums.DistrictLevelEnum;
import com.hishop.wine.model.po.basic.AreaNamePO;
import com.hishop.wine.model.po.basic.DistrictCreatePO;
import com.hishop.wine.model.po.basic.DistrictUpdatePO;
import com.hishop.wine.model.vo.basic.*;
import com.hishop.wine.model.vo.setting.TencentMapSettingVO;
import com.hishop.wine.repository.entity.District;
import com.hishop.wine.repository.service.DistrictService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 地区表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-06-26
 */
@Slf4j
@Service
public class DistrictBizImpl implements DistrictBiz {

    @Resource
    private DistrictService districtService;
    @Resource
    private BasicSettingBiz basicSettingBiz;
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 特别行政区
     */
    private static final List<String> SPECIAL_PROVINCES = Arrays.asList("北京", "天津", "上海", "重庆", "香港", "澳门");
    /**
     * 省市区编码长度
     */
    private static final Integer PROVINCE_CITY_AREA_CODE_LENGTH = 6;
    /**
     * 省直辖县标识
     */
    private static final String PROVINCE_DIRECTLY_UNDER_COUNTY_CODE = "90";


    /**
     * 查询行政区域树
     *
     * @param maxLevel 最大层级
     * @return 行政区域树
     */
    @Override
    public List<DistrictTreeVO> tree(Integer maxLevel) {
        maxLevel = ObjectUtil.isNull(maxLevel) ? 3 : maxLevel;

        String redisKey = String.format(BasicCacheConstants.DISTRICT_TREE_PREFIX, maxLevel);
        List<DistrictTreeVO> cacheObj = RedisUtil.get(redisKey);
        if (ObjectUtil.isNotNull(cacheObj)) {
            return cacheObj;
        }

        // 查询等级内的所有地区数据
        List<District> districts = districtService.list(new LambdaQueryWrapper<District>()
                .le(District::getLevel, maxLevel));

        List<DistrictTreeVO> nodeList = BeanUtil.copyToList(districts, DistrictTreeVO.class);

        // 构建树
        Map<Integer, List<DistrictTreeVO>> parentMap = nodeList.stream().collect(Collectors.groupingBy(DistrictTreeVO::getParentId));
        List<DistrictTreeVO> rootList = new ArrayList<>();
        nodeList.forEach(node -> {
            node.setChildList(parentMap.get(node.getId()));

            if (node.getParentId().equals(BasicConstants.ROOT_DISTRICT_PARENT_ID)) {
                rootList.add(node);
            }
        });

        RedisUtil.setDefaultTime(redisKey, rootList);
        return rootList;
    }

    /**
     * 获取区域列表
     *
     * @param parentId 上级id
     * @return 区域列表
     */
    @Override
    public List<DistrictVO> list(Integer parentId) {
        parentId = ObjectUtil.isNull(parentId) ? BasicConstants.ROOT_DISTRICT_PARENT_ID : parentId;

        String redisKey = String.format(BasicCacheConstants.DISTRICT_LIST_PREFIX, parentId);
        List<DistrictVO> cacheObj = RedisUtil.get(redisKey);
        if (ObjectUtil.isNotNull(cacheObj)) {
            return cacheObj;
        }

        // 查询等级内的所有地区数据
        List<District> districts = districtService.list(new LambdaQueryWrapper<District>()
                .eq(District::getParentId, parentId));

        List<DistrictVO> districtVOS = BeanUtil.copyToList(districts, DistrictVO.class);
        RedisUtil.setDefaultTime(redisKey, districtVOS);
        return districtVOS;
    }

    /**
     * 获取区域详情
     *
     * @param id 区域id
     * @return 区域详情
     */
    @Override
    public DistrictVO detail(Integer id) {
        District district = districtService.getById(id);
        Assert.isTrue(ObjectUtil.isNotNull(district), "区域不存在");

        return BeanUtil.copyProperties(district, DistrictVO.class);
    }

    /**
     * 获取区域信息详情
     *
     * @param id 区域id
     * @return 区域详情
     */
    @Override
    public DistrictDetailVO getDistrictDetail(Integer id) {
        District district = districtService.getById(id);
        Assert.isTrue(ObjectUtil.isNotNull(district), "区域不存在");

        String[] parentIds = district.getParentIds().split(StrUtil.COMMA);
        String[] parentNames = district.getFullName().split(StrUtil.COMMA);

        DistrictDetailVO districtDetailVO = new DistrictDetailVO();
        districtDetailVO.setProvinceId(parentIds.length > 0 ? Integer.valueOf(parentIds[0]) : null);
        districtDetailVO.setProvince(parentNames.length > 0 ? parentNames[0] : null);
        districtDetailVO.setCityId(parentIds.length > 1 ? Integer.valueOf(parentIds[1]) : null);
        districtDetailVO.setCity(parentNames.length > 1 ? parentNames[1] : null);
        districtDetailVO.setAreaId(parentIds.length > 2 ? Integer.valueOf(parentIds[2]) : null);
        districtDetailVO.setArea(parentNames.length > 2 ? parentNames[2] : null);
        districtDetailVO.setStreetId(parentIds.length > 3 ? Integer.valueOf(parentIds[3]) : null);
        districtDetailVO.setStreet(parentNames.length > 3 ? parentNames[3] : null);
        districtDetailVO.setLevel(district.getLevel());
        return districtDetailVO;
    }

    /**
     * 查询大区集合
     *
     * @return 大区集合
     */
    @Override
    public List<RegionVO> listRegions() {
        List<RegionVO> cacheObj = RedisUtil.get(BasicCacheConstants.REGION_LIST_CACHE);
        if (ObjectUtil.isNotNull(cacheObj)) {
            return cacheObj;
        }

        List<District> dbDistricts = districtService.list(new LambdaQueryWrapper<District>().eq(District::getLevel, DistrictLevelEnum.PROVINCE.getLevel()));
        Map<String, List<District>> regionMap = dbDistricts.stream().collect(Collectors.groupingBy(District::getRegionName, LinkedHashMap::new, Collectors.toList()));
        List<RegionVO> regions = new ArrayList<>();
        regionMap.forEach((regionName, districts) -> {
            RegionVO regionVO = new RegionVO();
            regionVO.setRegionName(regionName);
            regionVO.setDistrictList(BeanUtil.copyToList(districts, DistrictVO.class));
            regions.add(regionVO);
        });

        RedisUtil.setDefaultTime(BasicCacheConstants.REGION_LIST_CACHE, regions);
        return regions;
    }

    /**
     * 根据等级查询地区信息
     *
     * @param maxLevel 最大等级
     * @return 地区信息
     */
    @Override
    public List<DistrictVO> listByLevel(Integer maxLevel) {
        String redisKey = String.format(BasicCacheConstants.DISTRICT_LIST_LEVEL_PREFIX, maxLevel);
        List<DistrictVO> cacheObj = RedisUtil.get(redisKey);
        if (ObjectUtil.isNotNull(cacheObj)) {
            return cacheObj;
        }

        // 查询等级内的所有地区数据
        List<District> districts = districtService.list(new LambdaQueryWrapper<District>()
                .le(District::getLevel, maxLevel));

        List<DistrictVO> districtVOS = BeanUtil.copyToList(districts, DistrictVO.class);
        RedisUtil.setDefaultTime(redisKey, districtVOS);
        return districtVOS;
    }


    /**
     * 基于大区的树形结构
     *
     * @param maxLevel 查询到的最小层级，默认为 省市区
     * @return 大区集合
     */
    @Override
    public List<RegionTreeVO> regionTree(Integer maxLevel) {
        List<RegionTreeVO> cacheObj = RedisUtil.get(BasicCacheConstants.REGION_TREE_CACHE);
        if (ObjectUtil.isNotNull(cacheObj)) {
            return cacheObj;
        }
        int level = ObjectUtil.defaultIfNull(maxLevel, DistrictLevelEnum.AREA.getLevel());
        List<District> dbDistricts = districtService.list(new LambdaQueryWrapper<District>().le(District::getLevel, level));
        // 从数据中找出省级数据，大区设置在省上
        List<District> provinceList = dbDistricts.stream()
                .filter(district -> district.getLevel().equals(DistrictLevelEnum.PROVINCE.getLevel()))
                .collect(Collectors.toList());
        // 构造树形结构，并根据最小层级过滤
        Map<Integer, List<DistrictTreeVO>> districtGroup = dbDistricts.stream()
                // 比如设置最小层级是3，代表区，则过滤出省市区的，排除掉街道=4的数据
                .filter(d -> d.getLevel() <= level)
                .map(d -> BeanUtil.copyProperties(d, DistrictTreeVO.class))
                .collect(Collectors.groupingBy(DistrictTreeVO::getParentId));
        Map<String, List<District>> regionMap = provinceList.stream()
                .collect(Collectors.groupingBy(District::getRegionName, LinkedHashMap::new, Collectors.toList()));
        List<RegionTreeVO> regions = new ArrayList<>();
        regionMap.forEach((regionName, districts) -> {
            RegionTreeVO regionVO = new RegionTreeVO();
            regionVO.setRegionName(regionName);
            List<DistrictTreeVO> provinceTreeList = BeanUtil.copyToList(districts, DistrictTreeVO.class);
            buildRegionSubList(provinceTreeList, districtGroup);
            regionVO.setChildList(provinceTreeList);
            regions.add(regionVO);
        });

        RedisUtil.setDefaultTime(BasicCacheConstants.REGION_TREE_CACHE, regions);
        return regions;
    }

    private void buildRegionSubList(List<DistrictTreeVO> regionList, Map<Integer, List<DistrictTreeVO>> districtGroup) {
        regionList.forEach(region -> {
            if (districtGroup.containsKey(region.getId())) {
                List<DistrictTreeVO> subList = districtGroup.get(region.getId());
                region.setChildList(subList);
                if (CollUtil.isNotEmpty(subList)) {
                    buildRegionSubList(subList, districtGroup);
                }
            }
        });
    }

    /**
     * 同步地图数据
     */
    // @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncDistrict() {
        log.info("======> 同步地区任务开始");
        Long startTime = System.currentTimeMillis();

        // 读取地图配置
        TencentMapSettingVO settingVO = basicSettingBiz.getSetting(BasicSettingEnum.TENCENT_MAP_SETTING);

        // 从腾讯地图获取数据
        TencentResponse<List<List<TencentDistrictBean>>> tencentResponse = TencentMapUtils.getProvinceAreaCity();
        // 对比版本号是否一致 一致则无需更新
        Assert.isTrue(!settingVO.getDataVersion().equals(tencentResponse.getDataVersion()), "当前已经是最新版本, 无需更新");

        List<List<TencentDistrictBean>> provinceAreaCityList = tencentResponse.getResult();

        // 递归同步
        syncDistrict(provinceAreaCityList, provinceAreaCityList.get(0),
                DistrictLevelEnum.PROVINCE.getLevel(), BasicConstants.ROOT_DISTRICT_PARENT_ID, StrUtil.EMPTY, StrUtil.EMPTY);

        // 更新版本号
        settingVO.setDataVersion(tencentResponse.getDataVersion());
        basicSettingBiz.saveSetting(BasicSettingEnum.TENCENT_MAP_SETTING, JSONObject.toJSONString(settingVO));

        // 移除缓存
        removeCache();

        log.info("======> 同步地区任务结束, 耗时: {} ms", System.currentTimeMillis() - startTime);
    }

    /**
     * 新增地区
     *
     * @param districtCreatePO 新增地区参数
     */
    @Override
    public void createDistrict(DistrictCreatePO districtCreatePO) {
        District district = new District();
        district.setName(districtCreatePO.getName());
        district.setId(getRandomNum());
        district.setLng(BigDecimal.ZERO);
        district.setLat(BigDecimal.ZERO);
        district.setHasChild(Boolean.FALSE);

        // 非一级区域
        if (ObjectUtil.isNotNull(districtCreatePO.getParentId()) && districtCreatePO.getParentId() > 0) {
            District parentDistrict = districtService.getById(districtCreatePO.getParentId());
            Assert.isTrue(ObjectUtil.isNotNull(parentDistrict), "上级地区不存在");
            district.setParentId(parentDistrict.getId());
            district.setParentIds(String.format("%s,%s", parentDistrict.getParentIds(), district.getId()));
            district.setFullName(String.format("%s,%s", parentDistrict.getFullName(), district.getName()));
            district.setLevel(parentDistrict.getLevel() + 1);
        }
        // 一级区域
        else {
            district.setFullName(districtCreatePO.getName());
            district.setParentId(BasicConstants.ROOT_DISTRICT_PARENT_ID);
            district.setParentIds(String.valueOf(district.getId()));
            district.setLevel(DistrictLevelEnum.PROVINCE.getLevel());
        }
        districtService.save(district);

        removeCache();
    }

    /**
     * 编辑地区
     *
     * @param districtUpdatePO 编辑地区参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDistrict(DistrictUpdatePO districtUpdatePO) {
        District district = districtService.getById(districtUpdatePO.getId());
        Assert.isTrue(ObjectUtil.isNotNull(district), "地区不存在");
        if (district.getName().equals(districtUpdatePO.getName())) {
            return;
        }

        String oldFullName = district.getFullName();
        // 更新自己
        District updDistrict = new District();
        updDistrict.setId(districtUpdatePO.getId());
        updDistrict.setName(districtUpdatePO.getName());
        updDistrict.setFullName(oldFullName.replace(district.getName(), districtUpdatePO.getName()));
        districtService.updateById(updDistrict);

        // 更新下级
        districtService.updateChildName(district.getId(), oldFullName, updDistrict.getFullName());

        removeCache();
    }

    /**
     * 删除地区
     *
     * @param id 地区id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Integer id) {
        District district = districtService.getById(id);
        Assert.isTrue(ObjectUtil.isNotNull(district), "地区不存在");
        Assert.isTrue(!districtService.hasChild(id), "地区存在下级, 无法删除");

        // 删除本级
        districtService.removeById(id);

        // 更新上级的hasChild字段
        if (!district.getParentId().equals(BasicConstants.ROOT_DISTRICT_PARENT_ID)) {
            District updParent = new District();
            updParent.setId(district.getParentId());
            updParent.setHasChild(districtService.hasChild(district.getParentId()));
            districtService.updateById(updParent);
        }

        removeCache();
    }

    /**
     * 移除缓存
     */
    @Override
    public void removeCache() {
        List<String> keys = new ArrayList<>();
        keys.addAll(redisTemplate.keys(String.format(BasicCacheConstants.DISTRICT_TREE_PREFIX, StrUtil.EMPTY) + "*"));
        keys.addAll(redisTemplate.keys(String.format(BasicCacheConstants.DISTRICT_LIST_PREFIX, StrUtil.EMPTY) + "*"));
        keys.addAll(redisTemplate.keys(String.format(BasicCacheConstants.DISTRICT_LIST_LEVEL_PREFIX, StrUtil.EMPTY) + "*"));
        keys.add(BasicCacheConstants.REGION_LIST_CACHE);
        keys.add(BasicCacheConstants.REGION_TREE_CACHE);
        RedisUtil.del(keys.toArray(new String[keys.size()]));
        log.info("=======> 移除地区缓存成功");
    }

    /**
     * 根据最后一级id 获取区域列表
     *
     * @param lastId 最后一级id
     * @return 层级区域列表
     */
    @Override
    public List<DistrictVO> listByLastId(Integer lastId) {
        DistrictVO lastDistrict = detail(lastId);

        List<Integer> parentIds = Arrays.asList(lastDistrict.getParentIds().split(StrUtil.COMMA))
                .stream().map(idStr -> Integer.parseInt(idStr)).collect(Collectors.toList());

        return recursionList(parentIds, 0);
    }

    @Override
    public DistrictVO getAreaByName(AreaNamePO areaNamePO) {
        District province = districtService.getByNameAndLeven(areaNamePO.getProvince(), 1, 0);
        if(province == null) {
            return null;
        }
        District city = districtService.getByNameAndLeven(areaNamePO.getCity(), 2, province.getId());
        if(city == null) {
            return null;
        }
        // 例如：广东的东莞，就没有区县，所以直接返回市级对象
        if(StringUtils.isEmpty(areaNamePO.getArea())) {
            return BeanUtil.copyProperties(city, DistrictVO.class);
        }
        District area = districtService.getByNameAndLeven(areaNamePO.getArea(), 3, city.getId());
        if(area == null) {
            // 例如：广东的东莞，就没有区县，所以直接返回市级对象
            return BeanUtil.copyProperties(city, DistrictVO.class);
        }
        return BeanUtil.copyProperties(area, DistrictVO.class);
    }

    /**
     * 递归查询层级列表
     *
     * @param parentIds 上级id的集合
     * @param level 查询到的层级
     * @return 层级列表
     */
    private List<DistrictVO> recursionList(List<Integer> parentIds, Integer level) {
        Integer parentId = level == 0 ? BasicConstants.ROOT_DISTRICT_PARENT_ID : (level > parentIds.size() ? null : parentIds.get(level - 1));
        Integer currentId = level > parentIds.size() -1 ? null : parentIds.get(level);
        if (ObjectUtil.isNull(parentId) || ObjectUtil.isNull(currentId)) {
            return null;
        }

        List<DistrictVO> districtList = list(parentId);
        DistrictVO selectDistrict = districtList.stream().filter(district -> district.getId().equals(currentId)).findFirst().get();
        selectDistrict.setChildList(recursionList(parentIds, level + 1));
        return districtList;
    }


    /**
     * 递归同步地区数据
     *
     * @param provinceAreaCityList 从腾讯地图获取省市区集合
     * @param syncDistrictList     当前同步的集合
     * @param level                当前同步等级
     * @param parentId             当前上级id
     * @param parentIds            上前上级id的集合 逗号隔开
     * @param parentNames          当前上级名称的集合 逗号隔开
     */
    private void syncDistrict(List<List<TencentDistrictBean>> provinceAreaCityList,
                              List<TencentDistrictBean> syncDistrictList,
                              Integer level, Integer parentId,
                              String parentIds, String parentNames) {
        if (CollectionUtils.isEmpty(syncDistrictList) || level > DistrictLevelEnum.STREET.getLevel()) {
            return;
        }

        // 从db中查询出已经存在的地区
        List<Integer> syncIds = syncDistrictList.stream().map(item -> item.getId()).collect(Collectors.toList());
        Map<Integer, District> dbDistrictMap = districtService.list(new LambdaQueryWrapper<District>()
                .in(District::getId, syncIds)).stream().collect(Collectors.toMap(District::getId, Function.identity()));

        List<District> insertList = new ArrayList<>();
        List<District> updateList = new ArrayList<>();
        syncDistrictList.forEach(syncDistrict -> {
            log.info("同步地区: id: {}, name: {}", syncDistrict.getId(), syncDistrict.getName());

            // 查看db中是否存在
            District dbDistrict = dbDistrictMap.get(syncDistrict.getId());

            // 属性赋值 只有是一级的特别行政区 才能使用简称
            District district = new District();
            district.setId(syncDistrict.getId());
            district.setName(SPECIAL_PROVINCES.contains(syncDistrict.getName()) && level.equals(DistrictLevelEnum.PROVINCE.getLevel()) ? syncDistrict.getName() : syncDistrict.getFullName());
            district.setParentId(parentId);
            district.setRegionName(StrUtil.EMPTY);
            district.setParentIds(StrUtil.isEmpty(parentIds) ? String.valueOf(syncDistrict.getId()) : String.format("%s,%s", parentIds, syncDistrict.getId()));
            district.setFullName(StrUtil.isEmpty(parentNames) ? district.getName() : String.format("%s,%s", parentNames, district.getName()));
            district.setLevel(level);
            district.setLat(syncDistrict.getLocation().getLat());
            district.setLng(syncDistrict.getLocation().getLng());
            district.setHasChild(Boolean.FALSE);

            // 如果当前同步的层级已经大于三级 无需再寻找下级
            if (level > DistrictLevelEnum.AREA.getLevel() || String.valueOf(district.getId()).length() > PROVINCE_CITY_AREA_CODE_LENGTH) {
                log.info("同步地区: id: {}, name: {}, 层级大于3, 无需递归下级", syncDistrict.getId(), syncDistrict.getName());
                // 将数据添加到待处理数组
                addToWaitList(district, dbDistrict, insertList, updateList);
                return;
            }
            // 判断当前级别是否是特别行政区的下级
            boolean izSpecialProvince = SPECIAL_PROVINCES.contains(parentNames) && level.equals(DistrictLevelEnum.CITY.getLevel());
            // 寻找下级数据
            List<TencentDistrictBean> children = listChildren(syncDistrict, level, izSpecialProvince, provinceAreaCityList);
            log.info("同步地区: id: {}, name: {}, 获取下级条数: {}", syncDistrict.getId(), syncDistrict.getName(), children.size());
            district.setHasChild(CollUtil.isNotEmpty(children));
            if (district.getHasChild()) {
                syncDistrict(provinceAreaCityList, children, level + 1, district.getId(), district.getParentIds(), district.getFullName());
            }

            // 将数据添加到待处理数组
            addToWaitList(district, dbDistrict, insertList, updateList);
        });

        // 执行批量操作
        saveBatchDistrict(insertList, updateList);
    }

    /**
     * 将数据添加进入待处理数组
     *
     * @param district   待添加的数据
     * @param dbDistrict db中已经存在的数据
     * @param insertList 待新增数组
     * @param updateList 待编辑数组
     */
    private void addToWaitList(District district, District dbDistrict, List<District> insertList, List<District> updateList) {
        // 根据db是否存在 判断是新增还是编辑
        if (ObjectUtil.isNull(dbDistrict)) {
            district.setCreateBy(LoginUserUtil.getLoginUser().getUserId());
            district.setCreateTime(new Date());
            insertList.add(district);
            log.info("同步地区: id: {}, name: {}, db中不存在, 添加到新增数组", district.getId(), district.getName());
        }
        // 值发生了变化才更新
        else {
            if (!district.equals(dbDistrict)) {
                district.setUpdateBy(LoginUserUtil.getLoginUser().getUserId());
                district.setUpdateTime(new Date());
                updateList.add(district);
                log.info("同步地区: id: {}, name: {}, 与数据库中数据不一致, 添加到编辑数组", district.getId(), district.getName());
                return;
            }
            log.info("同步地区: id: {}, name: {}, 与数据库中数据一致, 丢弃", district.getId(), district.getName());
        }
    }

    /**
     * 执行批量保存地区操作
     *
     * @param insertList 新增地区
     * @param updateList 编辑地区
     */
    private void saveBatchDistrict(List<District> insertList, List<District> updateList) {
        if (!CollectionUtils.isEmpty(insertList)) {
            log.info("同步地区: 执行批量新增");
            districtService.insertBatchSomeColumn(insertList);
        }

        if (!CollectionUtils.isEmpty(updateList)) {
            log.info("同步地区: 执行批量编辑");
            districtService.updateBatchSomeColumn(updateList);
        }
    }

    /**
     * 获取下级地区
     * 城市代码第3、4位为90的，为省直辖县
     * 如果是区级或者是省直辖县需要通过接口请求下级数据
     *
     * @param syncDistrict         当前同步的地区
     * @param level                当前同步地区的等级
     * @param izSpecialProvince    当前地区是否是否是特别行政区的下级
     * @param provinceAreaCityList 省市区集合
     * @return 下级地区
     */
    @SneakyThrows
    private List<TencentDistrictBean> listChildren(TencentDistrictBean syncDistrict, Integer level, Boolean izSpecialProvince, List<List<TencentDistrictBean>> provinceAreaCityList) {
        if (level > DistrictLevelEnum.AREA.getLevel() || String.valueOf(syncDistrict.getId()).length() > PROVINCE_CITY_AREA_CODE_LENGTH) {
            return new ArrayList<>();
        }

        String code = String.valueOf(syncDistrict.getId());
        code = code.substring(2, code.length() - 2);
        // 如果当前是区级或者是省直辖县需要通过接口请求下级数据
        if (level.equals(DistrictLevelEnum.AREA.getLevel()) || code.equals(PROVINCE_DIRECTLY_UNDER_COUNTY_CODE)) {
            // 免费的腾讯key 请求速度有上限
            Thread.sleep(200);
            return TencentMapUtils.getChildrenDistrict(syncDistrict.getId());
        }
        // 如果是特别行政区 替换第四位编码后 将自己返回
        else if (SPECIAL_PROVINCES.contains(syncDistrict.getName())) {
            TencentDistrictBean subDistrict = BeanUtil.copyProperties(syncDistrict, TencentDistrictBean.class);
            subDistrict.setId(transSpecialProvinceCode(subDistrict.getId(), "1"));
            subDistrict.setName(subDistrict.getFullName());
            return Arrays.asList(subDistrict);
        }
        // 当前同步的地区是省或者市, 去已有的数据中匹配
        else {
            int len = level == 1 ? 2 : 4;
            String subId = String.valueOf(syncDistrict.getId()).substring(0, len);
            int index = izSpecialProvince ? level - 1 : level;
            return provinceAreaCityList.get(index).stream()
                    .filter(x -> String.valueOf(x.getId()).substring(0, len).equals(subId)).collect(Collectors.toList());
        }
    }

    /**
     * 转换特别行政区域编码
     *
     * @param id         当前id
     * @param replaceNum 替换的编码
     * @return 转换后的编码
     */
    private Integer transSpecialProvinceCode(Integer id, String replaceNum) {
        String idStr = String.valueOf(id);
        idStr = idStr.substring(0, 3) + replaceNum + idStr.substring(4);
        return Integer.parseInt(idStr);
    }

    /**
     * 获取10位数组随机数
     */
    private Integer getRandomNum() {
        Random random = new Random();
        int num = random.nextInt(999999999) + 1;
        return num > 0 ? num * -1 : num;
    }
}