package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.util.HeaderUtil;
import com.hishop.setting.config.annotation.RefreshSetting;
import com.hishop.wine.biz.PaymentSettingBiz;
import com.hishop.wine.common.enums.PaymentEnum;
import com.hishop.wine.constants.SettingConstants;
import com.hishop.wine.enums.OfflineChannel;
import com.hishop.wine.model.po.payment.PaymentSettingOfflinePO;
import com.hishop.wine.model.po.payment.PaymentSettingSavePO;
import com.hishop.wine.model.po.payment.PaymentSettingWxPO;
import com.hishop.wine.model.vo.payment.*;
import com.hishop.wine.repository.dto.payment.PaymentSettingDTO;
import com.hishop.wine.repository.entity.MiniAppPayment;
import com.hishop.wine.repository.entity.PaymentCertificate;
import com.hishop.wine.repository.entity.PaymentSetting;
import com.hishop.wine.repository.service.MiniAppPaymentService;
import com.hishop.wine.repository.service.PaymentCertificateService;
import com.hishop.wine.repository.service.PaymentSettingService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 支付设置 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-07-18
 */

@Slf4j
@Service
public class PaymentSettingBizImpl implements PaymentSettingBiz {

    @Resource
    private PaymentSettingService paymentSettingService;
    @Resource
    private PaymentCertificateService paymentCertificateService;
    @Resource
    private MiniAppPaymentService miniAppPaymentService;

    /**
     * 新增支付设置
     *
     * @param paymentSettingSavePO 新增支付设置参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RefreshSetting(names = {SettingConstants.WX_PAY_SETTING})
    public void create(PaymentSettingSavePO paymentSettingSavePO) {
        paymentSettingSavePO.setId(null);
        save(paymentSettingSavePO);
    }

    /**
     * 编辑支付设置
     *
     * @param paymentSettingSavePO 编辑支付设置参数
     */
    @Override
    @RefreshSetting(names = {SettingConstants.WX_PAY_SETTING, SettingConstants.WX_MAPPING_SETTING})
    public void update(PaymentSettingSavePO paymentSettingSavePO) {
        Assert.isTrue(ObjectUtil.isNotNull(paymentSettingSavePO.getId()), "支付设置id不能为空");
        save(paymentSettingSavePO);
    }

    /**
     * 保存支付设置
     *
     * @param paymentSettingSavePO 保存支付设置参数
     */
    @Override
    public void save(PaymentSettingSavePO paymentSettingSavePO) {
        PaymentEnum.Type paymentType = PaymentEnum.Type.getEnumByType(paymentSettingSavePO.getPaymentType());
        Assert.isTrue(ObjectUtil.isNotNull(paymentType), "支付类型错误");

        if (paymentSettingSavePO.getId() != null){
            PaymentSetting paymentSetting = paymentSettingService.getById(paymentSettingSavePO.getId());
            Assert.isTrue(paymentSetting.getPaymentType().equals(paymentSettingSavePO.getPaymentType()), "暂不支持支付类型更新");
        }

        switch (paymentType) {
            case WX_PAY:
                saveWxPay(paymentSettingSavePO);
                break;
            case OFFLINE:
                saveOfflinePay(paymentSettingSavePO);
                break;
            default:
                break;
        }
    }

    /**
     * 保存线下支付设置
     *
     * @param paymentSettingSavePO 保存支付设置参数
     */

    private void saveOfflinePay(PaymentSettingSavePO paymentSettingSavePO) {
        PaymentSettingOfflinePO paymentSettingOfflinePO = paymentSettingSavePO.getOfflinePay();
        Assert.isTrue(paymentSettingSavePO.getOfflineChannel() != null, "线下支付渠道不能为空");
        Assert.isTrue(ObjectUtil.isNotNull(paymentSettingOfflinePO), "请添加线下支付配置");
        paymentSettingOfflinePO.checkParam();

        if (OfflineChannel.getOfflineChannelEnumByType(paymentSettingSavePO.getOfflineChannel()) == OfflineChannel.BANK_CARD_TRANSFER) {
            Assert.isTrue(StringUtils.isNotBlank(paymentSettingOfflinePO.getBankName()), "开户银行名称不能为空");
        }
        PaymentSetting paymentSetting = BeanUtil.copyProperties(paymentSettingSavePO, PaymentSetting.class);
        paymentSetting.setSettingValue(JSONObject.toJSONString(paymentSettingOfflinePO));
        paymentSettingService.saveOrUpdate(paymentSetting);
    }

    /**
     * 删除支付设置
     *
     * @param id 支付设置id
     */
    @Override
    public void deleteById(Long id) {
        long count = miniAppPaymentService.count(new LambdaQueryWrapper<MiniAppPayment>().eq(MiniAppPayment::getPaymentId, id));
        Assert.isTrue(count == 0, "该支付设置已被使用，无法删除");

        paymentSettingService.removeById(id);
    }

    /**
     * 查询支付设置详情
     *
     * @param id 支付设置id
     * @return 支付设置详情
     */
    @Override
    public PaymentSettingDetailVO detail(Long id) {
        PaymentSetting entity = paymentSettingService.getById(id);
        Assert.isTrue(ObjectUtil.isNotNull(entity), "支付设置不存在");
        PaymentSettingDetailVO detailVO = BeanUtil.copyProperties(entity, PaymentSettingDetailVO.class);
        PaymentEnum.Type paymentType = PaymentEnum.Type.getEnumByType(entity.getPaymentType());

        switch (paymentType) {
            case WX_PAY:
                detailVO.setWxPay(JSONObject.parseObject(entity.getSettingValue(), PaymentSettingWxVO.class));
                break;
            case OFFLINE:
                detailVO.setOfflinePay(JSONObject.parseObject(entity.getSettingValue(), PaymentSettingOfflinePO.class));
                break;
            default:
                break;
        }
        return detailVO;
    }

    /**
     * 查询支付设置列表
     *
     * @return 支付设置列表
     */
    @Override
    public List<PaymentSettingVO> list() {
        List<PaymentSettingDTO> dbList = paymentSettingService.listAll();
        List<PaymentSettingVO> paymentList = BeanUtil.copyToList(dbList, PaymentSettingVO.class);
        paymentList.forEach(payment -> payment.setPaymentTypeName(PaymentEnum.Type.getNameByType(payment.getPaymentType())));
        return paymentList;
    }

    /**
     * 上传支付证书
     *
     * @param file 证书文件
     * @return 证书id
     */
    @Override
    @SneakyThrows
    public Long uploadCertificate(MultipartFile file) {
        PaymentCertificate paymentCertificate = new PaymentCertificate();
        paymentCertificate.setContent(file.getBytes());
        paymentCertificateService.save(paymentCertificate);
        return paymentCertificate.getId();
    }

    /**
     * 支付设置下拉
     *
     * @return 支付设置下拉
     */
    @Override
    public List<PaymentSettingSelectVO> listForSelect(String group) {
        LambdaQueryWrapper<PaymentSetting> queryWrapper = new LambdaQueryWrapper<>();

        // 支付类型分组
        if (StringUtils.isNotBlank(group)){
            List<PaymentEnum.Type> typeList = PaymentEnum.Type.getEnumByGroup(group);
            if (CollectionUtils.isEmpty(typeList)) {
                return Lists.newArrayList();
            }
            queryWrapper.in(PaymentSetting::getPaymentType, typeList.stream().map(PaymentEnum.Type::getType).collect(Collectors.toList()));
        }
        return BeanUtil.copyToList(paymentSettingService.list(queryWrapper), PaymentSettingSelectVO.class);
    }

    @Override
    public List<PaymentSettingOfflineVO> listForOfflineSelect() {
        return BeanUtil.copyToList(paymentSettingService.listForOfflineSelect(HeaderUtil.getModuleCode()), PaymentSettingOfflineVO.class);
    }

    /**
     * 保存微信支付设置
     *
     * @param paymentSettingSavePO 微信支付设置参数
     */
    private void saveWxPay(PaymentSettingSavePO paymentSettingSavePO) {
        PaymentSettingWxPO setting = paymentSettingSavePO.getWxPay();
        Assert.isTrue(ObjectUtil.isNotNull(setting), "请添加微信支付配置");
        setting.checkParam();
        checkMerchantId(setting.getMchId(), paymentSettingSavePO.getId());
        checkCert(setting.getCertificateId());

        PaymentSetting paymentSetting = BeanUtil.copyProperties(paymentSettingSavePO, PaymentSetting.class);
        paymentSetting.setSettingValue(JSONObject.toJSONString(setting));
        paymentSetting.setMerchantId(setting.getMchId());
        paymentSettingService.saveOrUpdate(paymentSetting);
    }

    /**
     * 检测商户好是否存在
     *
     * @param merchantId 商户id
     * @param id         支付设置id
     */
    private void checkMerchantId(String merchantId, Long id) {
        long count = paymentSettingService.count(new LambdaQueryWrapper<PaymentSetting>()
                .eq(PaymentSetting::getMerchantId, merchantId)
                .ne(ObjectUtil.isNotNull(id) && id > 0, PaymentSetting::getId, id));
        Assert.isTrue(count == 0, "该商户已绑定, 无需重复绑定");
    }

    /**
     * 检测证书是否存在
     *
     * @param certificateId 证书id
     */
    private void checkCert(Long certificateId) {
        long count = paymentCertificateService.count(new LambdaQueryWrapper<PaymentCertificate>().eq(PaymentCertificate::getId, certificateId));
        Assert.isTrue(count > 0, "证书不存在");
    }
}