package com.hishop.wine.biz.impl;

import cn.hutool.core.lang.generator.SnowflakeGenerator;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hishop.common.util.RedisUtil;
import com.hishop.nfs.api.NFS;
import com.hishop.wine.biz.NfsBiz;
import com.hishop.wine.constants.BasicConstants;
import com.hishop.wine.constants.NfsCacheConstants;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * nfsbiz
 *
 * <AUTHOR>
 * @date : 2023/7/5
 */
@Slf4j
@Service
@AllArgsConstructor
public class NfsBizImpl implements NfsBiz {

    @Resource
    private NFS nfs;
    @Resource
    private SnowflakeGenerator snowflakeGenerator;

    /**
     * 上传文件路径集合(obs 暂时未集成)
     */
    private final static List<String> POLICY_PATHS = new ArrayList<>();
    /**
     * 文件上传最大限制(obs 没有此参数)
     */
    private final static long POLICY_CONTENT_LENGTH = 1048576000L;
    /**
     * 凭据过期时间(12个小时)
     */
    private final static int POLICY_EXPIRE_MILLIS = 60 * 60 * 12;

    /**
     * 文件上传保存路径
     */
    private final static String UPLOAD_PATH = "hishop/images/basicSystem/";

    /**
     * 获取临时凭据
     *
     * @return 临时凭据
     */
    @Override
    public Map<String, String> getTemporaryCredentials() {
        Map<String, String> credentials = RedisUtil.get(NfsCacheConstants.TEMPORARY_CREDENTIALS);
        if (ObjectUtil.isNotNull(credentials)) {
            return credentials;
        }

        credentials = nfs.getPostPolicy(POLICY_PATHS, POLICY_CONTENT_LENGTH, POLICY_EXPIRE_MILLIS);
        credentials.put("bucketName", nfs.getBucketName());
        credentials.put("path", BasicConstants.WEB_UPLOAD_PATH);

        // 过期时间略低实际过期时间
        RedisUtil.set(NfsCacheConstants.TEMPORARY_CREDENTIALS, credentials, POLICY_EXPIRE_MILLIS - 60 * 60);
        return credentials;
    }

    @Override
    public String upload(MultipartFile file) {
        try {
            //获取文件后缀
            String suffix = StrUtil.subAfter(file.getOriginalFilename(), ".", true);
            String filePath = UPLOAD_PATH + snowflakeGenerator.next() + "." + suffix;
            nfs.upload(filePath, file.getBytes());
            return filePath;
        } catch (IOException e) {
            log.error("文件上传失败", e);
            e.printStackTrace();
        }
        return null;
    }
}
