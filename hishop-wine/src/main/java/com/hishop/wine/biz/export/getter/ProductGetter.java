package com.hishop.wine.biz.export.getter;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.hishop.common.export.context.DataContext;
import com.hishop.common.export.context.DataWrapper;
import com.hishop.common.export.getter.DefaultExportGetter;
import com.hishop.common.export.model.BizType;
import com.hishop.common.response.PageResult;
import com.hishop.wine.biz.ProductBiz;
import com.hishop.wine.biz.export.enums.BizTypeEnum;
import com.hishop.wine.biz.export.model.ProductEO;
import com.hishop.wine.biz.export.wrapper.ProductWrapper;
import com.hishop.wine.model.po.product.ProductQueryPO;
import com.hishop.wine.model.vo.product.ProductPageVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 产品导出数据获取
 *
 * @author: HuBiao
 * @date: 2023-07-05
 */
@Service
public class ProductGetter extends DefaultExportGetter<ProductQueryPO> {

    @Resource
    private ProductBiz productBiz;

    @Override
    public DataContext dataContext(ProductQueryPO param) {
        PageResult<ProductPageVO> page =  productBiz.pageList(param);
        List<ProductEO> productEOS = BeanUtil.copyToList(page.getList(), ProductEO.class);
        DataWrapper<ProductEO> wrapper = new ProductWrapper(productEOS);
        return DataContext.valueOf(Collections.singletonList(wrapper));
    }

    @Override
    public BizType bizType() {
        return BizTypeEnum.PRODUCT;
    }

    @Override
    public String fileName() {
        return BizTypeEnum.PRODUCT.desc();
    }
}
