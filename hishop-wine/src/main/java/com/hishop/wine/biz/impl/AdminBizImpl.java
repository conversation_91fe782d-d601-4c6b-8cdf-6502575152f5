package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.hishop.common.constants.LoginConstants;
import com.hishop.common.enums.IdentityTypeEnums;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import com.hishop.common.util.HeaderUtil;
import com.hishop.common.util.PasswordUtil;
import com.hishop.common.util.RedisUtil;
import com.hishop.wine.biz.AdminBiz;
import com.hishop.wine.biz.RoleBiz;
import com.hishop.wine.biz.UserBiz;
import com.hishop.wine.model.po.DepartmentUpdateIdPO;
import com.hishop.wine.model.po.admin.BusinessUserQueryPo;
import com.hishop.wine.model.po.basic.AdminCreatePO;
import com.hishop.wine.model.po.basic.AdminQueryPO;
import com.hishop.wine.model.po.basic.AdminUpdatePO;
import com.hishop.wine.model.vo.basic.AdminDetailVO;
import com.hishop.wine.model.vo.basic.AdminVO;
import com.hishop.wine.model.vo.sale.SaleAreaSimpleVo;
import com.hishop.wine.model.vo.user.BusinessUserVo;
import com.hishop.wine.model.vo.user.UserDetailVo;
import com.hishop.wine.repository.entity.*;
import com.hishop.wine.repository.param.AdminParam;
import com.hishop.wine.repository.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.hishop.wine.constants.Constant.DEFAULT_SALE_AREA_SPLIT;

/**
 * 管理员管理
 *
 * <AUTHOR>
 * @date : 2023/7/25
 */
@Slf4j
@Service
public class AdminBizImpl implements AdminBiz {

    @Resource
    private UserService userService;
    @Resource
    private IdentityService identityService;
    @Resource
    private UserBiz userBiz;
    @Resource
    private RoleBiz roleBiz;

    @Resource
    private DepartmentEmployeeService departmentEmployeeService;

    @Resource
    private UserAreaService userAreaService;

    @Resource
    private SaleAreaService saleAreaService;

    @Resource
    private RoleService roleService;

    /**
     * 创建管理员
     *
     * @param adminCreatePO 创建管理员参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createAdmin(AdminCreatePO adminCreatePO) {
        adminCreatePO.check();

        // 检测用户名是否被使用
        User otherUser = userService.getUserByUsername(adminCreatePO.getUsername());
        if (otherUser != null) {
            if (StringUtils.isNotBlank(otherUser.getMobile()) &&
                    !otherUser.getMobile().equals(adminCreatePO.getMobile())) {
                throw new BusinessException("该用户名已绑定其他手机号，请换个用户名试试吧~");
            }
            Identity identity = identityService.getByUserId(otherUser.getId(), IdentityTypeEnums.ADMIN.getType());
            Assert.isTrue(identity == null, "该用户名已被绑定, 请换个用户名试试吧~");
        }
        // 根据手机号去匹配用户
        User user = userBiz.getUserIdentityByMobile(adminCreatePO.getMobile(), IdentityTypeEnums.ADMIN);
        if (ObjectUtil.isNull(user)) {
            // 如果用户不存在,则创建用户
            if (ObjectUtil.isNull(otherUser)) {
                user = new User();
            } else {
                //手机号虽然没有账号，但是用户名有账号，且对应的手机号为空，此时需要更新手机号而不是新增账号
                user = otherUser;
            }
        } else {
            // 如果用户存在,则判断是否存在当前身份
            Assert.isTrue(ObjectUtil.isNull(user.getIdentity()), "该手机号账户已存在, 无需重复添加~");
        }
        // 如果有历史用户 则把管理员与业务员的身份给删了以及部门关系给删了
        if(user.getId() != null) {
            identityService.remove(new LambdaUpdateWrapper<Identity>().eq(Identity::getUserId, user.getId())
                    .in(Identity::getIdentityType, IdentityTypeEnums.ADMIN.getType(), IdentityTypeEnums.BUSINESS.getType()));
            departmentEmployeeService.remove(new LambdaUpdateWrapper<DepartmentEmployee>().eq(DepartmentEmployee::getUserId, user.getId()));
        }

        // 覆盖用户信息
        BeanUtil.copyProperties(adminCreatePO, user);

        // 检测角色信息
        roleBiz.checkAndGetRole(adminCreatePO.getRoleId(), Boolean.TRUE);
        user.setPassword(PasswordUtil.encrypt(user.getPassword()));
        // 保存用户信息
        userService.saveOrUpdate(user);

        Long userId = user.getId();
        // 关联用户身份
        Identity identity = new Identity();
        identity.setUserId(userId);
        identity.setIdentityType(IdentityTypeEnums.ADMIN.getType());
        identity.setModuleCode(HeaderUtil.getModuleCode());
        identity.setRoleId(adminCreatePO.getRoleId());
        identityService.saveOrUpdate(identity);

        // 如果有业务员权限 就新增业务员身份
        if(adminCreatePO.getIzBusinessUser()) {
            Identity identity2 = new Identity();
            identity2.setUserId(userId);
            identity2.setIdentityType(IdentityTypeEnums.BUSINESS.getType());
            identity2.setModuleCode(HeaderUtil.getModuleCode());
            identity2.setRoleId(adminCreatePO.getRoleId());
            identityService.saveOrUpdate(identity2);
        }

        // 保存用户销售区域
        if (adminCreatePO.getIzBusinessUser() != null && adminCreatePO.getIzBusinessUser()) {
            if (CollectionUtils.isNotEmpty(adminCreatePO.getSaleAreaList())) {
                userAreaService.saveBatch(adminCreatePO.getSaleAreaList().stream().map(saleAreaId -> {
                    UserArea userArea = new UserArea();
                    userArea.setUserId(userId);
                    userArea.setSaleAreaId(saleAreaId);
                    return userArea;
                }).collect(Collectors.toList()));
            }
        }

        //部门关联用
        DepartmentEmployee departmentEmployee = new DepartmentEmployee();
        departmentEmployee.setDepartmentId(adminCreatePO.getDepartmentId());
        departmentEmployee.setUserId(user.getId());
        departmentEmployee.setIzHead(Boolean.FALSE);
        departmentEmployeeService.add(departmentEmployee);
    }

    /**
     * 更新管理员
     *
     * @param adminUpdatePO 更新管理员参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAdmin(AdminUpdatePO adminUpdatePO) {
        adminUpdatePO.check();

        Identity identity = identityService.getOne(new LambdaQueryWrapper<Identity>()
                .eq(Identity::getUserId, adminUpdatePO.getId())
                .eq(Identity::getIdentityType, IdentityTypeEnums.ADMIN.getType())
                .eq(Identity::getIzDelete, false).last("limit 1"));
        Assert.isTrue(ObjectUtil.isNotNull(identity), "用户身份不存在");

        User user = userService.getById(adminUpdatePO.getId());
        Assert.isTrue(ObjectUtil.isNotNull(user), "用户不存在");

        // 检测角色信息
        roleBiz.checkAndGetRole(adminUpdatePO.getRoleId(), Boolean.TRUE);

        // 更新用户信息
        User updUser = BeanUtil.copyProperties(adminUpdatePO, User.class);
        updUser.setId(user.getId());
        if (StrUtil.isNotEmpty(updUser.getPassword())) {
            updUser.setPassword(PasswordUtil.encrypt(updUser.getPassword()));
        }
        userService.updateById(updUser);

        if(adminUpdatePO.getIzBusinessUser()) {
            identity.setRoleId(adminUpdatePO.getRoleId());
            identityService.saveOrUpdate(identity);

            Identity identityBusiness = identityService.getOne(new LambdaQueryWrapper<Identity>()
                    .eq(Identity::getUserId, adminUpdatePO.getId())
                    .eq(Identity::getIdentityType, IdentityTypeEnums.BUSINESS.getType())
                    .eq(Identity::getIzDelete, false).last("limit 1"));
            if(identityBusiness == null) {
                identityBusiness = new Identity();
                identityBusiness.setUserId(adminUpdatePO.getId());
                identityBusiness.setIdentityType(IdentityTypeEnums.BUSINESS.getType());
                identityBusiness.setModuleCode(HeaderUtil.getModuleCode());
                identityBusiness.setRoleId(adminUpdatePO.getRoleId());
            }
            identityBusiness.setRoleId(adminUpdatePO.getRoleId());
            identityService.saveOrUpdate(identityBusiness);
        } else {
            // 更新身份信息
            identity.setRoleId(adminUpdatePO.getRoleId());
            identityService.updateById(identity);


            List<Identity> list = identityService.list(new LambdaQueryWrapper<Identity>()
                    .eq(Identity::getUserId, adminUpdatePO.getId()).eq(Identity::getIdentityType, IdentityTypeEnums.BUSINESS.getType()));
            if(CollectionUtils.isNotEmpty(list)) {
                list.forEach(f->{
                    // 清空token
                    String redisKey = String.format(LoginConstants.LOGIN_USER_MARK, f.getUserId(), f.getId());
                    RedisUtil.del(redisKey);
                });
            }
            // 并且把业务员身份清理掉
            identityService.remove(new LambdaUpdateWrapper<Identity>()
                    .eq(Identity::getUserId, adminUpdatePO.getId())
                    .eq(Identity::getIdentityType, IdentityTypeEnums.BUSINESS.getType())
                    .eq(Identity::getModuleCode, HeaderUtil.getModuleCode()));

        }
        userAreaService.remove(new LambdaUpdateWrapper<UserArea>().eq(UserArea::getUserId, adminUpdatePO.getId()));

        // 保存用户销售区域
        if (adminUpdatePO.getIzBusinessUser() != null && adminUpdatePO.getIzBusinessUser()) {
            if (CollectionUtils.isNotEmpty(adminUpdatePO.getSaleAreaList())) {
                userAreaService.saveBatch(adminUpdatePO.getSaleAreaList().stream().map(saleAreaId -> {
                    UserArea userArea = new UserArea();
                    userArea.setUserId(user.getId());
                    userArea.setSaleAreaId(saleAreaId);
                    return userArea;
                }).collect(Collectors.toList()));
            }
        }

        DepartmentUpdateIdPO departmentEmployee = new DepartmentUpdateIdPO();
        departmentEmployee.setDepartmentId(adminUpdatePO.getDepartmentId());
        departmentEmployee.setUserIds(Lists.newArrayList(user.getId()));
        departmentEmployee.setIzHead(Boolean.FALSE);

        DepartmentEmployee queryEmployee = departmentEmployeeService.getByDepartmentIdAndUserId(user.getId());
        if (null != queryEmployee && queryEmployee.getDepartmentId().equals(adminUpdatePO.getDepartmentId())) {
            departmentEmployee.setIzHead(queryEmployee.getIzHead());
        }
        departmentEmployeeService.updateDep(departmentEmployee);
    }


    /**
     * 查询管理员列表
     *
     * @param adminQueryPO 分页参数
     * @return 管理员列表
     */
    @Override
    public PageResult<AdminVO> pageListAdmin(AdminQueryPO adminQueryPO) {
        AdminParam adminParam = BeanUtil.copyProperties(adminQueryPO, AdminParam.class);
        adminParam.setIdentityTypeList(Lists.newArrayList(IdentityTypeEnums.ADMIN.getType()));
        Page<User> result = userService.pageListAdmin(adminQueryPO.buildPage(), adminParam);
        return PageResultHelper.transfer(result, AdminVO.class);
    }

    @Override
    public PageResult<AdminVO> pageListAdminDep(AdminQueryPO adminQueryPO) {
        Page<AdminVO> result = userService.pageListAdminDep(adminQueryPO.buildPage(), BeanUtil.copyProperties(adminQueryPO, AdminParam.class));
        return PageResultHelper.transfer(result, AdminVO.class);
    }

    /**
     * 查询管理员详情
     *
     * @param id 身份id
     * @return 管理员详情
     */
    @Override
    public AdminDetailVO getAdminDetail(Long id) {
        Identity identity = identityService.getById(id);
        Assert.isTrue(ObjectUtil.isNotNull(identity), "用户身份不存在");

        User user = userService.getById(identity.getUserId());
        Assert.isTrue(ObjectUtil.isNotNull(user), "用户不存在");

        AdminDetailVO adminVO = BeanUtil.copyProperties(user, AdminDetailVO.class);
        BeanUtil.copyProperties(identity, adminVO);
        return adminVO;
    }

    @Override
    public UserDetailVo userDetail(Long id) {

        Identity identity = identityService.getById(id);
        Assert.isTrue(ObjectUtil.isNotNull(identity), "用户身份不存在");

        User user = userService.getById(identity.getUserId());
        Assert.isTrue(ObjectUtil.isNotNull(user), "用户不存在");

        UserDetailVo userDetailVo = BeanUtil.copyProperties(user, UserDetailVo.class);
        userDetailVo.setIzBusinessUser(Boolean.FALSE);
        userDetailVo.setRoleId(identity.getRoleId());

        Identity identityBusiness = identityService.getOne(new LambdaQueryWrapper<Identity>()
                .eq(Identity::getUserId, identity.getUserId()).eq(Identity::getIzDelete, false)
                .eq(Identity::getIdentityType, IdentityTypeEnums.BUSINESS.getType()).last("limit 1"));

        //查询角色信息
        if (identity.getRoleId() != null){
            Role role = roleService.getById(identity.getRoleId());
            userDetailVo.setRoleName(role.getName());
        }

        //判断是否是业务员
        if (identityBusiness != null) {
            userDetailVo.setIzBusinessUser(Boolean.TRUE);
            userDetailVo.setSaleAreaList(getSaleAreaList(user.getId()));
        }

        DepartmentEmployee queryEmployee = departmentEmployeeService.getByDepartmentIdAndUserId(user.getId());
        if (null != queryEmployee) {
            userDetailVo.setDepartmentId(queryEmployee.getDepartmentId());
            userDetailVo.setIzHead(queryEmployee.getIzHead());
        }

        return userDetailVo;
    }


    @Override
    public PageResult<BusinessUserVo> businessPageList(BusinessUserQueryPo queryPo) {

        AdminParam adminParam = BeanUtil.copyProperties(queryPo, AdminParam.class);
        adminParam.setIdentityType(IdentityTypeEnums.BUSINESS.getType());
        adminParam.setStatus(1);
        Page<User> result = userService.pageListAdmin(queryPo.buildPage(), adminParam);

        PageResult<BusinessUserVo> pageResult = PageResultHelper.transfer(result, BusinessUserVo.class);
        if (CollectionUtils.isNotEmpty(pageResult.getList())) {
            //查询销售区域信息
            List<UserArea> userAreaList = userAreaService.list(new LambdaQueryWrapper<UserArea>()
                    .eq(UserArea::getIzDelete, Boolean.FALSE)
                    .in(UserArea::getUserId, pageResult.getList().stream().map(BusinessUserVo::getUserId).distinct().collect(Collectors.toList())));

            if (CollectionUtils.isNotEmpty(userAreaList)) {
                Map<Long, List<Long>> userAreaIdList = userAreaList.stream().collect(Collectors.groupingBy(UserArea::getUserId, Collectors.mapping(UserArea::getSaleAreaId, Collectors.toList())));
                pageResult.getList().forEach(user -> {
                    if (userAreaIdList.containsKey(user.getUserId())) {
                        List<SaleArea> saleAreaList = saleAreaService.list(new LambdaQueryWrapper<SaleArea>()
                                .eq(SaleArea::getIzDelete, Boolean.FALSE)
                                .in(SaleArea::getId, userAreaIdList.get(user.getUserId()).stream().distinct().collect(Collectors.toList())));
                        user.setSaleAreaList(saleAreaList.stream().map(SaleArea::getFullName).collect(Collectors.toList()));
                    }
                });
            }
        }
        return pageResult;
    }

    /**
     * 获取销售区域信息列表
     *
     * @param id 主键
     * @return list
     */
    private List<List<SaleAreaSimpleVo>> getSaleAreaList(Long id) {
        //查询销售区域信息
        List<UserArea> userAreaList = userAreaService.list(new LambdaQueryWrapper<UserArea>()
                .eq(UserArea::getIzDelete, Boolean.FALSE)
                .eq(UserArea::getUserId, id));

        List<List<SaleAreaSimpleVo>> allAreaList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(userAreaList)) {
            List<SaleArea> saleAreaList = saleAreaService.list(new LambdaQueryWrapper<SaleArea>().eq(SaleArea::getIzDelete, Boolean.FALSE)
                    .in(SaleArea::getId, userAreaList.stream().map(UserArea::getSaleAreaId).distinct().collect(Collectors.toList())));
            for (SaleArea saleArea : saleAreaList) {
                List<SaleAreaSimpleVo> areaList = org.apache.commons.compress.utils.Lists.newArrayList();

                String[] codes = saleArea.getFullCode().split("[" + DEFAULT_SALE_AREA_SPLIT + "]");
                saleAreaService.list(new LambdaQueryWrapper<SaleArea>().eq(SaleArea::getIzDelete, Boolean.FALSE)
                        .in(SaleArea::getCode, Arrays.asList(codes))).forEach(area -> {
                    SaleAreaSimpleVo saleAreaSimpleVo = new SaleAreaSimpleVo();
                    saleAreaSimpleVo.setId(area.getId());
                    saleAreaSimpleVo.setName(area.getName());
                    areaList.add(saleAreaSimpleVo);
                });
                allAreaList.add(areaList);
            }
        }
        return allAreaList;
    }

    @Override
    public UserDetailVo userDetailByUserId(Long userId) {

        Identity identity = identityService.getOne(new LambdaQueryWrapper<Identity>()
                .eq(Identity::getUserId, userId).eq(Identity::getIzDelete, false)
                .eq(Identity::getIdentityType, IdentityTypeEnums.ADMIN.getType()).last("limit 1"));

        Assert.isTrue(ObjectUtil.isNotNull(identity), "用户身份不存在");

        User user = userService.getById(identity.getUserId());
        Assert.isTrue(ObjectUtil.isNotNull(user), "用户不存在");

        UserDetailVo userDetailVo = BeanUtil.copyProperties(user, UserDetailVo.class);
        userDetailVo.setIzBusinessUser(Boolean.FALSE);
        userDetailVo.setRoleId(identity.getRoleId());

        Identity identityBusiness = identityService.getOne(new LambdaQueryWrapper<Identity>()
                .eq(Identity::getUserId, identity.getUserId()).eq(Identity::getIzDelete, false)
                .eq(Identity::getIdentityType, IdentityTypeEnums.BUSINESS.getType()).last("limit 1"));

        //查询角色信息
        if (identity.getRoleId() != null){
            Role role = roleService.getById(identity.getRoleId());
            userDetailVo.setRoleName(role.getName());
        }

        //判断是否是业务员
        if (identityBusiness != null) {
            userDetailVo.setIzBusinessUser(Boolean.TRUE);
            userDetailVo.setSaleAreaList(getSaleAreaList(user.getId()));
        }

        DepartmentEmployee queryEmployee = departmentEmployeeService.getByDepartmentIdAndUserId(user.getId());
        if (null != queryEmployee) {
            userDetailVo.setDepartmentId(queryEmployee.getDepartmentId());
            userDetailVo.setIzHead(queryEmployee.getIzHead());
        }

        return userDetailVo;
    }
}
