package com.hishop.wine.biz;

import com.hishop.wine.model.po.wechat.WechatCodeCreatePO;
import com.hishop.wine.model.po.wechat.WechatSavePO;
import com.hishop.wine.model.vo.wechat.WechatCodeVO;

/**
 * 微信二维码表 业务逻辑接口
 *
 * @author: LiGuoQiang
 * @date: 2023-06-20
 */

public interface WechatCodeBiz {

    /**
     * 保存微信二维码数据
     *
     * <AUTHOR>
     * @date 2023/6/20
     */
    void save(WechatSavePO savePO);

    /**
     * 根据key获取微信二维码信息
     *
     * @param codeKey 二维码唯一key
     * @return 微信二维码对象
     */
    WechatCodeVO qryByKey(String codeKey);


    /**
     * 生成推广码
     *
     * <AUTHOR>
     * @date 2023/6/19
     */
    String genMaCode(WechatCodeCreatePO createPo);

    /**
     * 获取小程序地址(带参数)
     *
     * @param scene 二维码唯一Key
     * @return 小程序地址
     */
    String getRealPage(String scene);

    /**
     * 生成宴席门店太阳码
     * @param createPo
     * @return
     */
    String genFeastMaCode(WechatCodeCreatePO createPo);

    String getWxacode(WechatCodeCreatePO createPo);

    String getWxacodeByBizPath(WechatCodeCreatePO createPo);

    /**
     * 生成推广码
     *
     * <AUTHOR>
     * @date 2023/6/19
     */
    String genMaCodeByModule(WechatCodeCreatePO createPo);
}