package com.hishop.wine.biz;

import com.hishop.wine.model.po.SaleAreaAddPo;
import com.hishop.wine.model.po.SaleAreaQueryPo;
import com.hishop.wine.model.po.SaleAreaUpdatePo;
import com.hishop.wine.model.vo.sale.SaleAreaVo;

import java.util.List;

/**
 * @description: 销售区域业务接口
 * @author: chenzw
 * @date: 2024/7/4 09:32
 */
public interface SaleAreaBiz {

    /**
     * 查询销售区域列表
     * @param saleAreaQueryPo 销售区域查询请求参数
     * @return 销售区域列表
     */
    List<SaleAreaVo> querySaleAreaList(SaleAreaQueryPo saleAreaQueryPo);

    /**
     * 新增销售区域
     * @param saleAreaAddPo 销售区域新增请求参数
     */
    void addSaleArea(SaleAreaAddPo saleAreaAddPo);

    /**
     * 更新销售区域
     * @param saleAreaUpdatePo 销售区域更新请求参数
     */
    void updateSaleArea(SaleAreaUpdatePo saleAreaUpdatePo);

    /**
     * 删除销售区域
     * @param id 销售区域id
     */
    void deleteSaleArea(Long id);
}
