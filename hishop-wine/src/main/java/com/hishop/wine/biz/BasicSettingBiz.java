package com.hishop.wine.biz;

import com.hishop.setting.AbstractSetting;
import com.hishop.wine.enums.BasicSettingEnum;
import com.hishop.wine.model.po.setting.SaveSystemSettingPO;

/**
 * 公共设置表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-07-12
 */

public interface BasicSettingBiz {

    /**
     * 保存配置
     *
     * @param settingEnum  配置枚举
     * @param settingValue 配置值
     */
    void saveSetting(BasicSettingEnum settingEnum, String settingValue);

    /**
     * 读取配置
     *
     * @param settingEnum 配置枚举
     * @return 配置信息
     */
    <T extends AbstractSetting> T getSetting(BasicSettingEnum settingEnum);

    /**
     * 保存系统配置
     *
     * @param systemSettingPO 保存系统配置参数
     */
    void saveSystemSetting(SaveSystemSettingPO systemSettingPO);

}