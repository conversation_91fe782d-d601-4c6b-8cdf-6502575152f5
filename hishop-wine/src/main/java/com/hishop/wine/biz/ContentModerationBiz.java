package com.hishop.wine.biz;

import java.util.List;

import com.hishop.common.response.PageResult;
import com.hishop.moderation.support.ModerationConfig;
import com.hishop.wine.model.po.moderation.ContentModerationCreatePO;
import com.hishop.wine.model.vo.moderation.ContentModerationVO;
import com.hishop.wine.repository.entity.ContentModerationInfo;

/**
 * 内容审核表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-09-11
 */
public interface ContentModerationBiz {

    /**
     * 创建审核记录
     *
     * @param createPO 发起审核参数
     * @return 审核id
     */
    Long create(ContentModerationCreatePO createPO);

    /**
     * 发起内容审核
     */
    void launchModeration();

    /**
     * 发起审核
     *
     * @param config 审核配置
     * @param info   审核明细
     */
    void launchModeration(ModerationConfig config, ContentModerationInfo info);

    /**
     * 检查异步审核结果
     */
    void queryModerationResult();

    /**
     * 检查异步审核结果
     *
     * @param config 审核配置
     * @param info   审核明细
     */
    void queryModerationResult(ModerationConfig config, ContentModerationInfo info);
}