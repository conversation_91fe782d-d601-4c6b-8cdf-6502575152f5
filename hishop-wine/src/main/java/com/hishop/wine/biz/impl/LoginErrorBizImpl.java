package com.hishop.wine.biz.impl;

import cn.binarywang.wx.miniapp.config.WxMaConfig;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import com.hishop.wine.biz.LoginErrorBiz;
import com.hishop.wine.enums.bill.TransactionType;
import com.hishop.wine.enums.order.PayMethod;
import com.hishop.wine.model.po.LoginErrorPO;
import com.hishop.wine.model.po.LoginErrorQueryPO;
import com.hishop.wine.model.po.login.UserMiniMobileLoginPO;
import com.hishop.wine.model.vo.LoginErrorVo;
import com.hishop.wine.model.vo.bill.BillVo;
import com.hishop.wine.repository.dto.WxLoginDataDTO;
import com.hishop.wine.repository.entity.Bill;
import com.hishop.wine.repository.entity.LoginError;
import com.hishop.wine.repository.entity.MiniUser;
import com.hishop.wine.repository.entity.User;
import com.hishop.wine.repository.service.LoginErrorService;
import com.hishop.wine.repository.service.MiniUserService;
import com.hishop.wine.repository.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Service
public class LoginErrorBizImpl implements LoginErrorBiz {

    @Resource
    private LoginErrorService loginErrorService;

    @Resource
    private MiniUserService miniUserService;

    @Resource
    private UserService userService;


    @Override
    public PageResult<LoginErrorVo> pageList(LoginErrorQueryPO loginErrorQueryPo) {
        Page<LoginError> pageInfo = loginErrorService.queryPage(loginErrorQueryPo.buildPage(), loginErrorQueryPo);
        return PageResultHelper.transfer(pageInfo, LoginErrorVo.class, vo->{
            if(vo.getType() == 0) {
                vo.setTypeStr("微信号绑定其他手机");
            } else {
                vo.setTypeStr("手机号绑定其他微信");
            }
        });
    }

    @Override
    public void resetMinUser(Long id) {
        LoginError loginError = loginErrorService.getById(id);
        if(loginError == null) {
            throw new RuntimeException("登录异常信息不存在");
        }
        if(loginError.getType() == 0) {
            // 根据appId与userId删除miniUser
            miniUserService.remove(new LambdaQueryWrapper<MiniUser>().eq(MiniUser::getAppId, loginError.getAppId()).eq(MiniUser::getOpenId, loginError.getOpenId()));
            loginError.setStatus(Boolean.TRUE);
            loginErrorService.updateById(loginError);
        } else {
            // 根据mobile查询登录用户
            User user = userService.getOne(new LambdaQueryWrapper<User>().eq(User::getMobile, loginError.getMobile()).eq(User::getIzDelete, Boolean.FALSE));
            if(user == null) {
                throw new RuntimeException("手机号对应用户不存在");
            }
            // 再根据userId删除miniUser
            miniUserService.remove(new LambdaQueryWrapper<MiniUser>().eq(MiniUser::getUserId, user.getId()));
            loginError.setStatus(Boolean.TRUE);
            loginErrorService.updateById(loginError);
        }
    }


    @Override
    @Async
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void saveLoginErrorAsync(UserMiniMobileLoginPO loginPO, WxMaConfig wxMaConfig, WxLoginDataDTO wxLoginData, Integer type) {
        LoginError loginError = new LoginError();
        loginError.setMobile(loginPO.getMobile());
        loginError.setType(type);
        loginError.setAppId(wxMaConfig.getAppid());
        loginError.setOpenId(wxLoginData.getOpenId());
        loginError.setCreateBy(1L);
        loginError.setCreateTime(new Date());
        loginErrorService.save(loginError);
    }
}
