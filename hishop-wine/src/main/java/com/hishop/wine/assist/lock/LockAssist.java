package com.hishop.wine.assist.lock;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/8/2
 */
@Component
@Slf4j
public class LockAssist {

    @Resource
    private RedissonClient redissonClient;

    /**
     * 积分变更-加锁
     * <p>单个用户的积分操作</p>
     * <AUTHOR>
     * @date 2023/8/2
     */
    public boolean lockChangePoints(Long userId, long waitTime, long leaseTime, TimeUnit timeUnit) {
        log.info("变更积分，加锁，userId: {}", userId);
        String key = String.format(LockConst.KEY_POINTS_CHANGE, userId);
        try {
            return redissonClient.getLock(key).tryLock(waitTime, leaseTime, timeUnit);
        } catch (InterruptedException e) {
            log.error("变更积分，加锁异常：{}", userId, e);
        }
        return false;
    }

    /**
     * 积分变更-解锁
     * <p>单个用户的积分操作</p>
     * <AUTHOR>
     * @date 2023/8/2
     */
    public void unlockChangePoints(Long userId) {
        String key = String.format(LockConst.KEY_POINTS_CHANGE, userId);
        RLock lock = redissonClient.getLock(key);
        if (ObjectUtil.isNotNull(lock) && lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
        log.info("变更积分，解锁成功，drawRecordId: {}", userId);
    }


    //*****************************


    /**
     * 所有用户积分缓存-加锁
     * <AUTHOR>
     * @date 2023/8/2
     */
    public boolean lockInitAllUserPoints(long waitTime, long leaseTime, TimeUnit timeUnit) {
        log.info("缓存用户积分排行榜，加锁");
        try {
            return redissonClient.getLock(LockConst.KEY_CACHE_ALL_USER_RANK).tryLock(waitTime, leaseTime, timeUnit);
        } catch (InterruptedException e) {
            log.error("变更积分，加锁异常", e);
        }
        return false;
    }

    /**
     * 所有用户积分缓存-解锁
     * <AUTHOR>
     * @date 2023/8/2
     */
    public void unlockInitAllUserPoints() {
        RLock lock = redissonClient.getLock(LockConst.KEY_CACHE_ALL_USER_RANK);
        if (ObjectUtil.isNotNull(lock) && lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
        log.info("缓存用户积分排行榜，解锁成功");
    }

}
