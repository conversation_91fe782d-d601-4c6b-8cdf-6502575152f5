package com.hishop.wine.assist.points;

import cn.hutool.core.collection.CollUtil;
import com.hishop.wine.assist.lock.LockAssist;
import com.hishop.wine.constants.GlobalCacheConst;
import com.hishop.wine.repository.entity.MemberPoints;
import com.hishop.wine.repository.service.MemberPointsService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 积分缓存辅助类
 * 这种方式对于用户查看积分排行榜时，如果缓存异常或者不及时，会有一定的延迟，但应该不影响
 * <AUTHOR>
 * @date 2023/8/4
 */
@Component
@Slf4j
public class PointsCacheAssist {

    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private MemberPointsService memberPointsService;
    @Resource
    private LockAssist lockAssist;

    @Async
    public void initAllUser() {
        try {
            boolean locked = lockAssist.lockInitAllUserPoints(0, 5, TimeUnit.MINUTES);
            if (!locked) {
                return;
            }
            List<MemberPoints> allUserList = memberPointsService.listAll();
            if (CollUtil.isEmpty(allUserList)) {
                return;
            }
            Set<ZSetOperations.TypedTuple<String>> userSet = allUserList.parallelStream()
                    .map(p -> new DefaultTypedTuple<>(String.format(GlobalCacheConst.USER_POINTS_RANK_KEY, p.getUserId(), p.getIdentityType()), p.getTotalPoints().doubleValue()))
                    .collect(Collectors.toSet());
            redisTemplate.opsForZSet().add(GlobalCacheConst.USER_POINTS_RANK, userSet);
        } catch (Exception e) {
            log.error("初始化用户积分排行榜异常", e);
        } finally {
            lockAssist.unlockInitAllUserPoints();
        }
    }

    @Async
    public void cacheUserPoints(Long userId, Integer identityType) {
        try {
            MemberPoints userPoints = memberPointsService.getUserPoints(userId, identityType);
            redisTemplate.opsForZSet().add(GlobalCacheConst.USER_POINTS_RANK, String.format(GlobalCacheConst.USER_POINTS_RANK_KEY, userId, identityType), userPoints.getTotalPoints().doubleValue());
        } catch (Exception e) {
            log.error("缓存用户积分异常：{}", userId, e);
        }
    }

}
