package com.hishop.wine.assist.transaction.weixin;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.generator.SnowflakeGenerator;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.binarywang.wxpay.bean.notify.*;
import com.github.binarywang.wxpay.bean.request.WxPayRefundV3Request;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayRefundV3Result;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.bean.transfer.TransferBatchDetailResult;
import com.github.binarywang.wxpay.bean.transfer.TransferBatchesRequest;
import com.github.binarywang.wxpay.bean.transfer.TransferBatchesResult;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.github.binarywang.wxpay.service.WxPayService;
import com.hishop.common.exception.BusinessException;
import com.hishop.wine.assist.UserAssist;
import com.hishop.wine.assist.transaction.AbsTransactionHandler;
import com.hishop.wine.common.annotation.SwitchWxMini;
import com.hishop.wine.common.config.WxAutoMapping;
import com.hishop.wine.common.config.WxAutoMappingConfig;
import com.hishop.wine.enums.TransactionEnum;
import com.hishop.wine.model.po.transaction.TransactionEntPayPO;
import com.hishop.wine.model.po.transaction.TransactionPayPO;
import com.hishop.wine.model.po.transaction.TransactionRefundPO;
import com.hishop.wine.model.vo.transaction.TransactionEntPayVO;
import com.hishop.wine.model.vo.transaction.TransactionPayVO;
import com.hishop.wine.model.vo.transaction.TransactionRefundVO;
import com.hishop.wine.repository.entity.MiniUser;
import com.hishop.wine.repository.entity.Transaction;
import com.hishop.wine.repository.entity.TransactionNotify;
import com.hishop.wine.repository.service.MiniUserService;
import com.hishop.wine.repository.service.TransactionNotifyService;
import com.hishop.wine.repository.service.TransactionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 微信交易处理
 *
 * <AUTHOR>
 * @date : 2023/9/18
 */
@Slf4j
@Component
@SuppressWarnings("all")
public class WeixinTransactionHandler extends AbsTransactionHandler {

    @Resource
    private TransactionService transactionService;
    @Resource
    private TransactionNotifyService transactionNotifyService;
    @Resource
    private WxPayService wxPayService;
    @Resource
    private WxAutoMappingConfig wxAutoMappingConfig;
    @Resource
    private MiniUserService miniUserService;
    @Resource
    private HttpServletRequest httpServletRequest;
    @Resource
    private SnowflakeGenerator snowflakeGenerator;
    @Resource
    private UserAssist userAssist;

    @Value("${hishop.wx.notify.pay.url}")
    private String WX_PAY_NOTIFY_URL;
    @Value("${hishop.wx.notify.refund.url}")
    private String WX_REFUND_NOTIFY_URL;

    /**
     * 微信支付流水号前缀
     */
    private static final String PAY_NO_PREFIX = "WXP";
    /**
     * 微信退款流水号前缀
     */
    private static final String REFUND_NO_PREFIX = "WXR";
    /**
     * 微信打款流水号前缀
     */
    private static final String ENTRY_PAY_NO_PREFIX = "WXEP";

    /**
     * 发起微信支付
     *
     * @param payParam 支付参数
     * @return 支付返回值
     */
    @Override
    @SwitchWxMini(value = "#payParam.bizType.moduleCode", switchMini = false, switchPay = true)
    public TransactionPayVO pay(TransactionPayPO payParam) {
        try {
            log.info("【发起支付-微信支付】支付参数{}", payParam);
            // 根据模块编码查询对应的微信配置
            WxAutoMapping wxConfig = wxAutoMappingConfig.getWxAutoMapping(payParam.getBizType().getModuleCode());
            // 查询用户 openid
            String openId = getOpenId(payParam.getUserId(), wxConfig.getAppId());
            log.info("【发起支付-微信支付】支付配置:{}, 用户openId:{}", payParam.getUserId(), openId);

            // 创建交易id 交易表的主键 同时也是微信支付的商户订单号
            Long transactionId = snowflakeGenerator.next();
            String transactionNo = PAY_NO_PREFIX + transactionId;
            Transaction transaction = BeanUtil.copyProperties(payParam, Transaction.class);
            // 保存交易记录
            transaction.setId(transactionId);
            transaction.setModuleCode(payParam.getBizType().getModuleCode());
            transaction.setTransactionNo(transactionNo);
            transaction.setMchId(wxConfig.getMchId());
            transaction.setAppId(wxConfig.getAppId());
            transaction.setTransactionType(payParam.getBizType().getTransactionType().name());
            transaction.setOpenId(openId);
            transaction.setRefundStatus(TransactionEnum.RefundStatus.NOT_REFUND.getStatus());
            transactionService.save(transaction);
            log.info("【发起支付-微信支付】保存交易记录:{}", transaction);

            // 构建支付参数
            WxPayUnifiedOrderV3Request request = buildPayRequest(transaction);
            log.info("【发起支付-微信支付】调用微信支付请求参数{}", request);
            // 发起支付
            Object orderV3 = wxPayService.createOrderV3(TradeTypeEnum.JSAPI, request);
            Map<String, Object> orderMap = BeanUtil.beanToMap(orderV3);
            orderMap.put("package", orderMap.get("packageValue"));
            log.info("【发起支付-微信支付】调用微信支付返回值{}", orderV3);
            return TransactionPayVO.of(transactionId, transactionNo, orderMap);
        } catch (Exception e) {
            log.error("【发起支付-微信支付】发起失败 {}", e);
            throw new BusinessException("发起微信失败: " + e.getMessage());
        }
    }

    /**
     * 支付回调
     *
     * @param moduleCode 模块编码
     * @param notifyData 回调参数
     * @return 回调结果
     */
    @Override
    @SwitchWxMini(value = "#moduleCode", switchMini = false, switchPay = true)
    public String notifyPay(String moduleCode, String notifyData) {
        log.info("【支付回调-微信支付】接收到回调, 模块编码: {}, 回调信息: {}", moduleCode, notifyData);
        try {
            // 解密回调参数
            OriginNotifyResponse notifyResponse = JSONUtil.toBean(notifyData, OriginNotifyResponse.class);
            WxPayOrderNotifyV3Result wxPayOrderNotifyV3Result = wxPayService.parseOrderNotifyV3Result(jsonStrSort(notifyResponse), getNotifyPaySignatureHeader());
            WxPayOrderNotifyV3Result.DecryptNotifyResult result = wxPayOrderNotifyV3Result.getResult();
            log.info("【支付回调-微信支付】解密支付回调参数: {}", result);

            // 提取交易记录
            String transactionId = result.getOutTradeNo();
            Transaction transaction = transactionService.getById(transactionId);
            Assert.isTrue(ObjectUtil.isNotNull(transaction), "交易记录不存在");

            // 更新交易状态
            Transaction updTransaction = new Transaction();
            updTransaction.setId(transaction.getId());
            updTransaction.setModuleCode(moduleCode);
            updTransaction.setStatus(WxPayConstants.WxpayTradeStatus.SUCCESS.equals(result.getTradeState())
                    ? TransactionEnum.Status.SUCCESS.getStatus() : TransactionEnum.Status.FAIL.getStatus());
            updTransaction.setThirdStatus(result.getTradeState());
            updTransaction.setThirdTransactionNo(result.getTransactionId());
            updTransaction.setFinishTime(DateUtil.parse(result.getSuccessTime(), "yyyy-MM-dd'T'HH:mm:ss+08:00"));
            updTransaction.setBizType(transaction.getBizType());
            updTransaction.setBizCode(transaction.getBizCode());
            transactionService.updateById(updTransaction);
            log.info("【支付回调-微信支付】更新交易记录: {}", updTransaction);

            // 创建支付流水
            saveNotifyData(transaction.getId(), result);
            log.info("【支付回调-微信支付】保存支付流水成功: TransactionId: {}", updTransaction.getId());

            // 反向通知业务系统
            callBackBusinessSystem(updTransaction, "支付回调-微信支付");
            return WxPayNotifyV3Response.success("微信支付回调成功");
        } catch (Exception e) {
            log.error("【支付回调-微信支付】回调处理失败: {}", e);
            return WxPayNotifyV3Response.fail(e.getMessage());
        }
    }

    /**
     * 退款
     *
     * @param refundParam    退款参数
     * @param orgTransaction 原交易记录
     * @return 退款返回值
     */
    @Override
    @SwitchWxMini(value = "#orgTransaction.moduleCode", switchMini = false, switchPay = true)
    public TransactionRefundVO refund(TransactionRefundPO refundParam, Transaction orgTransaction) {
        log.info("【发起退款-微信支付】退款参数: {}, 原交易记录: {}", refundParam, orgTransaction);

        // 添加新的交易流水
        Long transactionId = snowflakeGenerator.next();
        String transactionNo = REFUND_NO_PREFIX + transactionId;
        Transaction refundTransaction = new Transaction();
        refundTransaction.setId(transactionId);
        refundTransaction.setTransactionNo(transactionNo);
        refundTransaction.setTransactionMethod(orgTransaction.getTransactionMethod());
        refundTransaction.setModuleCode(orgTransaction.getModuleCode());
        refundTransaction.setMchId(orgTransaction.getMchId());
        refundTransaction.setAppId(orgTransaction.getAppId());
        refundTransaction.setUserId(orgTransaction.getUserId());
        refundTransaction.setOpenId(orgTransaction.getOpenId());
        refundTransaction.setBizType(refundParam.getBizType().getType());
        refundTransaction.setBizCode(refundParam.getBizCode());
        refundTransaction.setTransactionType(refundParam.getBizType().getTransactionType().name());
        refundTransaction.setAmount(refundParam.getAmount());
        refundTransaction.setOrgTransactionId(orgTransaction.getId());
        transactionService.save(refundTransaction);
        log.info("【发起退款-微信支付】保存退款交易流水: {}", refundTransaction);

        // 重新计算原流水的退款信息
        transactionService.resetRefundData(orgTransaction.getId());
        log.info("【发起退款-微信支付】重新计算原流水的退款信息, TransactionId: {}", orgTransaction.getId());
        // 发起微信退款
        try {
            WxPayRefundV3Request request = buildRefundRequest(refundTransaction, orgTransaction);
            log.info("【发起退款-微信支付】请求参数: {}", request);
            WxPayRefundV3Result wxPayRefundV3Result = wxPayService.refundV3(request);
            log.info("【发起退款】发起退款调用成功, 等待回调, 响应参数: {}", wxPayRefundV3Result);
            return TransactionRefundVO.of(refundTransaction.getId(), refundTransaction.getTransactionNo());
        } catch (Exception e) {
            log.info("【发起退款】发起退款失败: 参数 {}, 异常: {}", refundParam, e);
            throw new BusinessException("微信退款失败: " + e.getMessage());
        }
    }

    /**
     * 退款回调
     *
     * @param moduleCode 模块编码
     * @param notifyData 回调参数
     * @return 回调结果
     */
    @Override
    @SwitchWxMini(value = "#moduleCode", switchMini = false, switchPay = true)
    public String notifyRefund(String moduleCode, String notifyData) {
        log.info("【退款回调-微信支付】模块编码:{}, 回调参数:{}", moduleCode, notifyData);
        try {
            // 解密回调参数
            OriginNotifyResponse notifyResponse = JSONUtil.toBean(notifyData, OriginNotifyResponse.class);
            WxPayRefundNotifyV3Result wxPayRefundNotifyV3Result = wxPayService.parseRefundNotifyV3Result(jsonStrSort(notifyResponse), getNotifyPaySignatureHeader());
            WxPayRefundNotifyV3Result.DecryptNotifyResult result = wxPayRefundNotifyV3Result.getResult();
            log.info("【退款回调-微信支付】解密退款信息: {}", result);

            // 提取交易记录
            String transactionId = result.getOutRefundNo();
            Transaction transaction = transactionService.getById(transactionId);
            Assert.isTrue(ObjectUtil.isNotNull(transaction), "交易记录不存在");

            // 更新交易状态
            Transaction updTransaction = new Transaction();
            updTransaction.setId(transaction.getId());
            updTransaction.setModuleCode(moduleCode);
            updTransaction.setStatus(WxPayConstants.RefundStatus.SUCCESS.equals(result.getRefundStatus())
                    ? TransactionEnum.Status.SUCCESS.getStatus() : TransactionEnum.Status.FAIL.getStatus());
            updTransaction.setThirdStatus(result.getRefundStatus());
            updTransaction.setThirdTransactionNo(result.getRefundId());
            updTransaction.setFinishTime(DateUtil.parse(result.getSuccessTime(), "yyyy-MM-dd'T'HH:mm:ss+08:00"));
            updTransaction.setBizType(transaction.getBizType());
            updTransaction.setBizCode(transaction.getBizCode());
            transactionService.updateById(updTransaction);
            log.info("【退款回调-微信支付】更新交易状态成功: {}", updTransaction);

            // 创建支付流水
            saveNotifyData(transaction.getId(), result);
            log.info("【退款回调-微信支付】保存回调记录, TransactionId:", updTransaction.getId());

            // 如果是退款失败的状态, 则重新计算一次退款信息
            if (!WxPayConstants.RefundStatus.SUCCESS.equals(result.getRefundStatus())) {
                transactionService.resetRefundData(transaction.getOrgTransactionId());
            }

            // 回调业务系统
            callBackBusinessSystem(updTransaction, "退款回调-微信支付");
            return WxPayNotifyV3Response.success("回调成功");
        } catch (Exception e) {
            log.error("【退款回调-微信支付】回调处理失败: {}", e);
            return WxPayNotifyV3Response.fail(e.getMessage());
        }
    }

    /**
     * 发起企业打款
     *
     * @param entPayParam 企业打款参数
     * @return 企业打款返回值
     */
    @Override
    @SwitchWxMini(value = "#entPayParam.bizType.moduleCode", switchMini = false, switchPay = true)
    public TransactionEntPayVO entPay(TransactionEntPayPO entPayParam) {
        log.info("【企业打款-微信支付】发起参数:{}", entPayParam);
        try {
            // 根据模块编码查询对应的微信配置
            WxAutoMapping wxConfig = wxAutoMappingConfig.getWxAutoMapping(entPayParam.getBizType().getModuleCode());

            // 查询用户 openid
            MiniUser miniUser = miniUserService.getOne(new LambdaQueryWrapper<MiniUser>()
                    .eq(MiniUser::getUserId, entPayParam.getUserId()).eq(MiniUser::getAppId, wxConfig.getAppId()));
            Assert.isTrue(ObjectUtil.isNotNull(miniUser), "用户不存在");

            // 创建交易id 交易表的主键 同时也是微信支付的商户订单号
            Long transactionId = snowflakeGenerator.next();
            String transactionNo = ENTRY_PAY_NO_PREFIX + transactionId;
            Transaction transaction = BeanUtil.copyProperties(entPayParam, Transaction.class);
            // 保存交易记录
            transaction.setId(transactionId);
            transaction.setModuleCode(entPayParam.getBizType().getModuleCode());
            transaction.setTransactionNo(transactionNo);
            transaction.setMchId(wxConfig.getMchId());
            transaction.setAppId(wxConfig.getAppId());
            transaction.setTransactionType(entPayParam.getBizType().getTransactionType().name());
            transaction.setOpenId(miniUser.getOpenId());
            transaction.setDescription(entPayParam.getDescription());
            transactionService.save(transaction);

            TransferBatchesRequest request = buildTransferRequest(transaction);
            log.info("【企业打款】请求参数: {}", request);
            TransferBatchesResult transferBatchesResult = wxPayService.getTransferService().transferBatches(request);
            log.info("【企业打款】返回值: {}", transferBatchesResult);
            return TransactionEntPayVO.of(transactionId, transactionNo);
        } catch (Exception e) {
            log.error("【企业打款】失败: {}", e);
            throw new BusinessException("【企业打款】失败: " + e.getMessage());
        }
    }

    /**
     * 查询企业付款到零钱结果
     *
     * @param transaction 交易流水
     */
    @Override
    @SwitchWxMini(value = "#transaction.moduleCode", switchMini = false, switchPay = true)
    public void checkEntPayResult(Transaction transaction) {
        log.info("【企业打款-结果查询】查询交易记录:{}", transaction);
        try {
            String rid = String.valueOf(transaction.getId());
            TransferBatchDetailResult result = wxPayService.getTransferService().transferBatchesOutBatchNoDetail(rid, rid);
            log.info("【企业打款-结果查询】返回值:{}", result);

            // 交易成功
            if (WeixinTransactionEnum.EntryPayStatus.SUCCESS.name().equals(result.getDetailStatus())) {
                transaction.setStatus(TransactionEnum.Status.SUCCESS.getStatus());
            }

            // 交易失败
            if (WeixinTransactionEnum.EntryPayStatus.FAIL.name().equals(result.getDetailStatus())) {
                transaction.setStatus(TransactionEnum.Status.FAIL.getStatus());
                transaction.setFailReason(result.getFailReason());
            }

            transaction.setThirdStatus(result.getDetailStatus());
            transaction.setUpdateTime(DateUtil.parse(result.getUpdateTime(), "yyyy-MM-dd'T'HH:mm:ss+08:00"));
            transactionService.updateById(transaction);
            log.info("【企业打款-结果查询】更新交易状态成功: {}", transaction);

            // 回调业务系统
            callBackBusinessSystem(transaction, "企业打款-结果查询");
        } catch (Exception e) {
            log.error("【企业打款-结果查询】失败, 交易记录:{}, 错误信息: {}", transaction, e);
        }
    }

    @Override
    public TransactionEnum.MethodEnum getMethod() {
        return TransactionEnum.MethodEnum.WX_PAY;
    }

    /**
     * 构建支付参数
     *
     * @param transaction 交易流水
     * @return 支付参数
     */
    private WxPayUnifiedOrderV3Request buildPayRequest(Transaction transaction) {
        // 创建支付参数
        WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();
        request.setAppid(transaction.getAppId());
        request.setMchid(transaction.getMchId());
        request.setOutTradeNo(String.valueOf(transaction.getId()));
        request.setDescription(transaction.getDescription());
        // 设置支付回调 回调需要追加模块编码 不然不知道回调的是哪个商户号的支付
        request.setNotifyUrl(WX_PAY_NOTIFY_URL + "/" + transaction.getModuleCode());

        // 设置支付金额
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        amount.setTotal(transaction.getAmount().multiply(new BigDecimal(100)).intValue());
        request.setAmount(amount);

        // 设置支付人
        WxPayUnifiedOrderV3Request.Payer payer = new WxPayUnifiedOrderV3Request.Payer();
        payer.setOpenid(transaction.getOpenId());
        request.setPayer(payer);
        return request;
    }

    /**
     * 构建退款请求
     *
     * @param refundTransaction 退款交易流水
     * @param orgTransaction    原交易流水
     * @return 退款请求
     */
    private WxPayRefundV3Request buildRefundRequest(Transaction refundTransaction, Transaction orgTransaction) {
        // 根据模块编码查询对应的微信配置
        WxAutoMapping wxConfig = wxAutoMappingConfig.getWxAutoMapping(refundTransaction.getModuleCode());
        wxPayService.switchover(wxConfig.getMchId());

        WxPayRefundV3Request request = new WxPayRefundV3Request();
        request.setOutTradeNo(String.valueOf(orgTransaction.getId()));
        request.setOutRefundNo(String.valueOf(refundTransaction.getId()));

        // 设置支付金额
        WxPayRefundV3Request.Amount amount = new WxPayRefundV3Request.Amount();
        amount.setRefund(refundTransaction.getAmount().multiply(new BigDecimal(100)).intValue());
        amount.setTotal(orgTransaction.getAmount().multiply(new BigDecimal(100)).intValue());
        amount.setCurrency("CNY");
        request.setAmount(amount);

        request.setNotifyUrl(WX_REFUND_NOTIFY_URL + "/" + refundTransaction.getModuleCode());
        return request;
    }

    /**
     * 构建转账请求参数
     *
     * @param transferTransaction 转账交易流水
     * @return 转账请求
     */
    private TransferBatchesRequest buildTransferRequest(Transaction transferTransaction) {
        TransferBatchesRequest request = new TransferBatchesRequest();
        request.setAppid(transferTransaction.getAppId());
        request.setOutBatchNo(String.valueOf(transferTransaction.getId()));
        request.setBatchName(transferTransaction.getDescription());
        request.setBatchRemark(transferTransaction.getDescription());
        request.setTotalAmount(transferTransaction.getAmount().multiply(new BigDecimal(100)).intValue());
        request.setTotalNum(1);

        TransferBatchesRequest.TransferDetail transferDetail = new TransferBatchesRequest.TransferDetail();
        transferDetail.setOutDetailNo(String.valueOf(transferTransaction.getId()));
        transferDetail.setTransferAmount(transferTransaction.getAmount().multiply(new BigDecimal(100)).intValue());
        transferDetail.setTransferRemark(transferTransaction.getDescription());
        transferDetail.setOpenid(transferTransaction.getOpenId());
        transferDetail.setTransferRemark(transferTransaction.getDescription());
        request.setTransferDetailList(Collections.singletonList(transferDetail));
        return request;
    }

    /**
     * 获取用户 openid
     *
     * @param userId 用户id
     * @param appId  应用id
     * @return openid
     */
    private String getOpenId(Long userId, String appId) {
        MiniUser miniUser = userAssist.checkAndGetMiniUser(userId, appId);
        return miniUser.getOpenId();
    }


    /**
     * 提取回调请求头
     *
     * @return 回调请求头
     */
    private SignatureHeader getNotifyPaySignatureHeader() {
        SignatureHeader signatureHeader = new SignatureHeader();
        signatureHeader.setTimeStamp(httpServletRequest.getHeader("wechatpay-timestamp"));
        signatureHeader.setSignature(httpServletRequest.getHeader("wechatpay-signature"));
        signatureHeader.setNonce(httpServletRequest.getHeader("wechatpay-nonce"));
        signatureHeader.setSerial(httpServletRequest.getHeader("wechatpay-serial"));
        return signatureHeader;
    }

    /**
     * 请求报文：按官方接口示例键值 --- 排序(必须)
     * 官方文档：https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_1_5.shtml，
     * https://pay.weixin.qq.com/wiki/doc/apiv3/wechatpay/wechatpay4_1.shtml
     * 《微信支付API v3签名验证》 中注意：应答主体（response Body），需要按照接口返回的顺序进行验签，错误的顺序将导致验签失败。
     *
     * @param originNotifyResponse OriginNotifyResponse
     * @return String
     */
    private String jsonStrSort(OriginNotifyResponse originNotifyResponse) {
        Map<String, Object> jsonSort = new LinkedHashMap<>();
        jsonSort.put("id", originNotifyResponse.getId());
        jsonSort.put("create_time", originNotifyResponse.getCreateTime());
        jsonSort.put("resource_type", originNotifyResponse.getResourceType());
        jsonSort.put("event_type", originNotifyResponse.getEventType());
        jsonSort.put("summary", originNotifyResponse.getSummary());
        Map<String, Object> resource = new LinkedHashMap();
        resource.put("original_type", originNotifyResponse.getResource().getOriginalType());
        resource.put("algorithm", originNotifyResponse.getResource().getAlgorithm());
        resource.put("ciphertext", originNotifyResponse.getResource().getCiphertext());
        resource.put("associated_data", originNotifyResponse.getResource().getAssociatedData());
        resource.put("nonce", originNotifyResponse.getResource().getNonce());
        jsonSort.put("resource", resource);
        return JSONUtil.toJsonStr(jsonSort);
    }

    /**
     * 保存回调数据
     *
     * @param transactionId 流水id
     * @param result        回调结果
     */
    private void saveNotifyData(Long transactionId, Object result) {
        TransactionNotify notify = new TransactionNotify();
        notify.setTransactionId(transactionId);
        notify.setNotifyJson(JSONObject.toJSONString(result));
        transactionNotifyService.save(notify);
    }
}
