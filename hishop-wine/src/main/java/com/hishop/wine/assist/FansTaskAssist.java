package com.hishop.wine.assist;

import com.hishop.mq.api.MQ;
import com.hishop.wine.constants.RocketMqConstants;
import com.hishop.wine.model.po.task.FansClubTaskPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date : 2023/8/3
 */
@Component
@Slf4j
public class FansTaskAssist {

    @Resource
    private MQ mq;

    /**
     * 完成任务检测
     *
     * @param userId      用户id
     * @param subTaskType 子任务类型 {@link com.hishop.wine.constants.FansClubTaskConstant}
     */
    public void complete(Long userId, String subTaskType) {
        try {
            mq.publish(RocketMqConstants.COMPLETE_FANS_TASK_TOPIC, FansClubTaskPO.of(userId, subTaskType));
        } catch (Exception e) {
            log.info("======> 推送完成任务检测消息失败，userId：{}，subTaskType：{}, 错误消息: {}", userId, subTaskType, e);
        }
    }

}
