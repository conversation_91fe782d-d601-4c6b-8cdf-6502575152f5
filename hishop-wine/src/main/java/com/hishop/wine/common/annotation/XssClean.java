package com.hishop.wine.common.annotation;

import com.hishop.wine.common.validator.XssValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * XSS 清理注解
 * 用于标记需要进行 XSS 防护的字段
 *
 * @author: System
 * @date: 2025-01-05
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = XssValidator.class)
public @interface XssClean {

    /**
     * 清理模式
     */
    enum Mode {
        /**
         * 富文本模式 - 保留安全的 HTML 标签
         */
        RICH_TEXT,
        
        /**
         * 纯文本模式 - 移除所有 HTML 标签
         */
        TEXT_ONLY,
        
        /**
         * JSON 模式 - 清理 JSON 内容中的 XSS
         */
        JSON,
        
        /**
         * Base64 模式 - 清理 Base64 编码的内容
         */
        BASE64
    }

    /**
     * 清理模式，默认为富文本模式
     */
    Mode mode() default Mode.RICH_TEXT;

    /**
     * 是否允许为空
     */
    boolean allowEmpty() default true;

    /**
     * 验证失败时的错误消息
     */
    String message() default "内容包含不安全的脚本代码";

    /**
     * 验证分组
     */
    Class<?>[] groups() default {};

    /**
     * 负载
     */
    Class<? extends Payload>[] payload() default {};
}
