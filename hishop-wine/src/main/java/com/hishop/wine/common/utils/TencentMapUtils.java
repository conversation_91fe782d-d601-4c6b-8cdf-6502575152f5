package com.hishop.wine.common.utils;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.hishop.common.response.tencent.TencentDistrictBean;
import com.hishop.common.response.tencent.TencentResponse;
import com.hishop.wine.biz.BasicSettingBiz;
import com.hishop.wine.enums.BasicSettingEnum;
import com.hishop.wine.model.vo.map.TecentAddressDetailDTO;
import com.hishop.wine.model.vo.setting.TencentMapSettingVO;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 腾讯地图工具类
 *
 * <AUTHOR>
 * @date : 2023/7/14
 */
@Slf4j
public class TencentMapUtils {

    private final static String BASE_URL = "https://apis.map.qq.com";
    private final static String KEY;

    static {
        TencentMapSettingVO setting = SpringUtil.getBean(BasicSettingBiz.class).getSetting(BasicSettingEnum.TENCENT_MAP_SETTING);
        KEY = setting.getKey();
    }

    /**
     * 获取所有省市区(前三级), 分别对应二维数组的三个下标
     *
     * @return 省市区前三级
     */
    public static TencentResponse<List<List<TencentDistrictBean>>> getProvinceAreaCity() {
        String requestUrl = String.format("%s/ws/district/v1/list?key=%s", BASE_URL, KEY);

        log.info("======> 获取地区列表: {}", requestUrl);
        String resultStr = HttpUtil.get(requestUrl);
        return dealDistrictResult(resultStr);
    }


    /**
     * 获取子集省市区
     *
     * @param id 父级id
     * @return 子集省市区
     */
    public static List<TencentDistrictBean> getChildrenDistrict(Integer id) {
        id = ObjectUtil.defaultIfNull(id, 0);
        String requestUrl = String.format("%s/ws/district/v1/getchildren?key=%s%s", BASE_URL, KEY, id == 0 ? StrUtil.EMPTY : "&id=" + id);

        log.info("======> 获取下级地区列表: {}", requestUrl);
        String resultStr = HttpUtil.get(requestUrl);
        return dealDistrictResult(resultStr).getResult().get(0);
    }

    /**
     * @param lat 纬度
     * @param lng 经度
     * <AUTHOR>
     * @date 2023/7/20
     */
    public static TecentAddressDetailDTO getAddressByLocation(BigDecimal lat, BigDecimal lng) {
        String requestUrl = String.format("%s/ws/geocoder/v1/?key=%s&location=%s", BASE_URL, KEY, lat + "," + lng);
        log.info("======> 根据经纬度获取位置: {}", requestUrl);
        String resultStr = HttpUtil.get(requestUrl);
        return resolveToOne(resultStr, TecentAddressDetailDTO.class);
    }

    /**
     * 处理腾讯返回的地区数据
     *
     * @param result 第三方服务返回值
     * @return 系统内部地区信息
     */
    private static TencentResponse<List<List<TencentDistrictBean>>> dealDistrictResult(String result) {
        TencentResponse<List<List<TencentDistrictBean>>> responseVO = JSON.parseObject(result, TencentResponse.class);
        Assert.isTrue(responseVO.getStatus() == 0, String.format("获取地区列表失败: %s", responseVO.getMessage()));

        List<List<TencentDistrictBean>> districts = new ArrayList<>();
        JSONArray tencentDistrictArr = JSONArray.parseArray(JSON.toJSONString(responseVO.getResult()));
        tencentDistrictArr.stream().forEach(subArr -> {
            districts.add(JSON.parseArray(JSON.toJSONString(subArr), TencentDistrictBean.class));
        });
        responseVO.setResult(districts);
        return responseVO;
    }


    private static <T> T resolveToOne(String result, Class<T> clazz) {
        TencentResponse<String> responseVO = JSON.parseObject(result, new TypeReference<TencentResponse<String>>(){});
        return JSONObject.parseObject(responseVO.getResult(), clazz);
    }

    private static <T> List<T> resolveToList(String result, Class<T> clazz) {
        TencentResponse<String> responseVO = JSON.parseObject(result, new TypeReference<TencentResponse<String>>(){});
        return JSONArray.parseArray(responseVO.getResult(), clazz);
    }

}
