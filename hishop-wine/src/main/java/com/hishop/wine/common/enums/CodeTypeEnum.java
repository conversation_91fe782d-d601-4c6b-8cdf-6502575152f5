package com.hishop.wine.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/07/08/ $
 * @description: 物流码类型
 */
@Getter
public enum CodeTypeEnum {

    /**
     * 盒码
     */
    BOX_CODE(0, "盒码"),
    /**
     * 箱码
     */
    CASE_CODE(1, "箱码");

    private final Integer type;

    private final String desc;

    CodeTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static CodeTypeEnum getCodeTypeEnum(Integer type) {
        for (CodeTypeEnum codeTypeEnum : CodeTypeEnum.values()) {
            if (codeTypeEnum.getType().equals(type)) {
                return codeTypeEnum;
            }
        }
        return null;
    }

    public static CodeTypeEnum getCodeTypeEnumByDesc(String desc) {
        for (CodeTypeEnum codeTypeEnum : CodeTypeEnum.values()) {
            if (codeTypeEnum.getDesc().equals(desc)) {
                return codeTypeEnum;
            }
        }
        return null;
    }

}
