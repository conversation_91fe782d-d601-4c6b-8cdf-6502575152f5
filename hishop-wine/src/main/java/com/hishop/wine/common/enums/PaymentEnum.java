package com.hishop.wine.common.enums;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 支付设置枚举
 *
 * <AUTHOR>
 * @date : 2023/7/18
 */
public class PaymentEnum {

    /**
     * 支付类型
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    public enum Type {

        /**
         * 微信支付
         */
        WX_PAY("WX_PAY", "微信支付","ONLINE"),

//        /**
//         * 支付宝支付
//         */
//        ALI_PAY("ALI_PAY", "支付宝支付"),
        /**
         * 线下支付
         */
        OFFLINE("OFFLINE", "线下支付","OFFLINE");

        /**
         * 支付类型
         */
        @Getter
        private final String type;

        /**
         * 支付类型名称
         */
        @Getter
        private final String name;

        /**
         * 支付类型分组
         */
        @Getter
        private final String group;

        Type(String type, String name,String group) {
            this.type = type;
            this.name = name;
            this.group = group;
        }

        public static Type getEnumByType(String type) {
            return Arrays.stream(Type.values()).filter(item -> item.getType().equals(type)).findFirst().orElse(null);
        }

        public static String getNameByType(String type) {
            return Arrays.stream(Type.values()).filter(item -> item.getType().equals(type)).map(Type::getName).findFirst().orElse(StrUtil.EMPTY);
        }

        public static List<Type> getEnumByGroup(String group) {
            return Arrays.stream(Type.values()).filter(item -> item.getGroup().equals(group)).collect(Collectors.toList());
        }
    }
}
