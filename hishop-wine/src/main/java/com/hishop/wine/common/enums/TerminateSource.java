package com.hishop.wine.common.enums;

import lombok.Getter;

/**
 * @description: 门店数据来源类型
 * @author: chenzw
 * @date: 2024/7/6 15:37
 */
@Getter
public enum TerminateSource {

    /**
     * 管理后台新增
     */
    ADD(1, "管理后台新增"),

    /**
     * 管理后台导入
     */
    IMPORT(2, "管理后台导入"),

    /**
     * 小程序邀请注册
     */
    APP_REGISTER(3, "小程序邀请注册"),
    ;

    private final Integer type;

    private final String desc;

    TerminateSource(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static TerminateSource getTerminateSourceEnum(Integer type) {
        for (TerminateSource terminateSource : TerminateSource.values()) {
            if (terminateSource.getType().equals(type)) {
                return terminateSource;
            }
        }
        return null;
    }
}
