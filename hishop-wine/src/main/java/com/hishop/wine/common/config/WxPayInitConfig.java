package com.hishop.wine.common.config;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import com.hishop.setting.AbsRefreshSetting;
import com.hishop.weixin.pay.ext.CustomWxPayConfig;
import com.hishop.wine.common.enums.PaymentEnum;
import com.hishop.wine.constants.SettingConstants;
import com.hishop.wine.repository.dto.payment.PaymentSettingWxDTO;
import com.hishop.wine.repository.entity.PaymentCertificate;
import com.hishop.wine.repository.entity.PaymentSetting;
import com.hishop.wine.repository.service.PaymentCertificateService;
import com.hishop.wine.repository.service.PaymentSettingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 微信支付配置
 *
 * <AUTHOR>
 * @date : 2023/6/28
 */
@Configuration
@Slf4j
public class WxPayInitConfig extends AbsRefreshSetting {

    @Resource
    private PaymentSettingService paymentSettingService;
    @Resource
    private PaymentCertificateService paymentCertificateService;

    @Bean
    public WxPayService wxPayService() {
        log.info("【初始化微信支付配置】start");
        WxPayService payService = new WxPayServiceImpl();
        Map<String, WxPayConfig> wxConfigs = getWxConfigs();

        if (CollectionUtils.isEmpty(wxConfigs)) {
            log.info("【初始化微信支付配置】fail, 未读取到微信支付配置");
            return payService;
        }

        payService.setMultiConfig(wxConfigs);
        log.info("【初始化微信支付配置】success");
        return payService;
    }

    @Override
    public void refresh() {
        log.info("【更新微信支付配置】start");
        WxPayService payService = SpringUtil.getBean(WxPayService.class);
        Map<String, WxPayConfig> wxConfigs = getWxConfigs();

        if (!CollectionUtils.isEmpty(wxConfigs)) {
            payService.setMultiConfig(wxConfigs);
        }
        log.info("【更新微信支付配置】success");
    }

    @Override
    public String getSettingName() {
        return SettingConstants.WX_PAY_SETTING;
    }

    /**
     * 组装微信支付配置信息
     *
     * @return 微信支付配置信息
     */
    private Map<String, WxPayConfig> getWxConfigs() {
        List<PaymentSetting> paymentSettingList = paymentSettingService.list(
                new LambdaQueryWrapper<PaymentSetting>().eq(PaymentSetting::getPaymentType, PaymentEnum.Type.WX_PAY.getType()));
        return paymentSettingList.stream()
                .map(payment -> {
                    PaymentSettingWxDTO setting = JSONObject.parseObject(payment.getSettingValue(), PaymentSettingWxDTO.class);
                    // 读取证书流
                    PaymentCertificate cert = paymentCertificateService.getById(setting.getCertificateId());

                    CustomWxPayConfig config = new CustomWxPayConfig();
                    config.setMchId(setting.getMchId());
                    config.setApiV3Key(setting.getApiV3Key());
                    config.setKeyContent(cert.getContent());
                    return config;
                }).collect(Collectors.toMap(WxPayConfig::getMchId, a -> a, (o, n) -> o));
    }
}
