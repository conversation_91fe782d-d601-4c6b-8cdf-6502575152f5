package com.hishop.wine.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;

/**
 * XSS 防护配置类
 * 用于配置 XSS 防护的全局参数
 *
 * @author: System
 * @date: 2025-01-05
 */
@Slf4j
@Configuration
@ConfigurationProperties(prefix = "hishop.xss")
public class XssConfig {

    /**
     * 是否启用 XSS 防护
     */
    private boolean enabled = true;

    /**
     * 是否记录 XSS 清理日志
     */
    private boolean logEnabled = true;

    /**
     * 需要跳过 XSS 检查的 URL 路径
     */
    private List<String> excludeUrls = Arrays.asList(
            "/actuator/**",
            "/swagger-ui/**",
            "/v2/api-docs",
            "/doc.html"
    );

    /**
     * 需要严格检查的字段名称模式
     */
    private List<String> strictFields = Arrays.asList(
            "settingJson",
            "settingValue",
            "userAgreement",
            "privacyPolicy",
            "content",
            "description"
    );

    /**
     * 允许的 HTML 标签（用于富文本）
     */
    private List<String> allowedHtmlTags = Arrays.asList(
            "p", "br", "div", "span", "h1", "h2", "h3", "h4", "h5", "h6",
            "b", "i", "u", "strong", "em", "mark", "small", "del", "ins", "sub", "sup",
            "ul", "ol", "li", "a", "img", "table", "thead", "tbody", "tr", "td", "th"
    );

    /**
     * 允许的 HTML 属性
     */
    private List<String> allowedHtmlAttributes = Arrays.asList(
            "href", "title", "src", "alt", "width", "height", "style", "class", "id"
    );

    @PostConstruct
    public void init() {
        log.info("XSS 防护配置初始化完成 - 启用状态: {}, 日志记录: {}", enabled, logEnabled);
        log.info("排除路径: {}", excludeUrls);
        log.info("严格检查字段: {}", strictFields);
    }

    // Getter 和 Setter 方法

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isLogEnabled() {
        return logEnabled;
    }

    public void setLogEnabled(boolean logEnabled) {
        this.logEnabled = logEnabled;
    }

    public List<String> getExcludeUrls() {
        return excludeUrls;
    }

    public void setExcludeUrls(List<String> excludeUrls) {
        this.excludeUrls = excludeUrls;
    }

    public List<String> getStrictFields() {
        return strictFields;
    }

    public void setStrictFields(List<String> strictFields) {
        this.strictFields = strictFields;
    }

    public List<String> getAllowedHtmlTags() {
        return allowedHtmlTags;
    }

    public void setAllowedHtmlTags(List<String> allowedHtmlTags) {
        this.allowedHtmlTags = allowedHtmlTags;
    }

    public List<String> getAllowedHtmlAttributes() {
        return allowedHtmlAttributes;
    }

    public void setAllowedHtmlAttributes(List<String> allowedHtmlAttributes) {
        this.allowedHtmlAttributes = allowedHtmlAttributes;
    }

    /**
     * 检查字段是否需要严格验证
     *
     * @param fieldName 字段名称
     * @return 是否需要严格验证
     */
    public boolean isStrictField(String fieldName) {
        if (fieldName == null) {
            return false;
        }
        return strictFields.stream().anyMatch(pattern -> 
            fieldName.toLowerCase().contains(pattern.toLowerCase()));
    }

    /**
     * 检查 URL 是否需要跳过 XSS 检查
     *
     * @param url 请求 URL
     * @return 是否跳过检查
     */
    public boolean shouldExcludeUrl(String url) {
        if (url == null) {
            return false;
        }
        return excludeUrls.stream().anyMatch(pattern -> {
            if (pattern.endsWith("/**")) {
                String prefix = pattern.substring(0, pattern.length() - 3);
                return url.startsWith(prefix);
            }
            return url.equals(pattern);
        });
    }
}
