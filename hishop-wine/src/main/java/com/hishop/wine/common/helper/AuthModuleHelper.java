package com.hishop.wine.common.helper;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hishop.common.constants.SystemConstants;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.util.RedisUtil;
import com.hishop.wine.constants.BasicRedisConstants;
import com.hishop.wine.model.vo.login.AuthModuleVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

/**
 * 授权模块帮助类
 * <AUTHOR>
 * @date 2023/7/14
 */
@Component
public class AuthModuleHelper {

    /**
     * 授权平台域名配置
     */
    @Value("${hishop.auth.domain}")
    private String authDomain;

    //未授权
    private static final String NO_AUTH_SYSTEM = "-1";

    private static final String AUTH_MODULE_STATE = "state";

    public AuthModuleVO getAuthModule() {
        AuthModuleVO authModuleVO = new AuthModuleVO();
        //只添加封藏项目
        authModuleVO.setFengtanWine("1");
        return authModuleVO;
    }


}
