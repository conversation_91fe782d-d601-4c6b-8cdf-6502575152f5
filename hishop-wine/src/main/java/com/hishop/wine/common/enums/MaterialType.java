package com.hishop.wine.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * @description: 资源库类型
 * @author: tomliu
 * @create: 2022/06/23 10:42
 **/
@RequiredArgsConstructor
public enum MaterialType {

    None(0),
    /**
     * 图片
     */
    IMAGE(1),

    /**
     * 视频
     */
    VIDEO(2),

    /**
     * 音频
     */
    AUDIO(3);

    /**
     * 值
     */
    @Getter
    @EnumValue
    private final Integer value;

    public static MaterialType getByValue(Integer value) {
        for (MaterialType type : MaterialType.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
}
