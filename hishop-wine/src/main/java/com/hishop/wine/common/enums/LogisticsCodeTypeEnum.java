package com.hishop.wine.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/07/09/ $
 * @description:
 */
@Getter
public enum LogisticsCodeTypeEnum {

    /**
     * 盒码
     */
    BOX_CODE(0, "盒码"),
    /**
     * 箱码
     */
    CASE_SCAN(1, "箱码")
    ;

    private final Integer value;

    private final String desc;

    LogisticsCodeTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static LogisticsCodeTypeEnum getLogisticsCodeTypeEnum(Integer value) {
        for (LogisticsCodeTypeEnum logisticsCodeTypeEnum : LogisticsCodeTypeEnum.values()) {
            if (logisticsCodeTypeEnum.getValue().equals(value)) {
                return logisticsCodeTypeEnum;
            }
        }
        return null;
    }
}
