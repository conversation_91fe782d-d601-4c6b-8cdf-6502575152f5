package com.hishop.wine.common.enums;

/**
 * <AUTHOR>
 * @date : 2023/7/24
 */
public enum AgreementEnum {

    /**
     * 用户协议
     */
    USER_AGREEMENT(1, "用户协议"),
    /**
     * 隐私政策
     */
    PRIVACY_POLICY(2, "隐私政策");

    private final Integer type;

    private final String name;

    AgreementEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    /**
     * 根据类型获取枚举
     *
     * @param type 类型
     * @return 枚举
     */
    public static AgreementEnum getByType(Integer type) {
        for (AgreementEnum agreementEnum : AgreementEnum.values()) {
            if (agreementEnum.getType().equals(type)) {
                return agreementEnum;
            }
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

}
