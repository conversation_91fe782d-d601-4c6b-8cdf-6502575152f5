package com.hishop.wine.common.validator;

import cn.hutool.core.util.StrUtil;
import com.hishop.wine.common.annotation.XssClean;
import com.hishop.wine.common.utils.XssUtils;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * XSS 验证器
 * 对标记了 @XssClean 注解的字段进行 XSS 验证和清理
 *
 * @author: System
 * @date: 2025-01-05
 */
@Slf4j
public class XssValidator implements ConstraintValidator<XssClean, String> {

    private XssClean.Mode mode;
    private boolean allowEmpty;

    @Override
    public void initialize(XssClean constraintAnnotation) {
        this.mode = constraintAnnotation.mode();
        this.allowEmpty = constraintAnnotation.allowEmpty();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 空值处理
        if (StrUtil.isBlank(value)) {
            return allowEmpty;
        }

        try {
            // 根据不同模式进行验证
            switch (mode) {
                case RICH_TEXT:
                    return validateRichText(value, context);
                case TEXT_ONLY:
                    return validateTextOnly(value, context);
                case JSON:
                    return validateJson(value, context);
                case BASE64:
                    return validateBase64(value, context);
                default:
                    return validateRichText(value, context);
            }
        } catch (Exception e) {
            log.error("XSS 验证过程中发生异常", e);
            addCustomMessage(context, "内容验证失败");
            return false;
        }
    }

    /**
     * 验证富文本内容
     */
    private boolean validateRichText(String value, ConstraintValidatorContext context) {
        if (XssUtils.containsDangerousScript(value)) {
            addCustomMessage(context, "富文本内容包含危险脚本");
            return false;
        }
        return true;
    }

    /**
     * 验证纯文本内容
     */
    private boolean validateTextOnly(String value, ConstraintValidatorContext context) {
        if (value.contains("<") && value.contains(">")) {
            addCustomMessage(context, "纯文本字段不允许包含 HTML 标签");
            return false;
        }
        
        if (XssUtils.containsDangerousScript(value)) {
            addCustomMessage(context, "文本内容包含危险脚本");
            return false;
        }
        return true;
    }

    /**
     * 验证 JSON 内容
     */
    private boolean validateJson(String value, ConstraintValidatorContext context) {
        if (XssUtils.containsDangerousScript(value)) {
            addCustomMessage(context, "JSON 配置包含危险脚本");
            return false;
        }
        return true;
    }

    /**
     * 验证 Base64 内容
     */
    private boolean validateBase64(String value, ConstraintValidatorContext context) {
        try {
            // 解码 Base64 内容进行验证
            String decodedContent = cn.hutool.core.codec.Base64.decodeStr(value);
            if (XssUtils.containsDangerousScript(decodedContent)) {
                addCustomMessage(context, "Base64 内容包含危险脚本");
                return false;
            }
            return true;
        } catch (Exception e) {
            addCustomMessage(context, "Base64 内容格式错误");
            return false;
        }
    }

    /**
     * 添加自定义错误消息
     */
    private void addCustomMessage(ConstraintValidatorContext context, String message) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
    }
}
