package com.hishop.wine.common.aspect;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.github.binarywang.wxpay.service.WxPayService;
import com.hishop.common.util.HeaderUtil;
import com.hishop.common.util.SpelUtil;
import com.hishop.wine.common.annotation.SwitchWxMini;
import com.hishop.wine.common.config.WxAutoMapping;
import com.hishop.wine.common.config.WxAutoMappingConfig;
import lombok.AllArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;

/**
 * 切换微信小程序配置
 *
 * <AUTHOR>
 */
@Aspect
@Component
@AllArgsConstructor
public class SwitchWxMiniAspect {
    @Resource
    private WxMaService wxMaService;
    @Resource
    private WxPayService wxPayService;
    @Resource
    private WxAutoMappingConfig wxAutoMappingConfig;

    private static String DEFAULT_MODULE_CODE = "basic_system";

    private static String MODULE_BUSINESS_KEY = "moduleBusinessCode";

    @Around("@annotation(switchWxMini)")
    public Object around(ProceedingJoinPoint joinPoint, SwitchWxMini switchWxMini) throws Throwable {
        String moduleCode = getModuleCode(joinPoint, switchWxMini.value());
        moduleCode = StrUtil.isEmpty(moduleCode) ? DEFAULT_MODULE_CODE : moduleCode;

        // 宴席酒子系统有多个小程序，需要根据请求头中的moduleBusinessCode来切换
        if (moduleCode.equals(DEFAULT_MODULE_CODE)) {
            String businessKey = HeaderUtil.getHeader(MODULE_BUSINESS_KEY);
            if (StrUtil.isNotEmpty(businessKey)) {
                moduleCode = businessKey;
            }
        }

        WxAutoMapping wxAutoMapping = wxAutoMappingConfig.getWxAutoMapping(moduleCode);
        Assert.isTrue(ObjectUtil.isNotNull(wxAutoMapping), "未找到对应的微信配置");

        if (switchWxMini.switchMini()) {
            wxMaService.switchover(wxAutoMapping.getAppId());
        }
        if (switchWxMini.switchPay()) {
            wxPayService.switchover(wxAutoMapping.getMchId());
        }
        return joinPoint.proceed();
    }

    /**
     * 获取moduleCode
     *
     * @param joinPoint
     * @param key
     * @return appId
     */
    private String getModuleCode(ProceedingJoinPoint joinPoint, String key) {
        // 从注解上提取
        if (StrUtil.isNotEmpty(key)) {
            Signature signature = joinPoint.getSignature();
            MethodSignature methodSignature = (MethodSignature) signature;
            Method targetMethod = methodSignature.getMethod();
            Object target = joinPoint.getTarget();
            Object[] arguments = joinPoint.getArgs();
            return SpelUtil.parse(target, key, targetMethod, arguments);
        }
        // 从请求头提取moduleCode 转换
        else {
            return HeaderUtil.getModuleCode();
        }
    }
}
