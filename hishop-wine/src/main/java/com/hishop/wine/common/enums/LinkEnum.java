package com.hishop.wine.common.enums;

import com.hishop.wine.enums.ModuleEnums;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 链接枚举
 *
 * <AUTHOR>
 * @date : 2023/7/21
 */
public class LinkEnum {


    /**
     * 链接类型
     */
    public enum LinkType {

        /**
         * 装修组件
         */
        DECORATE(),

        /**
         * 底部导航
         */
        NAVIGATION();

    }

    /**
     * 目标类型
     */
    public enum TargetType {

        /**
         * 系统小程序
         */
        SYSTEM_MINI_APP("SYSTEM_MINI_APP", "系统小程序", Collections.EMPTY_LIST),

        /**
         * 其他小程序
         */
        OTHER_MINI_APP("OTHER_MINI_APP", "其他小程序", Arrays.asList(LinkType.DECORATE, LinkType.NAVIGATION)),

        /**
         * 公众号文字
         */
        OFFICIAL_ACCOUNT("OFFICIAL_ACCOUNT", "公众号文章", Arrays.asList(LinkType.DECORATE)),

        /**
         * 微页面
         */
        MICRO_PAGE("MICRO_PAGE", "微页面", Arrays.asList(LinkType.DECORATE, LinkType.NAVIGATION));

        /**
         * 链接类型
         */
        @Getter
        private final String type;
        /**
         * 类型名称
         */
        @Getter
        private final String name;

        /**
         * 支持的链接类型
         */
        @Getter
        private final List<LinkType> supportTypes;

        TargetType(String type, String name, List<LinkType> supportTypes) {
            this.type = type;
            this.name = name;
            this.supportTypes = supportTypes;
        }
    }

    /**
     * tab枚举
     */
    @Getter
    public enum Tab {

        /**
         * 功能页面
         */
        FUNCTION_PAGES("FUNCTION_PAGES", "功能页面", Collections.emptyList(), Collections.emptyList()),

        /**
         * 抽奖活动(目前只有积分商城支持，调整为粉丝俱乐部)
         */
        EXCHANGE_ACTIVITY("EXCHANGE_ACTIVITY", "抽奖活动", Arrays.asList(ModuleEnums.fans_club.name()), Arrays.asList(LinkType.DECORATE));


        /**
         * tab类型
         */
        private final String code;

        /**
         * tab名称
         */
        private final String name;

        /**
         * 支持的模块编码 如果为空则表示全部支持
         */
        private final List<String> supportModuleCodes;

        /**
         * 支持的链接类型 如果为空则标识全部支持
         */
        private final List<LinkType> supportTypes;

        Tab(String code, String name, List<String> supportModuleCodes, List<LinkType> supportTypes) {
            this.code = code;
            this.name = name;
            this.supportModuleCodes = supportModuleCodes;
            this.supportTypes = supportTypes;
        }
    }

}
