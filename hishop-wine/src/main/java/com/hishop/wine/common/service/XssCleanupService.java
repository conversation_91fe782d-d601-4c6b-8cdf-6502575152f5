package com.hishop.wine.common.service;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.hishop.wine.biz.BasicSettingBiz;
import com.hishop.wine.common.config.XssConfig;
import com.hishop.wine.common.utils.XssUtils;
import com.hishop.wine.enums.BasicSettingEnum;
import com.hishop.wine.model.vo.setting.ProtocolSettingVO;
import com.hishop.wine.model.vo.setting.SystemSettingVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * XSS 数据清理服务
 * 用于清理已存储的数据中的 XSS 攻击代码
 *
 * @author: System
 * @date: 2025-01-05
 */
@Slf4j
@Service
public class XssCleanupService {

    @Resource
    private BasicSettingBiz basicSettingBiz;

    @Resource
    private XssConfig xssConfig;

    /**
     * 清理所有配置数据中的 XSS 内容
     * 注意：此方法会修改数据库中的数据，请谨慎使用
     */
    public void cleanupAllSettings() {
        if (!xssConfig.isEnabled()) {
            log.info("XSS 防护未启用，跳过数据清理");
            return;
        }

        log.info("开始清理所有配置数据中的 XSS 内容");

        try {
            // 清理协议设置
            cleanupProtocolSettings();
            
            // 清理系统设置
            cleanupSystemSettings();
            
            log.info("XSS 数据清理完成");
        } catch (Exception e) {
            log.error("XSS 数据清理过程中发生异常", e);
        }
    }

    /**
     * 清理协议设置中的 XSS 内容
     */
    public void cleanupProtocolSettings() {
        try {
            log.info("开始清理协议设置中的 XSS 内容");
            
            ProtocolSettingVO protocolSetting = basicSettingBiz.getSetting(BasicSettingEnum.PROTOCOL_SETTING);
            if (protocolSetting == null) {
                log.info("协议设置为空，跳过清理");
                return;
            }

            boolean needUpdate = false;

            // 清理用户协议
            if (StrUtil.isNotBlank(protocolSetting.getUserAgreement())) {
                String decodedUserAgreement = Base64.decodeStr(protocolSetting.getUserAgreement());
                String cleanedUserAgreement = XssUtils.cleanRichText(decodedUserAgreement);
                
                if (!decodedUserAgreement.equals(cleanedUserAgreement)) {
                    protocolSetting.setUserAgreement(Base64.encode(cleanedUserAgreement));
                    needUpdate = true;
                    log.info("用户协议中发现并清理了 XSS 内容");
                }
            }

            // 清理隐私政策
            if (StrUtil.isNotBlank(protocolSetting.getPrivacyPolicy())) {
                String decodedPrivacyPolicy = Base64.decodeStr(protocolSetting.getPrivacyPolicy());
                String cleanedPrivacyPolicy = XssUtils.cleanRichText(decodedPrivacyPolicy);
                
                if (!decodedPrivacyPolicy.equals(cleanedPrivacyPolicy)) {
                    protocolSetting.setPrivacyPolicy(Base64.encode(cleanedPrivacyPolicy));
                    needUpdate = true;
                    log.info("隐私政策中发现并清理了 XSS 内容");
                }
            }

            // 如果有更新，保存到数据库
            if (needUpdate) {
                basicSettingBiz.saveSetting(BasicSettingEnum.PROTOCOL_SETTING, JSONUtil.toJsonStr(protocolSetting));
                log.info("协议设置 XSS 清理完成并已保存");
            } else {
                log.info("协议设置中未发现 XSS 内容");
            }

        } catch (Exception e) {
            log.error("清理协议设置时发生异常", e);
        }
    }

    /**
     * 清理系统设置中的 XSS 内容
     */
    public void cleanupSystemSettings() {
        try {
            log.info("开始清理系统设置中的 XSS 内容");
            
            SystemSettingVO systemSetting = basicSettingBiz.getSetting(BasicSettingEnum.SYSTEM_SETTING);
            if (systemSetting == null) {
                log.info("系统设置为空，跳过清理");
                return;
            }

            boolean needUpdate = false;

            // 清理系统名称
            if (StrUtil.isNotBlank(systemSetting.getSystemName())) {
                String cleanedSystemName = XssUtils.cleanText(systemSetting.getSystemName());
                if (!systemSetting.getSystemName().equals(cleanedSystemName)) {
                    systemSetting.setSystemName(cleanedSystemName);
                    needUpdate = true;
                    log.info("系统名称中发现并清理了 XSS 内容");
                }
            }

            // 清理用户协议（如果存在）
            if (StrUtil.isNotBlank(systemSetting.getUserAgreement())) {
                String cleanedUserAgreement = XssUtils.cleanRichText(systemSetting.getUserAgreement());
                if (!systemSetting.getUserAgreement().equals(cleanedUserAgreement)) {
                    systemSetting.setUserAgreement(cleanedUserAgreement);
                    needUpdate = true;
                    log.info("系统设置中的用户协议发现并清理了 XSS 内容");
                }
            }

            // 清理隐私政策（如果存在）
            if (StrUtil.isNotBlank(systemSetting.getPrivacyPolicy())) {
                String cleanedPrivacyPolicy = XssUtils.cleanRichText(systemSetting.getPrivacyPolicy());
                if (!systemSetting.getPrivacyPolicy().equals(cleanedPrivacyPolicy)) {
                    systemSetting.setPrivacyPolicy(cleanedPrivacyPolicy);
                    needUpdate = true;
                    log.info("系统设置中的隐私政策发现并清理了 XSS 内容");
                }
            }

            // 如果有更新，保存到数据库
            if (needUpdate) {
                basicSettingBiz.saveSetting(BasicSettingEnum.SYSTEM_SETTING, JSONUtil.toJsonStr(systemSetting));
                log.info("系统设置 XSS 清理完成并已保存");
            } else {
                log.info("系统设置中未发现 XSS 内容");
            }

        } catch (Exception e) {
            log.error("清理系统设置时发生异常", e);
        }
    }

    /**
     * 验证指定配置是否包含 XSS 内容
     *
     * @param settingEnum 配置枚举
     * @return 是否包含 XSS 内容
     */
    public boolean containsXssContent(BasicSettingEnum settingEnum) {
        try {
            Object setting = basicSettingBiz.getSetting(settingEnum);
            if (setting == null) {
                return false;
            }

            String jsonStr = JSONUtil.toJsonStr(setting);
            return XssUtils.containsDangerousScript(jsonStr);
        } catch (Exception e) {
            log.error("验证配置 XSS 内容时发生异常: {}", settingEnum, e);
            return false;
        }
    }

    /**
     * 获取 XSS 清理统计信息
     *
     * @return 清理统计信息
     */
    public String getCleanupStatistics() {
        StringBuilder stats = StringBuilder();
        stats.append("XSS 防护状态: ").append(xssConfig.isEnabled() ? "启用" : "禁用").append("\n");
        stats.append("日志记录: ").append(xssConfig.isLogEnabled() ? "启用" : "禁用").append("\n");
        stats.append("排除路径数量: ").append(xssConfig.getExcludeUrls().size()).append("\n");
        stats.append("严格检查字段数量: ").append(xssConfig.getStrictFields().size()).append("\n");
        
        // 检查各个配置是否包含 XSS 内容
        for (BasicSettingEnum settingEnum : BasicSettingEnum.values()) {
            try {
                boolean hasXss = containsXssContent(settingEnum);
                stats.append(settingEnum.name()).append(": ").append(hasXss ? "包含XSS" : "安全").append("\n");
            } catch (Exception e) {
                stats.append(settingEnum.name()).append(": 检查失败\n");
            }
        }
        
        return stats.toString();
    }
}
