package com.hishop.wine.common.enums;

import lombok.Getter;

/**
 * @description: 门店面积来源类型
 * @author: chenzw
 * @date: 2024/7/6 15:37
 */
@Getter
public enum TerminateSquare {

    /**
     * 100m²以下
     */
    SMALL_SCALE(1, "100m²以下"),

    /**
     * 100-500m²
     */
    MEDIUM_SCALE(2, "100-500m²"),

    /**
     * 500-1000m²
     */
    MEDIUM_LARGE_SCALE(3, "500-1000m²"),

    /**
     * 1000m²以上
     */
    LARGE_SCALE(4, "1000m²以上"),
    ;

    private final Integer type;

    private final String desc;

    TerminateSquare(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static TerminateSquare getTerminateSquareEnum(Integer type) {
        for (TerminateSquare terminateSquare : TerminateSquare.values()) {
            if (terminateSquare.getType().equals(type)) {
                return terminateSquare;
            }
        }
        return null;
    }

    public static TerminateSquare getTerminateSquareEnumByDesc(String desc) {
        for (TerminateSquare terminateSquare : TerminateSquare.values()) {
            if (terminateSquare.getDesc().equals(desc)) {
                return terminateSquare;
            }
        }
        return null;
    }
}
