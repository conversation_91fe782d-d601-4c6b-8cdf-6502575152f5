package com.hishop.wine.common.annotation;

import java.lang.annotation.*;

/**
 * 切换微信小程序配置
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SwitchWxMini {

    /**
     * 指定切换的moduleCode
     */
    String value() default "";

    /**
     * 是否切换小程序配置 默认true
     */
    boolean switchMini() default true;
    /**
     * 是否切换微信支付配置
     *
     */
    boolean switchPay() default false;

}
