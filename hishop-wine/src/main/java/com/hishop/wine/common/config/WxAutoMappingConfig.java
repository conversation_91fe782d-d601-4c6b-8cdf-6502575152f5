package com.hishop.wine.common.config;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.pojo.entity.BaseEntity;
import com.hishop.setting.AbsRefreshSetting;
import com.hishop.wine.constants.SettingConstants;
import com.hishop.wine.enums.TransactionEnum;
import com.hishop.wine.repository.dto.module.ModuleWxParamDTO;
import com.hishop.wine.repository.entity.ModuleBusiness;
import com.hishop.wine.repository.service.ModuleBusinessService;
import com.hishop.wine.repository.service.ModuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 微信相关配置映射
 *
 * <AUTHOR>
 * @date : 2023/7/3
 */
@Configuration
@Slf4j
public class WxAutoMappingConfig extends AbsRefreshSetting {

    private Map<String, WxAutoMapping> wxAutoMappingMap;

    @Resource
    private ModuleService moduleService;

    @Resource
    private ModuleBusinessService moduleBusinessService;

    @Override
    public void init() {
        log.info("【初始化微信配置】start");
        this.wxAutoMappingMap = getMapping();
        log.info("【初始化微信配置】success, config: {}", wxAutoMappingMap);
    }

    @Override
    public void refresh() {
        log.info("【更新微信配置】start");
        this.wxAutoMappingMap = getMapping();
        log.info("【更新微信配置】success, config: {}", wxAutoMappingMap);
    }

    @Override
    public String getSettingName() {
        return SettingConstants.WX_MAPPING_SETTING;
    }

    public WxAutoMapping getWxAutoMapping(String moduleCode) {
        return wxAutoMappingMap.get(moduleCode);
    }

    public WxAutoMapping getWxAutoMappingByAppId(String appId) {
        Set<String> keys = wxAutoMappingMap.keySet();
        return keys.stream().map(key -> wxAutoMappingMap.get(key))
                .filter(wxAutoMapping -> wxAutoMapping.getAppId().equals(appId)).findFirst().orElse(null);
    }

    private Map<String, WxAutoMapping> getMapping() {
        List<ModuleWxParamDTO> moduleWxParamDTOS = moduleService.listModuleWxParam();
        Map<String, WxAutoMapping> mapping = new HashMap<>(moduleWxParamDTOS.size());
        moduleWxParamDTOS.forEach(relate -> {
            WxAutoMapping wxAutoMapping = BeanUtil.copyProperties(relate, WxAutoMapping.class);

            // 如果是微信支付，且支持服务商支付，则将支付类型改为服务商支付
            if (TransactionEnum.MethodEnum.WX_PAY == TransactionEnum.MethodEnum.getEnum(wxAutoMapping.getPaymentType())
                    && wxAutoMapping.getIsSupportSec() != null
                    && wxAutoMapping.getIsSupportSec()) {
                wxAutoMapping.setPaymentType(TransactionEnum.MethodEnum.WX_SEC_PAY.name());
            }
            mapping.put(relate.getModuleCode(), wxAutoMapping);
        });

        List<ModuleBusiness> moduleBusinessList = moduleBusinessService.list(new LambdaQueryWrapper<ModuleBusiness>().eq(BaseEntity::getIzDelete, false));

        if (CollectionUtils.isNotEmpty(moduleBusinessList)) {
            moduleBusinessList.forEach(moduleBusiness -> {
                WxAutoMapping wxAutoMapping = BeanUtil.copyProperties(moduleBusiness, WxAutoMapping.class);
                mapping.putIfAbsent(moduleBusiness.getModuleBusinessCode(), wxAutoMapping);
            });
        }

        return mapping;
    }
}
