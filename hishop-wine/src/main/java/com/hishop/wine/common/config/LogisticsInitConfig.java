package com.hishop.wine.common.config;

import cn.hutool.extra.spring.SpringUtil;
import com.hishop.logistics.api.LogisticsClient;
import com.hishop.logistics.syy.SyyLogisticsClient;
import com.hishop.setting.AbsRefreshSetting;
import com.hishop.sms.api.SmsSender;
import com.hishop.sms.syy.SyySmsSender;
import com.hishop.wine.biz.BasicSettingBiz;
import com.hishop.wine.enums.BasicSettingEnum;
import com.hishop.wine.model.vo.logistics.LogisticsSettingVO;
import com.hishop.wine.model.vo.setting.SmsSettingVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


/**
 * 短信配置
 *
 * <AUTHOR>
 * @date : 2023/7/12
 */
@Configuration
@Slf4j
public class LogisticsInitConfig extends AbsRefreshSetting {

    @Resource
    private BasicSettingBiz basicSettingBiz;

    @Bean
    public LogisticsClient logisticsClient() {
        log.info("【初始化物流配置】start");
        LogisticsSettingVO setting = basicSettingBiz.getSetting(BasicSettingEnum.LOGISTICS_SETTING);
        LogisticsClient client = new SyyLogisticsClient(setting.getAppKey(), setting.getAppSecret());
        log.info("【初始化物流配置】success, config: {}", setting);
        return client;
    }

    @Override
    public void refresh() {
        log.info("【更新物流配置】start");
        LogisticsSettingVO setting = basicSettingBiz.getSetting(BasicSettingEnum.LOGISTICS_SETTING);
        Map<String, String> config = new HashMap<>(2);
        config.put("appKey", setting.getAppKey());
        config.put("appSecret", setting.getAppSecret());
        SpringUtil.getBean(LogisticsClient.class).updateConfig(config);
        log.info("【更新物流配置】success, config: {}", setting);
    }

    @Override
    public String getSettingName() {
        return BasicSettingEnum.LOGISTICS_SETTING.name();
    }
}
