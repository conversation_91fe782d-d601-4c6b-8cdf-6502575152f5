package com.hishop.wine.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * @description: 资源状态
 * @author: tomliu
 * @create: 2022/06/23 10:55
 **/
@RequiredArgsConstructor
public enum MaterialStatus {

    /**
     * 上传中
     */
    UPLOADING(0),

    /**
     * 正常
     */
    NORMAL(1),

    /**
     * 转码中
     */
    TRANSCODING(2),

    /**
     * 转码失败
     */
    ERROR(3)
    ;

    /**
     * 资源状态值
     */
    @EnumValue
    @Getter
    private final Integer value;
}
