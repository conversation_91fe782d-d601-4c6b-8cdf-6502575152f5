package com.hishop.wine.common.enums;

import com.hishop.common.export.model.BizType;
import lombok.Getter;

/**
 * 文件导入类型
 * <AUTHOR>
 * @description: 文件导入类型
 * @date: 2024/7/5 14:17
 */
@Getter
public enum FileImportType implements BizType {

    /**
     * 经销商导入
     */
    DEALER_IMPORT("DEALER", "经销商导入"),
    /**
     * 门店导入
     */
    TERMINATE_IMPORT("TERMINATE", "门店导入"),
    /**
     * 物流编码导入
     */
    LOGISTICS_CODE_IMPORT("LOGISTICS_CODE", "物流编码导入"),
    /**
     * 扫码营销物流编码导入
     */
    LOGISTICS_CODE_SCAN_IMPORT("LOGISTICS_CODE_SCAN", "扫码营销物流编码导入"),

    ;

    private final String type;

    private final String desc;

    FileImportType(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static FileImportType getFileImportTypeEnum(String type) {
        for (FileImportType fileImportType : FileImportType.values()) {
            if (fileImportType.getType().equals(type)) {
                return fileImportType;
            }
        }
        return null;
    }

    @Override
    public String type() {
        return type;
    }

    @Override
    public String desc() {
        return desc;
    }
}
