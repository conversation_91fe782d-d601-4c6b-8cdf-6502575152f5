package com.hishop.wine.common.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.owasp.html.HtmlPolicyBuilder;
import org.owasp.html.PolicyFactory;

import java.util.Map;
import java.util.regex.Pattern;

/**
 * XSS 防护工具类
 * 提供对用户输入内容的 XSS 攻击防护
 *
 * @author: System
 * @date: 2025-01-05
 */
@Slf4j
public class XssUtils {

    /**
     * 基础 HTML 标签白名单策略 - 用于富文本内容
     */
    private static final PolicyFactory RICH_TEXT_POLICY = new HtmlPolicyBuilder()
            // 允许的基础标签
            .allowElements("p", "br", "div", "span", "h1", "h2", "h3", "h4", "h5", "h6")
            // 允许的文本格式标签
            .allowElements("b", "i", "u", "strong", "em", "mark", "small", "del", "ins", "sub", "sup")
            // 允许的列表标签
            .allowElements("ul", "ol", "li")
            // 允许的链接标签（限制属性）
            .allowElements("a").allowAttributes("href", "title").onElements("a")
            // 允许的图片标签（限制属性）
            .allowElements("img").allowAttributes("src", "alt", "title", "width", "height").onElements("img")
            // 允许的表格标签
            .allowElements("table", "thead", "tbody", "tr", "td", "th")
            // 允许的样式属性（限制范围）
            .allowAttributes("style").matching(SAFE_STYLE_PATTERN).globally()
            // 允许的通用属性
            .allowAttributes("class", "id").globally()
            .toFactory();

    /**
     * 严格策略 - 仅允许纯文本，移除所有 HTML 标签
     */
    private static final PolicyFactory STRICT_POLICY = new HtmlPolicyBuilder().toFactory();

    /**
     * JSON 安全策略 - 用于 JSON 配置内容
     */
    private static final PolicyFactory JSON_POLICY = new HtmlPolicyBuilder()
            .allowTextIn("script").allowTextIn("style") // 允许在特定标签内的文本
            .toFactory();

    /**
     * 安全的 CSS 样式模式
     */
    private static final Pattern SAFE_STYLE_PATTERN = Pattern.compile(
            "(?:(?:color|background-color|font-size|font-weight|text-align|margin|padding|border)\\s*:\\s*[a-zA-Z0-9\\s#%.-]+;?\\s*)*"
    );

    /**
     * 危险脚本模式检测
     */
    private static final Pattern DANGEROUS_SCRIPT_PATTERN = Pattern.compile(
            "(?i)(<script[^>]*>.*?</script>|javascript:|vbscript:|onload=|onerror=|onclick=|onmouseover=|onfocus=|onblur=)",
            Pattern.DOTALL
    );

    /**
     * 清理富文本内容中的 XSS 攻击代码
     * 适用于用户协议、隐私政策等富文本内容
     *
     * @param input 输入的富文本内容
     * @return 清理后的安全内容
     */
    public static String cleanRichText(String input) {
        if (StrUtil.isBlank(input)) {
            return input;
        }

        try {
            // 使用富文本策略清理内容
            String cleaned = RICH_TEXT_POLICY.sanitize(input);
            
            // 记录清理操作（仅在内容发生变化时）
            if (!input.equals(cleaned)) {
                log.warn("XSS 内容已被清理，原始长度: {}, 清理后长度: {}", input.length(), cleaned.length());
            }
            
            return cleaned;
        } catch (Exception e) {
            log.error("XSS 清理过程中发生异常", e);
            // 异常情况下使用严格策略
            return STRICT_POLICY.sanitize(input);
        }
    }

    /**
     * 清理纯文本内容，移除所有 HTML 标签
     * 适用于标题、名称等纯文本字段
     *
     * @param input 输入的文本内容
     * @return 清理后的纯文本
     */
    public static String cleanText(String input) {
        if (StrUtil.isBlank(input)) {
            return input;
        }

        try {
            return STRICT_POLICY.sanitize(input);
        } catch (Exception e) {
            log.error("文本清理过程中发生异常", e);
            return StrUtil.EMPTY;
        }
    }

    /**
     * 清理 JSON 配置内容
     * 对 JSON 字符串中的值进行 XSS 清理，保持 JSON 结构完整
     *
     * @param jsonStr JSON 字符串
     * @return 清理后的 JSON 字符串
     */
    public static String cleanJsonString(String jsonStr) {
        if (StrUtil.isBlank(jsonStr)) {
            return jsonStr;
        }

        try {
            // 首先验证是否为有效的 JSON
            if (!JSONUtil.isJson(jsonStr)) {
                log.warn("输入内容不是有效的 JSON 格式，进行文本清理");
                return cleanText(jsonStr);
            }

            // 解析 JSON 并递归清理值
            Object jsonObj = JSONUtil.parse(jsonStr);
            Object cleanedObj = cleanJsonObject(jsonObj);
            
            return JSONUtil.toJsonStr(cleanedObj);
        } catch (Exception e) {
            log.error("JSON 清理过程中发生异常", e);
            return cleanText(jsonStr);
        }
    }

    /**
     * 递归清理 JSON 对象中的值
     *
     * @param obj JSON 对象
     * @return 清理后的对象
     */
    @SuppressWarnings("unchecked")
    private static Object cleanJsonObject(Object obj) {
        if (obj == null) {
            return null;
        }

        if (obj instanceof String) {
            String str = (String) obj;
            // 检查是否包含危险脚本
            if (containsDangerousScript(str)) {
                return cleanText(str);
            }
            // 对于可能包含 HTML 的字符串进行清理
            if (str.contains("<") && str.contains(">")) {
                return cleanRichText(str);
            }
            return str;
        }

        if (obj instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) obj;
            map.replaceAll((key, value) -> cleanJsonObject(value));
            return map;
        }

        if (obj instanceof Iterable) {
            // 处理数组和列表
            return obj; // 简化处理，实际项目中可以进一步优化
        }

        return obj;
    }

    /**
     * 检查内容是否包含危险脚本
     *
     * @param content 待检查的内容
     * @return 是否包含危险脚本
     */
    public static boolean containsDangerousScript(String content) {
        if (StrUtil.isBlank(content)) {
            return false;
        }
        return DANGEROUS_SCRIPT_PATTERN.matcher(content).find();
    }

    /**
     * 验证内容是否安全（不包含 XSS 攻击代码）
     *
     * @param content 待验证的内容
     * @return 是否安全
     */
    public static boolean isSafeContent(String content) {
        if (StrUtil.isBlank(content)) {
            return true;
        }

        // 检查是否包含危险脚本
        if (containsDangerousScript(content)) {
            return false;
        }

        // 比较清理前后的内容是否一致
        String cleaned = cleanRichText(content);
        return content.equals(cleaned);
    }

    /**
     * 对 Base64 编码的内容进行 XSS 清理
     * 适用于用户协议、隐私政策等 Base64 存储的富文本内容
     *
     * @param base64Content Base64 编码的内容
     * @return 清理后重新编码的 Base64 内容
     */
    public static String cleanBase64Content(String base64Content) {
        if (StrUtil.isBlank(base64Content)) {
            return base64Content;
        }

        try {
            // 解码 Base64 内容
            String decodedContent = cn.hutool.core.codec.Base64.decodeStr(base64Content);
            
            // 清理 XSS 内容
            String cleanedContent = cleanRichText(decodedContent);
            
            // 重新编码为 Base64
            return cn.hutool.core.codec.Base64.encode(cleanedContent);
        } catch (Exception e) {
            log.error("Base64 内容清理过程中发生异常", e);
            return base64Content;
        }
    }
}
