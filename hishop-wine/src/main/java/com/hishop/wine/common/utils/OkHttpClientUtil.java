package com.hishop.wine.common.utils;

import com.alibaba.fastjson.JSON;
import com.hishop.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2024/05/06/ $
 * @description:
 */
@Slf4j
public class OkHttpClientUtil {
    private static final OkHttpClient CLIENT = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .build();

    public static String doGet(String url) throws IOException {
        log.info("【" + url + "】发起请求");
        Request request = new Request.Builder()
                .url(url)
                .build();
        try (Response response = CLIENT.newCall(request).execute()) {
            String data = response.body().string();
            log.info("【" + url + "】发起请求成功，返回data:" + data);
            return data;
        } catch (Exception e) {
            log.error("【" + url + "】http请求异常:" + e.getMessage());
            new BusinessException("http请求异常:" + e.getMessage());
        }
        return null;
    }
    public static String doPost(String url, Map<String, String> headers, String jsonBody) throws IOException {
        log.info("【" + url + "】发起请求，headers:"+JSON.toJSONString(headers)+"，jsonBody:" + jsonBody);
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), jsonBody);
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body);
        // 添加自定义 header
        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                requestBuilder.addHeader(entry.getKey(), entry.getValue());
            }
        }
        Request request = requestBuilder.build();
        try (Response response = CLIENT.newCall(request).execute()) {
            String data = response.body().string();
            log.info("【" + url + "】发起请求成功，返回data:" + data);
            return data;
        } catch (Exception e) {
            log.error("【" + url + "】http请求异常:" + e.getMessage());
            new BusinessException("http请求异常:" + e.getMessage());
        }
        return null;
    }

    public static String doPostFormData(String url, Map<String, String> headers, Map<String, String> paramMap) throws IOException {
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(1, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .readTimeout(10, TimeUnit.SECONDS)
                .build();
        log.info("【" + url + "】发起请求，headers:" + JSON.toJSONString(headers) + ";Body:" + JSON.toJSONString(paramMap));
        // 创建请求体
        FormBody.Builder formBuilder = new FormBody.Builder();
        for (Map.Entry<String, String> entry : paramMap.entrySet()) {
            formBuilder.add(entry.getKey(), entry.getValue());
        }
        RequestBody formBody = formBuilder.build();
        // 创建请求
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(formBody);
        // 添加头部信息
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            requestBuilder.addHeader(entry.getKey(), entry.getValue());
        }
        Request request = requestBuilder.build();
        try (Response response = client.newCall(request).execute()) {
            String data = response.body().string();
            log.info("【" + url + "】发起请求成功，返回data:" + data);
            return data;
        } catch (Exception e) {
            log.error("【" + url + "】http请求异常:" + e.getMessage());
            new BusinessException("http请求异常:" + e.getMessage());
        }
        return null;
    }
}
