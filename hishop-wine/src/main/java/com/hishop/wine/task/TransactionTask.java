package com.hishop.wine.task;

import com.hishop.common.annotation.RejectRepeat;
import com.hishop.wine.biz.TransactionBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 交易定时任务
 *
 * <AUTHOR>
 * @date : 2023/9/19
 */
@Component
@Slf4j
public class TransactionTask {

    @Resource
    private TransactionBiz transactionBiz;

    /**
     * 查询打款到零钱交易状态
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    @RejectRepeat
    public void checkEntPayResultTask() {
        transactionBiz.checkEntPayResult();
    }

}
