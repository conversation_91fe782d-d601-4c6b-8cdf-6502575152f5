package com.hishop.wine.task;

import com.hishop.common.annotation.RejectRepeat;
import com.hishop.wine.biz.ContentModerationBiz;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 内容审核定时任务
 *
 * <AUTHOR>
 * @date : 2023/9/11
 */
@Component
@Slf4j
public class ModerationTask {

    @Resource
    private ContentModerationBiz contentModerationBiz;

    /**
     * 发起内容审核
     */
    @Scheduled(cron = "0/20 * * * * ?")
    @RejectRepeat
    public void launchModeration() {
        contentModerationBiz.launchModeration();
    }

    /**
     * 检查异步审核结果
     */
    @Scheduled(cron = "0/30 * * * * ?")
    @RejectRepeat
    public void checkAsyncModeration() {
        contentModerationBiz.queryModerationResult();
    }
}
