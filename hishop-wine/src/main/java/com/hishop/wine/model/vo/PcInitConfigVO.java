package com.hishop.wine.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * PC网站初始化返回对象
 * @author: chenpeng
 * @date: 2023-08-12
 */

@Data
@ApiModel(value = "PCInitConfigVO", description = "PC网站初始化返回对象")
public class PcInitConfigVO {

    @ApiModelProperty(value = "站点名称")
    private String webName;

    @ApiModelProperty(value = "站点logo")
    private String webLogo;

    @ApiModelProperty(value = "obs域名路径前缀")
    private String obsPrefixUrl;

    @ApiModelProperty(value = "默认页面路径")
    private String defaultPagePath;

}
