package com.hishop.wine.model.po.logisticsCode;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 物流码管理表(LogisticsCode)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-08 11:51:56
 */
@Data
@ApiModel(value = "LogisticsCodePo", description = "物流码管理表")
public class LogisticsCodePo {
    
    @ApiModelProperty(value = "主键")
    private Long id;
    
    @ApiModelProperty(value = "一级物流编码")
    private String codeFirst;
    
    @ApiModelProperty(value = "二级物流编码")
    private String codeSecondary;
    
    @ApiModelProperty(value = "瓶内码")
    private String codeBottle;
    
    @ApiModelProperty(value = "产品编码")
    private String productCode;
    
    @ApiModelProperty(value = "物流码类型 0盒码 1箱码")
    private Integer codeType;
    
    @ApiModelProperty(value = "状态 0未使用 1已使用")
    private Integer status;

    @ApiModelProperty(value = "物流码类别 0普通物流码 1扫码营销物流码")
    private Integer codeCategory;

    @ApiModelProperty(value = "批次id")
    private Long fileImportId;
    
    
    
    
}
