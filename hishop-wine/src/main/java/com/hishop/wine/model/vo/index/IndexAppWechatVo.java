package com.hishop.wine.model.vo.index;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description: 首页应用和小程序
 * @author: chenzw
 * @date: 2024/6/25 17:47
 */
@Data
@ApiModel(value = "IndexAppRecommendVo", description = "首页应用和小程序")
public class IndexAppWechatVo {

    @ApiModelProperty(value = "更多推荐")
    private String moreUrl;

    @ApiModelProperty(value = "应用推荐列表")
    private List<AppRecommendVo> appRecommendList;

    @ApiModelProperty(value = "小程序列表")
    private List<IndexMiniAppVo> miniAppList;
}
