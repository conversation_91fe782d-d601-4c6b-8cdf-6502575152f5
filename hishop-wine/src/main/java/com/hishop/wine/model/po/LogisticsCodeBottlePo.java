package com.hishop.wine.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 瓶内码管理表(LogisticsCodeBottle)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-09 11:39:11
 */
@Data
@ApiModel(value = "LogisticsCodeBottlePo", description = "瓶内码管理表")
public class LogisticsCodeBottlePo {
    
    @ApiModelProperty(value = "主键")
    private Long id;
    
    @ApiModelProperty(value = "瓶内码")
    private String codeBottle;
    
    @ApiModelProperty(value = "物流码id")
    private Long logisticsCodeId;
    
    
    
    
    
    
}
