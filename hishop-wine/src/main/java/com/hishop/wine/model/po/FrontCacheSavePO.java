package com.hishop.wine.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @description: 前端缓存存储请求PO
 * @author: chenzw
 * @date: 2024/5/20 16:35
 */
@Data
@ApiModel(value = "FrontCacheSavePO", description = "前端缓存存储请求PO")
public class FrontCacheSavePO {

    @ApiModelProperty(value = "key")
    @NotBlank(message = "key不能为空")
    private String key;

    @ApiModelProperty(value = "缓存值")
    @NotBlank(message = "缓存值不能为空")
    private String value;
}
