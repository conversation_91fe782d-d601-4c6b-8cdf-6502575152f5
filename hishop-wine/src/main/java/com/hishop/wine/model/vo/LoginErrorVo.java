package com.hishop.wine.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "LoginErrorVo", description = "登录异常")
public class LoginErrorVo {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;
    /**
     * 异常类型 0微信号绑定其他手机 1手机号绑定其他微信
     */
    @ApiModelProperty(value = "异常类型 0微信号绑定其他手机 1手机号绑定其他微信")
    private Integer type;

    @ApiModelProperty(value = "异常类型显示")
    private String typeStr;
    /**
     * 创建者ID
     */
    @ApiModelProperty(value = "创建者ID")
    private Long createBy;
    /**
     * 更新者ID
     */
    @ApiModelProperty(value = "更新者ID")
    private Long updateBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 是否删除 0否 1是
     */
    @ApiModelProperty(value = "是否删除 0否 1是")
    private Boolean izDelete;

    @ApiModelProperty(value = "重置状态 0未重置 1已重置")
    private Boolean status;
}
