package com.hishop.wine.model.po.terminate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 门店状态更新请求入参
 *
 * <AUTHOR>
 * @since 2024-07-04 15:53:16
 */
@Data
@ApiModel(value = "TerminateUpdateStatusPo", description = "门店状态更新请求入参")
public class TerminateUpdateStatusPo {

    @ApiModelProperty(value = "门店ID列表")
    @NotEmpty(message = "门店ID列表不能为空")
    private List<Long> ids;

    @ApiModelProperty(value = "是否启用")
    @NotNull(message = "是否启用不能为空")
    private Boolean izEnable;
}
