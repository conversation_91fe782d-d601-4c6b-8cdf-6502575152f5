package com.hishop.wine.model.po.terminate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * @description: 门店类型请求入参
 * @author: chenzw
 * @date: 2024/7/6 14:52
 */
@Data
@ApiModel(value = "TerminateTypeSavePo", description = "门店类型请求入参")
public class TerminateTypeSavePo {

    @ApiModelProperty(value = "类型名称", required = true)
    @NotBlank(message = "类型名称不能为空")
    @Size(max = 10, message = "类型名称长度不能超过10")
    private String name;

    @ApiModelProperty(value = "id")
    private Long id;
}
