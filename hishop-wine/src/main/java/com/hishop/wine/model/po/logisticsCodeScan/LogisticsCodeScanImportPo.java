package com.hishop.wine.model.po.logisticsCodeScan;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hishop.common.excel.read.RowReadResult;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 扫码营销物流码导入对象
 * <AUTHOR>
 * @since 2024-07-08 11:51:56
 */
@Data
@ApiModel(value = "LogisticsCodeScanImportPo", description = "扫码营销物流码导入对象")
public class LogisticsCodeScanImportPo extends RowReadResult {

    @ExcelProperty(value = "一级物流码")
    private String codeFirst;

    @ExcelProperty(value = "物流码类型")
    private String codeTypeStr;

    @ExcelProperty(value = "二级物流码")
    private String codeSecondary;
    
    @ExcelProperty(value = "商品编码")
    private String productCode;

    @ExcelProperty(value = "瓶内码")
    private String codeBottle;

    @ExcelIgnore

    private Integer status;

    @ExcelIgnore
    private Integer codeType;

    @ExcelIgnore
    private Integer codeCategory;

    @ExcelIgnore
    private Long fileImportId;
    
    @ExcelIgnore
    private Long productId;

}
