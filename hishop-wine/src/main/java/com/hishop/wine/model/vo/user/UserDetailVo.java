package com.hishop.wine.model.vo.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hishop.wine.model.vo.sale.SaleAreaSimpleVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @description: 用户详情返回对象
 * @author: chenzw
 * @date: 2024/7/6 13:44
 */
@Data
@ApiModel(value = "UserDetailVo", description = "用户详情返回对象")
public class UserDetailVo {

    @ApiModelProperty(value = "管理员id")
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "头像")
    private String icon;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "状态 true-正常 false-停用")
    private Boolean status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "角色id")
    private Long roleId;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "是否是部门负责人")
    private Boolean izHead;

    @ApiModelProperty(value = "部门id")
    private Long departmentId;

    @ApiModelProperty(value = "是否业务员")
    private Boolean izBusinessUser;

    @ApiModelProperty(value = "销售区域列表")
    private List<List<SaleAreaSimpleVo>> saleAreaList;

}
