package com.hishop.wine.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * @Description: 部门表 查询入参对象
 * @Author: chenpeng
 * @since: 2023-04-25 10:50:00
 */

@Data
@ApiModel(value = "DepartmentQueryPO", description = "部门表查询入参对象")
public class DepartmentQueryPO {

    @ApiModelProperty(name = "departmentName" , value = "部门名称")
	private String departmentName;

    @ApiModelProperty(name = "parentId" , value = "上级部门ID")
	private Long parentId;

}
