package com.hishop.wine.model.po.terminate;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.PhoneUtil;
import com.hishop.wine.common.enums.TerminateSquare;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 小程序门店保存请求入参
 * <AUTHOR>
 * @since 2024-07-11 14:47:43
 */
@Data
@ApiModel(value = "TerminateMinSavePo", description = "小程序门店保存请求入参")
public class TerminateMinSavePo {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "门店编码")
    @NotBlank(message = "门店编码不能为空")
    @Size(max = 20, message = "门店编码长度不能超过20")
    private String code;

    @ApiModelProperty(value = "门店名称")
    @NotBlank(message = "门店名称不能为空")
    @Size(max = 50, message = "门店名称长度不能超过50")
    private String name;

    @ApiModelProperty(value = "类型id")
    private Long terminateTypeId;

    @ApiModelProperty(value = "业务员")
    private Long businessUserId;

    @ApiModelProperty(value = "省份ID")
    private Integer provinceId;

    @ApiModelProperty(value = "城市ID")
    private Integer cityId;

    @ApiModelProperty(value = "区县ID")
    private Integer districtId;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "负责人姓名")
    @NotBlank(message = "负责人姓名不能为空")
    @Size(max = 20, message = "负责人姓名长度不能超过20")
    private String dutyName;

    @ApiModelProperty(value = "手机号")
    @NotBlank(message = "手机号不能为空")
    @Size(max = 20, message = "手机号长度不能超过20")
    private String phone;

    @ApiModelProperty(value = "门店面积")
    private TerminateSquare terminateSquare;

    public void validate() {
        if(!PhoneUtil.isMobile(phone)) {
            Assert.isTrue(false, "手机号格式不正确");
        }
    }
}
