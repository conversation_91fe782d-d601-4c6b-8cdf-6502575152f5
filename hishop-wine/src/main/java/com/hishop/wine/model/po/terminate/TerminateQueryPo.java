package com.hishop.wine.model.po.terminate;

import com.hishop.common.pojo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 门店查询入参对象
 * @author: chenzw
 * @date: 2024-07-04
 */
@Data
@ApiModel(value = "TerminateQueryPo", description = "门店查询入参对象")
public class TerminateQueryPo extends PageParam {

    @ApiModelProperty(value = "搜索关键字。支持负责人姓名或手机号码")
	private String dutyNameOrPhone;

    @ApiModelProperty(value = "搜索关键字。支持门店编码或名称")
    private String terminateCodeOrName;

    @ApiModelProperty(value = "省份ID")
	private Integer provinceId;

    @ApiModelProperty(value = "城市ID")
	private Integer cityId;

    @ApiModelProperty(value = "区县ID")
	private Integer districtId;

    @ApiModelProperty(value = "业务员")
    private Long businessUserId;

    @ApiModelProperty(value = "业务员")
    private Long dealerId;

    @ApiModelProperty(value = "状态")
    private Boolean izEnable;

    @ApiModelProperty(value = "门店审核，传true表示查询门店审核功能")
    private Boolean izAudit;

    @ApiModelProperty(value = "来源",hidden = true)
    private Integer source;

    @ApiModelProperty(value = "审核状态",hidden = true)
    private List<Integer> auditStatusList;
}
