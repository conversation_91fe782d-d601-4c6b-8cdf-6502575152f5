package com.hishop.wine.model.po.logisticsCode;

import com.hishop.common.pojo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * 物流码 查询入参对象
 * @author: guoyufeng
 * @date: 2024-07-09
 */

@Data
@ApiModel(value = "LogisticsCodeQueryPo", description = "物流码查询入参对象")
public class LogisticsCodeQueryPo extends PageParam {
	
    @ApiModelProperty(value = "物流编码")
	private String logisticsCode;

    @ApiModelProperty(value = "状态 传空查全部，0 未使用 1已使用")
	private Integer status;

    @ApiModelProperty("开始时间")
    private String startTime;

    @ApiModelProperty("结束时间")
    private String endTime;

    @ApiModelProperty("批次名称")
    private String name;

    @ApiModelProperty("导入类型 传空查全部， 0 物流码 1扫码营销物流码")
    private Integer codeCategory;

}
