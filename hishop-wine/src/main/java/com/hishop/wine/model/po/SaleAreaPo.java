package com.hishop.wine.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 销售区域(SaleArea)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-04 09:08:04
 */
@Data
@ApiModel(value = "SaleAreaPo", description = "销售区域")
public class SaleAreaPo {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "销售维度id")
    private Long saleDimId;

    @ApiModelProperty(value = "名称")
    private String areaName;

    @ApiModelProperty(value = "编码")
    private String areaCode;

    @ApiModelProperty(value = "父级id")
    private Long parentId;

    @ApiModelProperty(value = "完整编码")
    private String fullCode;
}
