package com.hishop.wine.model.vo.terminate;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description: 门店类型返回对象
 * @author: chenzw
 * @date: 2024/7/6 14:56
 */
@Data
@ApiModel(value = "TerminateTypeVo", description = "门店类型返回对象")
public class TerminateTypeVo {

    @ApiModelProperty(value = "类型id")
    private Long id;

    @ApiModelProperty(value = "类型名称")
    private String name;

    @ApiModelProperty(value = "门店数量")
    private Integer terminateNumber;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}
