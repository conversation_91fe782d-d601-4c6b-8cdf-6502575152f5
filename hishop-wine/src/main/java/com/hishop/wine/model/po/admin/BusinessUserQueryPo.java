package com.hishop.wine.model.po.admin;

import com.hishop.common.pojo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 业务员查询请求参数
 * @author: chenzw
 * @date: 2024/7/8 09:15
 */
@Data
@ApiModel(value = "BusinessUserQueryPo", description = "业务员查询请求参数")
public class BusinessUserQueryPo extends PageParam {

    @ApiModelProperty(value = "业务员姓名")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String mobile;
}
