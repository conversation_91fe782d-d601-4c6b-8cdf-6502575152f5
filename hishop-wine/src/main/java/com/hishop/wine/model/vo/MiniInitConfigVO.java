package com.hishop.wine.model.vo;

import com.hishop.wine.model.vo.module.ModuleBusinessAppVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 小程序初始化信息返回对象
 * @author: LiGuoQiang
 * @date: 2023-06-26
 */

@Data
@ApiModel(value = "MiniConfigVO", description = "商城装修表返回对象")
public class MiniInitConfigVO {

    @ApiModelProperty(value = "站点名称")
    private String webName;

    @ApiModelProperty(value = "站点logo")
    private String webLogo;

    @ApiModelProperty(value = "obs域名路径前缀")
    private String obsPrefixUrl;

    @ApiModelProperty(value = "小程序首页使用的装修模板")
    private String homePageModuleCode;

    @ApiModelProperty(value = "小程序主题JSON配置")
    private String themeJson;

    @ApiModelProperty(value = "小程序底部导航JSON配置")
    private String navigationJson;

    @ApiModelProperty(value = "积分名称")
    private String pointsName;

    @ApiModelProperty(value = "前端h5项目域名路径前缀")
    private String h5ProjectDomain;

    @ApiModelProperty(value = "业务模块编码列表")
    private String moduleBusinessCode;

    @ApiModelProperty(value = "系统业务模块小程序列表")
    private List<ModuleBusinessAppVo> moduleBusinessAppList;
}
