package com.hishop.wine.model.vo.fileImport;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hishop.common.util.serializer.ObjectToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 导出记录表 返回对象
 *
 * @author: LiGuoQiang
 * @date: 2023-07-12
 */

@Data
@ApiModel(value = "FileImportRecordVo", description = "导出记录表返回对象")
public class FileImportRecordVo {

    @ApiModelProperty(value = "主键id")
    @JsonSerialize(using = ObjectToStringSerializer.class)
    private Long id;

    @ApiModelProperty(value="名称")
    private String name;

    @ApiModelProperty(value = "导入类型。DEALER：经销商；TERMINAL：终端；QRCODE：二维码； LOGISTICS_CODE:物流码； LOGISTICS_CODE_SCAN：扫码营销物流码")
    private String importType;

    @ApiModelProperty(value = "导入状态。1：导入中；2：导入成功；3：导入失败；4：部分失败；5：导入异常")
    private Integer importStatus;

    @ApiModelProperty(value = "导入状态。1：导入中；2：导入成功；3：导入失败；4：部分失败；5：导入异常")
    private String importStatusDesc;

    @ApiModelProperty(value = "导入成功数量")
    private Integer successCount;

    @ApiModelProperty(value = "导入失败数量")
    private Integer failCount;

    @ApiModelProperty(value = "导入文件名称")
    private String fileName;

    @ApiModelProperty(value = "异常文件下载地址")
    private String errFilePath;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "导入耗时")
    private Integer costTime;

    @ApiModelProperty(value = "创建人ID")
    private Long createBy;

    @ApiModelProperty(value = "创建人")
    private String createName;


}
