package com.hishop.wine.model.vo.sale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 销售区域(SaleArea)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-04 09:08:04
 */
@Data
@ApiModel(value = "SaleAreaVo", description = "销售区域")
public class SaleAreaVo {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "销售维度id")
    private Long saleDimId;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "父级id")
    private Long parentId;
}
