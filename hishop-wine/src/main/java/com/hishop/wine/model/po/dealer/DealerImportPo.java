package com.hishop.wine.model.po.dealer;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hishop.common.excel.read.RowReadResult;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**   
 * 经销商表 新增入参对象
 * @author: LiGuoQiang
 * @date: 2023-07-10
 */

@Data
@ApiModel(value = "DealerImportPo", description = "经销商表新增入参对象")
public class DealerImportPo extends RowReadResult {

    @ExcelProperty(value = "经销商编码")
    private String dealerCode;

    @ExcelProperty(value = "经销商名称")
    private String dealerName;

    @ExcelProperty(value = "经销商详细地址")
	private String region;

    @ExcelProperty(value = "所属业务员手机号")
    private String businessPhone;

    @ExcelProperty(value = "负责人姓名")
    private String dutyName;

    @ExcelProperty(value = "手机号")
    private String phone;

    @ExcelProperty(value = "邮箱")
    private String mail;

    /**
     * 所属业务员ID
     **/
    @ExcelIgnore
    private Long businessUserId;
    /**
     * 省份ID
     **/
    @ExcelIgnore
    private Integer provinceId;
    /**
     * 省份名称
     **/
    @ExcelIgnore
    private String provinceName;
    /**
     * 城市ID
     **/
    @ExcelIgnore
    private Integer cityId;
    /**
     * 城市名称
     **/
    @ExcelIgnore
    private String cityName;
    /**
     * 区县ID
     **/
    @ExcelIgnore
    private Integer districtId;
    /**
     * 区县名称
     **/
    @ExcelIgnore
    private String districtName;

    /**
     * 详细地址
     **/
    @ExcelIgnore
    private String address;
}
