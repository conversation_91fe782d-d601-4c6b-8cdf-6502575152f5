package com.hishop.wine.model.vo;

import com.hishop.wine.model.po.decorate.DecoratePO;
import com.hishop.wine.model.vo.decorate.DecorateVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**   
 * 积分商城初始化信息返回对象
 * @author: LiGuoQiang
 * @date: 2023-06-26
 */

@Data
@ApiModel(value = "PointsMallConfigVO", description = "积分商城初始化信息返回对象")
public class PointsMallConfigVO {

    @ApiModelProperty(value = "装修列表信息")
    private List<DecorateVO> decorateList;


}
