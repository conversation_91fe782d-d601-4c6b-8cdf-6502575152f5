package com.hishop.wine.model.po;

import com.hishop.common.pojo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "LoginErrorPO", description = "登录异常")
public class LoginErrorPO {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "异常类型 0微信号绑定其他手机 1手机号绑定其他微信")
    private Integer type;

    @ApiModelProperty(value = "appId")
    private String appId;

    @ApiModelProperty(value = "openId")
    private String openId;


}
