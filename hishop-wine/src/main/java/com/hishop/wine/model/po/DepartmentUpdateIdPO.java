package com.hishop.wine.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**   
 * @Description: 部门表 更新入参对象
 * @Author: chenpeng
 * @since: 2023-04-25 10:50:00
 */

@Data
@ApiModel(value = "DepartmentUpdateIdPO", description = "部门表更新id入参对象")
public class DepartmentUpdateIdPO {

    @NotEmpty(message = "请选择用户")
    @ApiModelProperty(name = "userIds" , value = "用户Id")
	private List<Long> userIds;

    @NotNull(message = "请选择部门")
    @ApiModelProperty(name = "departmentId" , value = "部门Id")
	private Long departmentId;

    @ApiModelProperty(name = "parentId" , value = "上级部门ID")
	private Long parentId;

    @ApiModelProperty(name = "izHead" , value = "是否部门负责人",hidden = true)
    private Boolean izHead;
}
