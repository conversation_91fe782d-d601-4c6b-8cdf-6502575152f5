package com.hishop.wine.model.po.terminate;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hishop.common.excel.read.RowReadResult;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**   
 * 门店导入入参对象
 * @author: chenzw
 * @date: 2024-07-08
 */

@Data
@ApiModel(value = "TerminateImportPo", description = "门店导入入参对象")
public class TerminateImportPo extends RowReadResult {

    @ExcelProperty(value = "门店编号")
    private String code;

    @ExcelProperty(value = "门店名称")
    private String name;

    @ExcelProperty(value = "门店类型")
	private String type;

    @ExcelProperty(value = "门店面积")
    private String squareDesc;

    @ExcelProperty(value = "所属业务员手机号")
    private String businessPhone;

    @ExcelProperty(value = "所属经销商手机号")
    private String dealerPhone;

    @ExcelProperty(value = "门店地址")
    private String region;

    @ExcelProperty(value = "负责人姓名")
    private String dutyName;

    @ExcelProperty(value = "负责人手机号")
    private String phone;

    @ExcelProperty(value = "电子邮箱")
    private String mail;


    /**
     * 门店面积
     **/
    @ExcelIgnore
    private Integer square;

    /**
     * 类型ID
     */
    @ExcelIgnore
    private Long terminateTypeId;

    /**
     * 所属业务员ID
     **/
    @ExcelIgnore
    private Long businessUserId;

    /**
     * 所属经销商ID
     **/
    @ExcelIgnore
    private Long dealerId;

    /**
     * 省份ID
     **/
    @ExcelIgnore
    private Integer provinceId;

    /**
     * 省份名称
     **/
    @ExcelIgnore
    private String provinceName;

    /**
     * 城市ID
     **/
    @ExcelIgnore
    private Integer cityId;
    /**
     * 城市名称
     **/
    @ExcelIgnore
    private String cityName;
    /**
     * 区县ID
     **/
    @ExcelIgnore
    private Integer districtId;
    /**
     * 区县名称
     **/
    @ExcelIgnore
    private String districtName;

    /**
     * 详细地址
     **/
    @ExcelIgnore
    private String address;
}
