package com.hishop.wine.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description: 销售区域新增请求参数
 * @author: chenzw
 * @date: 2024/7/4 09:33
 */
@Data
@ApiModel(value = "SaleAreaAddPo", description = "销售区域新增请求")
public class SaleAreaAddPo {

    @ApiModelProperty(value = "销售维度id")
    @NotNull(message = "销售维度id不能为空")
    private Long saleDimId;

    @ApiModelProperty(value = "上级销售区域id，顶级传0")
    @NotNull(message = "上级销售区域id不能为空")
    private Long parentId;

    @ApiModelProperty(value = "名称列表")
    @NotEmpty(message = "名称列表不能为空")
    private List<String> name;
}
