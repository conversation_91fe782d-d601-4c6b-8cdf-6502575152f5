package com.hishop.wine.model.vo.sale;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 销售维度划分(SaleDim)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-04 09:07:46
 */
@Data
@ApiModel(value = "SaleDimVo", description = "销售维度划分")
public class SaleDimVo {

    @ApiModelProperty(value = "主键id")
    private Integer id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "级别")
    private Integer level;
}
