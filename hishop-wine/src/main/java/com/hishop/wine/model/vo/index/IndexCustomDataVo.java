package com.hishop.wine.model.vo.index;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * @description: 首页客户数据返回对象
 * @author: chenzw
 * @date: 2024/6/26 14:59
 */
@Data
@Builder
@AllArgsConstructor
@ApiModel(value = "IndexCustomDataVo", description = "首页客户数据返回对象")
public class IndexCustomDataVo {

    @ApiModelProperty(value = "消费者数量")
    private Long userCount;

    @ApiModelProperty(value = "经销商数量")
    private Long dealerCount;

    @ApiModelProperty(value = "门店数量")
    private Long terminateCount;
}
