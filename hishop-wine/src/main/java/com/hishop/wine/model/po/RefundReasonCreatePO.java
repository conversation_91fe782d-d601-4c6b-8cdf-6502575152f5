package com.hishop.wine.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**   
 * 退款原因表新增入参对象
 * @author: chenpeng
 * @date: 2023-07-11
 */

@Data
@ApiModel(value = "RefundReasonCreatePO", description = "退款原因表新增入参对象")
public class RefundReasonCreatePO {


    @ApiModelProperty(value = "退款原因id")
    private Long id;

    @ApiModelProperty(value = "售后类型（1：仅退款，2：退货退款，3：退款）")
    @NotNull(message = "售后类型不能为空")
    private List<Integer> refundTypes;

    @ApiModelProperty(value = "售后原因")
    @NotEmpty(message = "售后原因不能为空")
    private String refundReason;

}
