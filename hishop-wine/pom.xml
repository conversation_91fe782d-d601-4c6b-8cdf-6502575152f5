<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jiukeduo-basic-system</artifactId>
        <groupId>com.hishop</groupId>
        <version>1.0.3-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <artifactId>hishop-wine</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.hishop</groupId>
            <artifactId>hishop-api-wine</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.hishop</groupId>
            <artifactId>hishop-weixin-wxmini</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop</groupId>
            <artifactId>hishop-weixin-pay</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop</groupId>
            <artifactId>hishop-weixin-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop</groupId>
            <artifactId>hishop-service</artifactId>
        </dependency>

        <!-- 华为云OBS，需要在基础配置中排除json包 -->
        <dependency>
            <groupId>com.hishop</groupId>
            <artifactId>hishop-nfs-obs-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop</groupId>
            <artifactId>hishop-mq-rocketmq-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop</groupId>
            <artifactId>hishop-export</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop</groupId>
            <artifactId>hishop-email-sender</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop</groupId>
            <artifactId>hishop-sms-syy-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop</groupId>
            <artifactId>hishop-logistics-syy</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop</groupId>
            <artifactId>hishop-log</artifactId>
        </dependency>

        <!-- XSS 防护依赖 -->
        <dependency>
            <groupId>com.googlecode.owasp-java-html-sanitizer</groupId>
            <artifactId>owasp-java-html-sanitizer</artifactId>
            <version>20220608.1</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>